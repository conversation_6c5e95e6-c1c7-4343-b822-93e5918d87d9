<div class="mobile-layout" 
     [class.menu-open]="isMenuOpen"
     [class.search-open]="isSearchOpen"
     [class.keyboard-open]="isKeyboardOpen">

  <!-- Mobile Header -->
  <header *ngIf="showHeader" class="mobile-header">
    <div class="header-content">
      <!-- <PERSON>u <PERSON> -->
      <button class="header-btn menu-btn" (click)="toggleMenu()">
        <i class="fas fa-bars" *ngIf="!isMenuOpen"></i>
        <i class="fas fa-times" *ngIf="isMenuOpen"></i>
      </button>

      <!-- Logo -->
      <div class="header-logo" routerLink="/">
        <img src="assets/images/logo.svg" alt="DFashion" class="logo-image">
        <span class="logo-text">DFashion</span>
      </div>

      <!-- Header Actions -->
      <div class="header-actions">
        <!-- Search Button -->
        <button class="header-btn search-btn" (click)="toggleSearch()">
          <i class="fas fa-search"></i>
        </button>

        <!-- Cart <PERSON>ton -->
        <button class="header-btn cart-btn" routerLink="/cart">
          <i class="fas fa-shopping-cart"></i>
          <span *ngIf="cartCount > 0" class="badge">{{ formatCount(cartCount) }}</span>
        </button>

        <!-- Wishlist Button -->
        <button class="header-btn wishlist-btn" routerLink="/wishlist">
          <i class="fas fa-heart"></i>
          <span *ngIf="wishlistCount > 0" class="badge">{{ formatCount(wishlistCount) }}</span>
        </button>
      </div>
    </div>

    <!-- Mobile Search Bar -->
    <div class="mobile-search" [class.active]="isSearchOpen">
      <div class="search-container">
        <input 
          type="text" 
          class="mobile-search-input"
          [(ngModel)]="searchQuery"
          (keyup.enter)="onSearchSubmit()"
          placeholder="Search for products, brands...">
        <button class="search-submit-btn" (click)="onSearchSubmit()">
          <i class="fas fa-search"></i>
        </button>
        <button class="search-close-btn" (click)="toggleSearch()">
          <i class="fas fa-times"></i>
        </button>
      </div>
    </div>
  </header>

  <!-- Mobile Side Menu -->
  <div class="mobile-menu" [class.active]="isMenuOpen">
    <div class="menu-overlay" (click)="closeMenu()"></div>
    <div class="menu-content">
      <!-- User Profile Section -->
      <div class="menu-profile" *ngIf="currentUser">
        <div class="profile-avatar">
          <img [src]="currentUser.avatar || 'assets/images/default-avatar.svg'"
               [alt]="currentUser.fullName"
               (error)="onAvatarError($event)">
        </div>
        <div class="profile-info">
          <h3>{{ currentUser.fullName }}</h3>
          <p>{{ currentUser.email }}</p>
        </div>
      </div>

      <!-- Guest Section -->
      <div class="menu-guest" *ngIf="!currentUser">
        <div class="guest-avatar">
          <i class="fas fa-user"></i>
        </div>
        <div class="guest-info">
          <h3>Welcome to DFashion</h3>
          <p>Sign in for personalized experience</p>
        </div>
        <div class="guest-actions">
          <button class="btn-primary" routerLink="/login" (click)="closeMenu()">Sign In</button>
          <button class="btn-secondary" routerLink="/register" (click)="closeMenu()">Sign Up</button>
        </div>
      </div>

      <!-- Menu Items -->
      <nav class="menu-nav">
        <a routerLink="/" (click)="closeMenu()" class="menu-item" [class.active]="isCurrentRoute('/')">
          <i class="fas fa-home"></i>
          <span>Home</span>
        </a>
        
        <a routerLink="/categories" (click)="closeMenu()" class="menu-item" [class.active]="isCurrentRoute('/categories')">
          <i class="fas fa-th-large"></i>
          <span>Categories</span>
        </a>
        
        <a routerLink="/trending-now" (click)="closeMenu()" class="menu-item" [class.active]="isCurrentRoute('/trending-now')">
          <img src="assets/svg/trending-now.svg" alt="Trending Now" class="menu-icon">
          <span>Trending Now</span>
        </a>

        <a routerLink="/featured-brands" (click)="closeMenu()" class="menu-item" [class.active]="isCurrentRoute('/featured-brands')">
          <img src="assets/svg/featured-brands.svg" alt="Featured Brands" class="menu-icon">
          <span>Featured Brands</span>
        </a>

        <a routerLink="/new-arrivals" (click)="closeMenu()" class="menu-item" [class.active]="isCurrentRoute('/new-arrivals')">
          <img src="assets/svg/new-arrivals.svg" alt="New Arrivals" class="menu-icon">
          <span>New Arrivals</span>
        </a>

        <a routerLink="/trending" (click)="closeMenu()" class="menu-item" [class.active]="isCurrentRoute('/trending')">
          <img src="assets/svg/trending.svg" alt="Trending" class="menu-icon">
          <span>Trending</span>
        </a>
        
        <a routerLink="/offers" (click)="closeMenu()" class="menu-item" [class.active]="isCurrentRoute('/offers')">
          <i class="fas fa-percent"></i>
          <span>Offers</span>
        </a>

        <!-- Authenticated User Menu Items -->
        <div *ngIf="currentUser" class="menu-section">
          <div class="menu-divider"></div>
          
          <a routerLink="/profile" (click)="closeMenu()" class="menu-item">
            <i class="fas fa-user"></i>
            <span>My Profile</span>
          </a>
          
          <a routerLink="/orders" (click)="closeMenu()" class="menu-item">
            <i class="fas fa-box"></i>
            <span>My Orders</span>
          </a>
          
          <a routerLink="/wishlist" (click)="closeMenu()" class="menu-item">
            <i class="fas fa-heart"></i>
            <span>Wishlist</span>
            <span *ngIf="wishlistCount > 0" class="menu-badge">{{ formatCount(wishlistCount) }}</span>
          </a>
          
          <a routerLink="/cart" (click)="closeMenu()" class="menu-item">
            <i class="fas fa-shopping-cart"></i>
            <span>Cart</span>
            <span *ngIf="cartCount > 0" class="menu-badge">{{ formatCount(cartCount) }}</span>
          </a>
          
          <a routerLink="/settings" (click)="closeMenu()" class="menu-item">
            <i class="fas fa-cog"></i>
            <span>Settings</span>
          </a>
          
          <div class="menu-divider"></div>
          
          <button class="menu-item logout-btn" (click)="logout()">
            <i class="fas fa-sign-out-alt"></i>
            <span>Logout</span>
          </button>
        </div>
      </nav>

      <!-- App Info -->
      <div class="menu-footer">
        <div class="app-info">
          <p>DFashion v1.0</p>
          <p>© 2024 All rights reserved</p>
        </div>
        <div class="social-links">
          <a href="#" class="social-link">
            <i class="fab fa-instagram"></i>
          </a>
          <a href="#" class="social-link">
            <i class="fab fa-facebook"></i>
          </a>
          <a href="#" class="social-link">
            <i class="fab fa-twitter"></i>
          </a>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <main class="mobile-main">
    <ng-content></ng-content>
  </main>

  <!-- Mobile Bottom Navigation -->
  <nav *ngIf="showBottomNav && !isKeyboardOpen" class="mobile-bottom-nav">
    <a routerLink="/" class="nav-item" [class.active]="isCurrentRoute('/')">
      <i class="fas fa-home"></i>
      <span>Home</span>
    </a>
    
    <a routerLink="/categories" class="nav-item" [class.active]="isCurrentRoute('/categories')">
      <i class="fas fa-th-large"></i>
      <span>Shop</span>
    </a>
    
    <button class="nav-item create-nav" (click)="toggleCreateMenu()" [class.active]="isCreateMenuOpen">
      <div class="create-btn">
        <i class="fas fa-plus"></i>
      </div>
      <span>Create</span>
    </button>
    
    <a routerLink="/wishlist" class="nav-item" [class.active]="isCurrentRoute('/wishlist')">
      <i class="fas fa-heart"></i>
      <span>Wishlist</span>
      <span *ngIf="wishlistCount > 0" class="nav-badge">{{ formatCount(wishlistCount) }}</span>
    </a>
    
    <a routerLink="/profile" class="nav-item profile-nav" [class.active]="isCurrentRoute('/profile')">
      <div class="profile-avatar" [style.background-image]="'url(' + getCurrentUserAvatar() + ')'">
        <i class="fas fa-user" *ngIf="!getCurrentUserAvatar()"></i>
      </div>
      <span>Profile</span>
    </a>
  </nav>

  <!-- Create Menu Popup -->
  <div class="create-menu-overlay" [class.active]="isCreateMenuOpen" (click)="closeCreateMenu()">
    <div class="create-menu" [class.active]="isCreateMenuOpen" (click)="$event.stopPropagation()">
      <div class="create-menu-header">
        <h3>Create New</h3>
        <button class="close-btn" (click)="closeCreateMenu()">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <div class="create-options">
        <button class="create-option" (click)="createReel()">
          <div class="option-icon">
            <img src="assets/svg/create-reel.svg" alt="Create Reel">
          </div>
          <div class="option-content">
            <h4>Reel</h4>
            <p>Create a short video</p>
          </div>
        </button>

        <button class="create-option" (click)="createStory()">
          <div class="option-icon">
            <img src="assets/svg/create-story.svg" alt="Create Story">
          </div>
          <div class="option-content">
            <h4>Story</h4>
            <p>Share a moment</p>
          </div>
        </button>

        <button class="create-option" (click)="createPost()">
          <div class="option-icon">
            <img src="assets/svg/create-post.svg" alt="Create Post">
          </div>
          <div class="option-content">
            <h4>Post</h4>
            <p>Share a photo or video</p>
          </div>
        </button>
      </div>
    </div>
  </div>

  <!-- Mobile Footer -->
  <footer *ngIf="showFooter && !showBottomNav" class="mobile-footer">
    <div class="footer-content">
      <div class="footer-links">
        <a href="/about">About</a>
        <a href="/contact">Contact</a>
        <a href="/privacy">Privacy</a>
        <a href="/terms">Terms</a>
      </div>
      <div class="footer-copyright">
        <p>© 2024 DFashion. All rights reserved.</p>
      </div>
    </div>
  </footer>
</div>
