{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../../core/services/cart.service\";\nimport * as i3 from \"../../../../core/services/wishlist.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nconst _c0 = () => [1, 2, 3];\nfunction FeedComponent_div_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 9);\n    i0.ɵɵelement(2, \"div\", 10);\n    i0.ɵɵelementStart(3, \"div\", 11);\n    i0.ɵɵelement(4, \"div\", 12)(5, \"div\", 13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(6, \"div\", 14)(7, \"div\", 15);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FeedComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵtemplate(1, FeedComponent_div_1_div_1_Template, 8, 0, \"div\", 7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c0));\n  }\n}\nfunction FeedComponent_div_2_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"p\");\n    i0.ɵɵtext(2, \"No posts available. Loading posts...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction FeedComponent_div_2_article_2_span_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 52);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const post_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(post_r2.location);\n  }\n}\nfunction FeedComponent_div_2_article_2_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵelement(1, \"img\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const post_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", post_r2.mediaUrl, i0.ɵɵsanitizeUrl)(\"alt\", post_r2.content);\n  }\n}\nfunction FeedComponent_div_2_article_2_div_13_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61);\n    i0.ɵɵelement(1, \"i\", 62);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"Reel\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction FeedComponent_div_2_article_2_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 55)(1, \"video\", 56);\n    i0.ɵɵlistener(\"click\", function FeedComponent_div_2_article_2_div_13_Template_video_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r3 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r3.toggleVideoPlay($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"div\", 57)(3, \"button\", 58);\n    i0.ɵɵlistener(\"click\", function FeedComponent_div_2_article_2_div_13_Template_button_click_3_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r3 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r3.toggleVideoPlay($event));\n    });\n    i0.ɵɵelement(4, \"i\", 59);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(5, FeedComponent_div_2_article_2_div_13_div_5_Template, 4, 0, \"div\", 60);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const post_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", post_r2.mediaUrl, i0.ɵɵsanitizeUrl)(\"muted\", true)(\"loop\", true);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", post_r2.isReel);\n  }\n}\nfunction FeedComponent_div_2_article_2_div_14_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 65);\n    i0.ɵɵlistener(\"click\", function FeedComponent_div_2_article_2_div_14_button_1_Template_button_click_0_listener() {\n      const product_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r3.showProductDetails(product_r6));\n    });\n    i0.ɵɵelement(1, \"i\", 66);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FeedComponent_div_2_article_2_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵtemplate(1, FeedComponent_div_2_article_2_div_14_button_1_Template, 2, 0, \"button\", 64);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const post_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", post_r2.products);\n  }\n}\nfunction FeedComponent_div_2_article_2_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 67)(1, \"span\", 68);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const post_r2 = i0.ɵɵnextContext().$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.formatLikesCount(post_r2.likes));\n  }\n}\nfunction FeedComponent_div_2_article_2_div_31_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 71);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const hashtag_r7 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"#\", hashtag_r7, \"\");\n  }\n}\nfunction FeedComponent_div_2_article_2_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 69);\n    i0.ɵɵtemplate(1, FeedComponent_div_2_article_2_div_31_span_1_Template, 2, 1, \"span\", 70);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const post_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", post_r2.hashtags);\n  }\n}\nfunction FeedComponent_div_2_article_2_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 72)(1, \"button\", 73);\n    i0.ɵɵlistener(\"click\", function FeedComponent_div_2_article_2_div_32_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const post_r2 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.toggleComments(post_r2));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const post_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" View all \", post_r2.comments, \" comments \");\n  }\n}\nfunction FeedComponent_div_2_article_2_div_40_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 77);\n    i0.ɵɵelement(1, \"img\", 78);\n    i0.ɵɵelementStart(2, \"div\", 79)(3, \"span\", 80);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 81);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 82);\n    i0.ɵɵelement(8, \"img\", 78);\n    i0.ɵɵelementStart(9, \"div\", 79)(10, \"span\", 80);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 81);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"div\", 83)(15, \"button\", 84);\n    i0.ɵɵlistener(\"click\", function FeedComponent_div_2_article_2_div_40_div_2_Template_button_click_15_listener() {\n      const product_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r3.viewProduct(product_r10));\n    });\n    i0.ɵɵtext(16, \"Shop\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"button\", 85);\n    i0.ɵɵlistener(\"click\", function FeedComponent_div_2_article_2_div_40_div_2_Template_button_click_17_listener() {\n      const product_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r3.addToCart(product_r10));\n    });\n    i0.ɵɵelement(18, \"i\", 86);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"button\", 87);\n    i0.ɵɵlistener(\"click\", function FeedComponent_div_2_article_2_div_40_div_2_Template_button_click_19_listener() {\n      const product_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r3.addToWishlist(product_r10));\n    });\n    i0.ɵɵelement(20, \"i\", 88);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"button\", 89);\n    i0.ɵɵlistener(\"click\", function FeedComponent_div_2_article_2_div_40_div_2_Template_button_click_21_listener() {\n      const product_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r3.buyNow(product_r10));\n    });\n    i0.ɵɵelement(22, \"i\", 90);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const product_r10 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", product_r10.image, i0.ɵɵsanitizeUrl)(\"alt\", product_r10.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(product_r10.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.formatPrice(product_r10.price));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", product_r10.image, i0.ɵɵsanitizeUrl)(\"alt\", product_r10.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(product_r10.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.formatPrice(product_r10.price));\n  }\n}\nfunction FeedComponent_div_2_article_2_div_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 74)(1, \"div\", 75);\n    i0.ɵɵtemplate(2, FeedComponent_div_2_article_2_div_40_div_2_Template, 23, 8, \"div\", 76);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const post_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", post_r2.products.slice(0, 3));\n  }\n}\nfunction FeedComponent_div_2_article_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"article\", 20)(1, \"header\", 21)(2, \"div\", 22)(3, \"div\", 23);\n    i0.ɵɵelement(4, \"img\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 25)(6, \"h3\", 26);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, FeedComponent_div_2_article_2_span_8_Template, 2, 1, \"span\", 27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"button\", 28);\n    i0.ɵɵelement(10, \"i\", 29);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 30);\n    i0.ɵɵtemplate(12, FeedComponent_div_2_article_2_div_12_Template, 2, 2, \"div\", 31)(13, FeedComponent_div_2_article_2_div_13_Template, 6, 4, \"div\", 32)(14, FeedComponent_div_2_article_2_div_14_Template, 2, 1, \"div\", 33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 34)(16, \"div\", 35)(17, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function FeedComponent_div_2_article_2_Template_button_click_17_listener() {\n      const post_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.toggleLike(post_r2));\n    });\n    i0.ɵɵelement(18, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function FeedComponent_div_2_article_2_Template_button_click_19_listener() {\n      const post_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.focusCommentInput(post_r2));\n    });\n    i0.ɵɵelement(20, \"i\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function FeedComponent_div_2_article_2_Template_button_click_21_listener() {\n      const post_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.sharePost(post_r2));\n    });\n    i0.ɵɵelement(22, \"i\", 40);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function FeedComponent_div_2_article_2_Template_button_click_23_listener() {\n      const post_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.toggleSave(post_r2));\n    });\n    i0.ɵɵelement(24, \"i\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(25, FeedComponent_div_2_article_2_div_25_Template, 3, 1, \"div\", 42);\n    i0.ɵɵelementStart(26, \"div\", 43)(27, \"span\", 26);\n    i0.ɵɵtext(28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"span\", 44);\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(31, FeedComponent_div_2_article_2_div_31_Template, 2, 1, \"div\", 45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(32, FeedComponent_div_2_article_2_div_32_Template, 3, 1, \"div\", 46);\n    i0.ɵɵelementStart(33, \"div\", 47);\n    i0.ɵɵtext(34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"div\", 48)(36, \"input\", 49, 0);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function FeedComponent_div_2_article_2_Template_input_ngModelChange_36_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r3.newComment, $event) || (ctx_r3.newComment = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keyup.enter\", function FeedComponent_div_2_article_2_Template_input_keyup_enter_36_listener() {\n      const post_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.addComment(post_r2));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"button\", 50);\n    i0.ɵɵlistener(\"click\", function FeedComponent_div_2_article_2_Template_button_click_38_listener() {\n      const post_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.addComment(post_r2));\n    });\n    i0.ɵɵtext(39, \" Post \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(40, FeedComponent_div_2_article_2_div_40_Template, 3, 1, \"div\", 51);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const post_r2 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"src\", (post_r2.user == null ? null : post_r2.user.avatar) || \"assets/images/default-avatar.png\", i0.ɵɵsanitizeUrl)(\"alt\", (post_r2.user == null ? null : post_r2.user.fullName) || \"User\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate((post_r2.user == null ? null : post_r2.user.username) || \"Unknown User\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", post_r2.location);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", post_r2.mediaType === \"image\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", post_r2.mediaType === \"video\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", post_r2.products && post_r2.products.length > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"liked\", post_r2.isLiked);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(post_r2.isLiked ? \"fas fa-heart\" : \"far fa-heart\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"saved\", post_r2.isSaved);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(post_r2.isSaved ? \"fas fa-bookmark\" : \"far fa-bookmark\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", post_r2.likes > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate((post_r2.user == null ? null : post_r2.user.username) || \"Unknown User\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(post_r2.content);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", post_r2.hashtags && post_r2.hashtags.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", post_r2.comments > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getTimeAgo(post_r2.createdAt), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r3.newComment);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r3.newComment || !ctx_r3.newComment.trim());\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", post_r2.products && post_r2.products.length > 0);\n  }\n}\nfunction FeedComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16);\n    i0.ɵɵtemplate(1, FeedComponent_div_2_div_1_Template, 3, 0, \"div\", 17)(2, FeedComponent_div_2_article_2_Template, 41, 24, \"article\", 18);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.posts.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.posts)(\"ngForTrackBy\", ctx_r3.trackByPostId);\n  }\n}\nfunction FeedComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 91)(1, \"button\", 92);\n    i0.ɵɵlistener(\"click\", function FeedComponent_div_3_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.loadMorePosts());\n    });\n    i0.ɵɵtext(2, \" Load More Posts \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction FeedComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 93)(1, \"div\", 94);\n    i0.ɵɵelement(2, \"i\", 95);\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"Welcome to DFashion\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Follow fashion influencers to see their latest posts and discover trending styles!\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nexport let FeedComponent = /*#__PURE__*/(() => {\n  class FeedComponent {\n    constructor(router, cartService, wishlistService) {\n      this.router = router;\n      this.cartService = cartService;\n      this.wishlistService = wishlistService;\n      this.posts = [];\n      this.loading = true;\n      this.hasMore = true;\n      this.currentPage = 1;\n      this.newComment = '';\n    }\n    ngOnInit() {\n      this.loadPosts();\n    }\n    loadPosts() {\n      this.loading = true;\n      // Simulate loading with realistic Instagram-style posts\n      setTimeout(() => {\n        this.posts = this.getFallbackPosts();\n        this.loading = false;\n      }, 1000);\n    }\n    getFallbackPosts() {\n      return [{\n        _id: 'post-1',\n        user: {\n          _id: 'user-1',\n          username: 'ai_fashionista_maya',\n          fullName: 'Maya Chen',\n          avatar: 'assets/images/default-avatar.svg'\n        },\n        content: 'Sustainable fashion is the future! 🌱✨ This eco-friendly dress is made from recycled materials and looks absolutely stunning. #SustainableFashion #EcoFriendly #OOTD',\n        mediaUrl: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=400&h=600&fit=crop',\n        mediaType: 'image',\n        location: 'Mumbai, India',\n        likes: 1247,\n        comments: 89,\n        shares: 34,\n        isLiked: false,\n        isSaved: false,\n        isReel: false,\n        hashtags: ['SustainableFashion', 'EcoFriendly', 'OOTD'],\n        createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),\n        products: [{\n          _id: 'prod-1',\n          name: 'Eco-Friendly Summer Dress',\n          price: 2499,\n          image: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=400&h=600&fit=crop'\n        }]\n      }, {\n        _id: 'post-3',\n        user: {\n          _id: 'user-3',\n          username: 'ai_trendsetter_zara',\n          fullName: 'Zara Patel',\n          avatar: 'assets/images/default-avatar.svg'\n        },\n        content: 'Ethnic fusion at its finest! Traditional meets modern ✨ This kurti is perfect for any occasion.',\n        mediaUrl: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400&h=600&fit=crop',\n        mediaType: 'image',\n        location: 'Bangalore, India',\n        likes: 2156,\n        comments: 134,\n        shares: 67,\n        isLiked: false,\n        isSaved: true,\n        isReel: false,\n        hashtags: ['EthnicWear', 'Fusion', 'Traditional'],\n        createdAt: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(),\n        products: [{\n          _id: 'prod-3',\n          name: 'Designer Ethnic Kurti',\n          price: 1899,\n          image: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400&h=600&fit=crop'\n        }]\n      }];\n    }\n    loadMorePosts() {\n      this.currentPage++;\n      this.loadPosts();\n    }\n    trackByPostId(index, post) {\n      return post._id;\n    }\n    // Instagram-style Actions\n    toggleLike(post) {\n      post.isLiked = !post.isLiked;\n      post.likes += post.isLiked ? 1 : -1;\n    }\n    toggleSave(post) {\n      post.isSaved = !post.isSaved;\n    }\n    toggleComments(post) {\n      // Navigate to post detail or show comments modal\n      console.log('Toggle comments for post:', post._id);\n    }\n    sharePost(post) {\n      // Implement share functionality\n      console.log('Share post:', post._id);\n    }\n    addComment(post) {\n      if (this.newComment.trim()) {\n        post.comments += 1;\n        console.log('Add comment:', this.newComment, 'to post:', post._id);\n        this.newComment = '';\n      }\n    }\n    focusCommentInput(post) {\n      // Focus on comment input\n      console.log('Focus comment input for post:', post._id);\n    }\n    toggleVideoPlay(event) {\n      const video = event.target;\n      if (video.paused) {\n        video.play();\n      } else {\n        video.pause();\n      }\n    }\n    showProductDetails(product) {\n      console.log('Show product details:', product);\n    }\n    viewProduct(product) {\n      this.router.navigate(['/product', product._id]);\n    }\n    formatLikesCount(likes) {\n      if (likes === 1) return '1 like';\n      if (likes < 1000) return `${likes} likes`;\n      if (likes < 1000000) return `${(likes / 1000).toFixed(1)}K likes`;\n      return `${(likes / 1000000).toFixed(1)}M likes`;\n    }\n    formatPrice(price) {\n      return new Intl.NumberFormat('en-IN', {\n        style: 'currency',\n        currency: 'INR',\n        minimumFractionDigits: 0\n      }).format(price);\n    }\n    getTimeAgo(dateString) {\n      const now = new Date();\n      const date = new Date(dateString);\n      const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));\n      if (diffInMinutes < 1) return 'now';\n      if (diffInMinutes < 60) return `${diffInMinutes}m`;\n      const diffInHours = Math.floor(diffInMinutes / 60);\n      if (diffInHours < 24) return `${diffInHours}h`;\n      const diffInDays = Math.floor(diffInHours / 24);\n      if (diffInDays < 7) return `${diffInDays}d`;\n      const diffInWeeks = Math.floor(diffInDays / 7);\n      return `${diffInWeeks}w`;\n    }\n    // E-commerce Actions\n    addToCart(product) {\n      console.log('Adding to cart:', product);\n      this.cartService.addToCart(product._id, 1, undefined, undefined).subscribe({\n        next: response => {\n          if (response.success) {\n            alert('Product added to cart!');\n          } else {\n            alert('Failed to add product to cart');\n          }\n        },\n        error: error => {\n          console.error('Error adding to cart:', error);\n          alert('Error adding product to cart');\n        }\n      });\n    }\n    addToWishlist(product) {\n      console.log('Adding to wishlist:', product);\n      this.wishlistService.addToWishlist(product._id).subscribe({\n        next: response => {\n          if (response.success) {\n            alert('Product added to wishlist!');\n          } else {\n            alert('Failed to add product to wishlist');\n          }\n        },\n        error: error => {\n          console.error('Error adding to wishlist:', error);\n          alert('Error adding product to wishlist');\n        }\n      });\n    }\n    buyNow(product) {\n      console.log('Buying product:', product);\n      this.router.navigate(['/checkout'], {\n        queryParams: {\n          productId: product._id,\n          source: 'feed'\n        }\n      });\n    }\n    static {\n      this.ɵfac = function FeedComponent_Factory(t) {\n        return new (t || FeedComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.CartService), i0.ɵɵdirectiveInject(i3.WishlistService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: FeedComponent,\n        selectors: [[\"app-feed\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 5,\n        vars: 4,\n        consts: [[\"commentInput\", \"\"], [1, \"instagram-feed\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"feed-posts\", 4, \"ngIf\"], [\"class\", \"load-more\", 4, \"ngIf\"], [\"class\", \"empty-feed\", 4, \"ngIf\"], [1, \"loading-container\"], [\"class\", \"post-skeleton\", 4, \"ngFor\", \"ngForOf\"], [1, \"post-skeleton\"], [1, \"skeleton-header\"], [1, \"skeleton-avatar\"], [1, \"skeleton-user-info\"], [1, \"skeleton-username\"], [1, \"skeleton-time\"], [1, \"skeleton-image\"], [1, \"skeleton-actions\"], [1, \"feed-posts\"], [\"class\", \"no-posts-message\", 4, \"ngIf\"], [\"class\", \"instagram-post\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"no-posts-message\"], [1, \"instagram-post\"], [1, \"post-header\"], [1, \"user-info\"], [1, \"user-avatar-container\"], [1, \"user-avatar\", 3, \"src\", \"alt\"], [1, \"user-details\"], [1, \"username\"], [\"class\", \"post-location\", 4, \"ngIf\"], [1, \"post-options\"], [1, \"fas\", \"fa-ellipsis-h\"], [1, \"post-media-container\"], [\"class\", \"post-image-container\", 4, \"ngIf\"], [\"class\", \"post-video-container\", 4, \"ngIf\"], [\"class\", \"product-tags-overlay\", 4, \"ngIf\"], [1, \"post-actions\"], [1, \"primary-actions\"], [1, \"action-btn\", \"like-btn\", 3, \"click\"], [1, \"action-btn\", \"comment-btn\", 3, \"click\"], [1, \"far\", \"fa-comment\"], [1, \"action-btn\", \"share-btn\", 3, \"click\"], [1, \"far\", \"fa-paper-plane\"], [1, \"action-btn\", \"save-btn\", 3, \"click\"], [\"class\", \"likes-section\", 4, \"ngIf\"], [1, \"post-caption\"], [1, \"caption-text\"], [\"class\", \"hashtags\", 4, \"ngIf\"], [\"class\", \"comments-preview\", 4, \"ngIf\"], [1, \"post-time\"], [1, \"add-comment-section\"], [\"type\", \"text\", \"placeholder\", \"Add a comment...\", 1, \"comment-input\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [1, \"post-comment-btn\", 3, \"click\", \"disabled\"], [\"class\", \"ecommerce-section\", 4, \"ngIf\"], [1, \"post-location\"], [1, \"post-image-container\"], [1, \"post-image\", 3, \"src\", \"alt\"], [1, \"post-video-container\"], [1, \"post-video\", 3, \"click\", \"src\", \"muted\", \"loop\"], [1, \"video-overlay\"], [1, \"play-pause-btn\", 3, \"click\"], [1, \"fas\", \"fa-play\"], [\"class\", \"reel-indicator\", 4, \"ngIf\"], [1, \"reel-indicator\"], [1, \"fas\", \"fa-video\"], [1, \"product-tags-overlay\"], [\"class\", \"product-tag-btn\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"product-tag-btn\", 3, \"click\"], [1, \"fas\", \"fa-shopping-bag\"], [1, \"likes-section\"], [1, \"likes-count\"], [1, \"hashtags\"], [\"class\", \"hashtag\", 4, \"ngFor\", \"ngForOf\"], [1, \"hashtag\"], [1, \"comments-preview\"], [1, \"view-comments-btn\", 3, \"click\"], [1, \"ecommerce-section\"], [1, \"product-showcase\"], [\"class\", \"featured-product\", 4, \"ngFor\", \"ngForOf\"], [1, \"featured-product\"], [1, \"product-thumbnail\", 3, \"src\", \"alt\"], [1, \"product-details\"], [1, \"product-name\"], [1, \"product-price\"], [1, \"product-header\"], [1, \"product-actions\"], [1, \"shop-btn\", 3, \"click\"], [1, \"cart-btn\", 3, \"click\"], [1, \"fas\", \"fa-shopping-cart\"], [1, \"wishlist-btn\", 3, \"click\"], [1, \"fas\", \"fa-heart\"], [1, \"buy-btn\", 3, \"click\"], [1, \"fas\", \"fa-bolt\"], [1, \"load-more\"], [1, \"load-more-btn\", 3, \"click\"], [1, \"empty-feed\"], [1, \"empty-content\"], [1, \"fas\", \"fa-camera\"]],\n        template: function FeedComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 1);\n            i0.ɵɵtemplate(1, FeedComponent_div_1_Template, 2, 2, \"div\", 2)(2, FeedComponent_div_2_Template, 3, 3, \"div\", 3)(3, FeedComponent_div_3_Template, 3, 0, \"div\", 4)(4, FeedComponent_div_4_Template, 7, 0, \"div\", 5);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.hasMore && !ctx.loading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.posts.length === 0);\n          }\n        },\n        dependencies: [CommonModule, i4.NgForOf, i4.NgIf, FormsModule, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgModel],\n        styles: [\".instagram-feed[_ngcontent-%COMP%]{max-width:470px;margin:0 auto;background:#fafafa;min-height:100vh}@media (max-width: 768px){.instagram-feed[_ngcontent-%COMP%]{max-width:100%;background:#fff;padding:0;margin:0}}.loading-container[_ngcontent-%COMP%]{padding:0}.no-posts-message[_ngcontent-%COMP%]{text-align:center;padding:40px 20px;color:#666;background:#fff;border-radius:8px;margin:20px 0}.no-posts-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:16px}.post-skeleton[_ngcontent-%COMP%]{background:#fff;margin-bottom:12px;border:1px solid #dbdbdb;border-radius:8px;overflow:hidden}.skeleton-header[_ngcontent-%COMP%]{display:flex;align-items:center;padding:16px;gap:12px}.skeleton-avatar[_ngcontent-%COMP%]{width:32px;height:32px;border-radius:50%;background:linear-gradient(90deg,#f0f0f0 25%,#e0e0e0,#f0f0f0 75%);background-size:200% 100%;animation:_ngcontent-%COMP%_shimmer 1.5s infinite}.skeleton-user-info[_ngcontent-%COMP%]{flex:1}.skeleton-username[_ngcontent-%COMP%]{width:100px;height:12px;background:linear-gradient(90deg,#f0f0f0 25%,#e0e0e0,#f0f0f0 75%);background-size:200% 100%;animation:_ngcontent-%COMP%_shimmer 1.5s infinite;border-radius:4px;margin-bottom:6px}.skeleton-time[_ngcontent-%COMP%]{width:60px;height:10px;background:linear-gradient(90deg,#f0f0f0 25%,#e0e0e0,#f0f0f0 75%);background-size:200% 100%;animation:_ngcontent-%COMP%_shimmer 1.5s infinite;border-radius:4px}.skeleton-image[_ngcontent-%COMP%]{width:100%;height:400px;background:linear-gradient(90deg,#f0f0f0 25%,#e0e0e0,#f0f0f0 75%);background-size:200% 100%;animation:_ngcontent-%COMP%_shimmer 1.5s infinite}.skeleton-actions[_ngcontent-%COMP%]{height:60px;background:linear-gradient(90deg,#f0f0f0 25%,#e0e0e0,#f0f0f0 75%);background-size:200% 100%;animation:_ngcontent-%COMP%_shimmer 1.5s infinite}@keyframes _ngcontent-%COMP%_shimmer{0%{background-position:-200% 0}to{background-position:200% 0}}.feed-posts[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:12px}.instagram-post[_ngcontent-%COMP%]{background:#fff;border:1px solid #dbdbdb;border-radius:8px;overflow:visible}@media (max-width: 768px){.instagram-post[_ngcontent-%COMP%]{overflow:visible;min-height:auto}}.post-header[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;padding:16px}.user-info[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px}.user-avatar-container[_ngcontent-%COMP%]{position:relative}.user-avatar[_ngcontent-%COMP%]{width:32px;height:32px;border-radius:50%;object-fit:cover}.user-details[_ngcontent-%COMP%]{display:flex;flex-direction:column}.username[_ngcontent-%COMP%]{font-size:14px;font-weight:600;color:#262626;margin:0;line-height:1.2}.post-location[_ngcontent-%COMP%]{font-size:12px;color:#8e8e8e;line-height:1.2}.post-options[_ngcontent-%COMP%]{background:none;border:none;cursor:pointer;padding:8px;color:#262626}.post-options[_ngcontent-%COMP%]:hover{color:#8e8e8e}.post-media-container[_ngcontent-%COMP%]{position:relative;width:100%;max-height:600px;overflow:hidden}.post-image-container[_ngcontent-%COMP%], .post-video-container[_ngcontent-%COMP%]{width:100%;position:relative}.post-image[_ngcontent-%COMP%]{width:100%;height:auto;display:block}.post-video[_ngcontent-%COMP%]{width:100%;height:auto;display:block;max-height:600px;object-fit:cover}.video-overlay[_ngcontent-%COMP%]{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);opacity:0;transition:opacity .3s ease}.post-video-container[_ngcontent-%COMP%]:hover   .video-overlay[_ngcontent-%COMP%]{opacity:1}.play-pause-btn[_ngcontent-%COMP%]{width:60px;height:60px;border-radius:50%;background:#000000b3;border:none;color:#fff;font-size:20px;cursor:pointer;display:flex;align-items:center;justify-content:center}.reel-indicator[_ngcontent-%COMP%]{position:absolute;top:16px;left:16px;background:#000000b3;color:#fff;padding:6px 12px;border-radius:20px;font-size:12px;font-weight:600;display:flex;align-items:center;gap:6px}.product-tags-overlay[_ngcontent-%COMP%]{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%)}.product-tag-btn[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;background:#000000b3;border:2px solid white;color:#fff;cursor:pointer;display:flex;align-items:center;justify-content:center;font-size:16px;transition:all .3s ease}.product-tag-btn[_ngcontent-%COMP%]:hover{transform:scale(1.1);background:#000000e6}.post-actions[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;padding:8px 16px}.primary-actions[_ngcontent-%COMP%]{display:flex;gap:16px}.action-btn[_ngcontent-%COMP%]{background:none;border:none;cursor:pointer;padding:8px;font-size:24px;color:#262626;transition:all .2s ease}.action-btn[_ngcontent-%COMP%]:hover{color:#8e8e8e}.action-btn.liked[_ngcontent-%COMP%]{color:#ed4956;animation:_ngcontent-%COMP%_likeAnimation .3s ease}.action-btn.saved[_ngcontent-%COMP%]{color:#262626}@keyframes _ngcontent-%COMP%_likeAnimation{0%,to{transform:scale(1)}50%{transform:scale(1.2)}}.likes-section[_ngcontent-%COMP%]{padding:0 16px 8px}.likes-count[_ngcontent-%COMP%]{font-size:14px;font-weight:600;color:#262626}.post-caption[_ngcontent-%COMP%]{padding:0 16px 8px;font-size:14px;line-height:1.4}.post-caption[_ngcontent-%COMP%]   .username[_ngcontent-%COMP%]{font-weight:600;color:#262626;margin-right:8px}.post-caption[_ngcontent-%COMP%]   .caption-text[_ngcontent-%COMP%]{color:#262626}.hashtags[_ngcontent-%COMP%]{margin-top:8px}.hashtag[_ngcontent-%COMP%]{color:#00376b;margin-right:8px;cursor:pointer}.hashtag[_ngcontent-%COMP%]:hover{text-decoration:underline}.comments-preview[_ngcontent-%COMP%]{padding:0 16px 8px}.view-comments-btn[_ngcontent-%COMP%]{background:none;border:none;color:#8e8e8e;font-size:14px;cursor:pointer;padding:0}.view-comments-btn[_ngcontent-%COMP%]:hover{color:#262626}.post-time[_ngcontent-%COMP%]{padding:0 16px 8px;font-size:10px;color:#8e8e8e;text-transform:uppercase;letter-spacing:.2px}.add-comment-section[_ngcontent-%COMP%]{display:flex;align-items:center;padding:16px;border-top:1px solid #efefef;gap:12px}.comment-input[_ngcontent-%COMP%]{flex:1;border:none;outline:none;font-size:14px;color:#262626}.comment-input[_ngcontent-%COMP%]::placeholder{color:#8e8e8e}.post-comment-btn[_ngcontent-%COMP%]{background:none;border:none;color:#0095f6;font-size:14px;font-weight:600;cursor:pointer}.post-comment-btn[_ngcontent-%COMP%]:disabled{color:#b2dffc;cursor:not-allowed}.post-comment-btn[_ngcontent-%COMP%]:hover:not(:disabled){color:#00376b}.ecommerce-section[_ngcontent-%COMP%]{border-top:1px solid #efefef;padding:16px;background:#fafafa;margin-top:8px;position:relative;z-index:1;overflow:visible}@media (max-width: 768px){.ecommerce-section[_ngcontent-%COMP%]{padding:12px 16px 16px;background:#fff;border-top:1px solid #efefef;margin-top:0}}.product-showcase[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:12px}@media (max-width: 768px){.product-showcase[_ngcontent-%COMP%]{gap:8px}}@media (max-width: 425px){.product-showcase[_ngcontent-%COMP%]{gap:6px}}@media (max-width: 320px){.product-showcase[_ngcontent-%COMP%]{gap:4px}}.featured-product[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;padding:12px;border:1px solid #efefef;border-radius:8px;transition:all .2s ease;background:#fff;overflow:visible;position:relative}.featured-product[_ngcontent-%COMP%]:hover{background:#f8f9fa;box-shadow:0 2px 8px #0000001a}@media (max-width: 768px){.featured-product[_ngcontent-%COMP%]{padding:10px;gap:10px;border-radius:6px}}@media (max-width: 425px){.featured-product[_ngcontent-%COMP%]{flex-direction:column;align-items:stretch;gap:8px}}.product-header[_ngcontent-%COMP%]{display:none}@media (max-width: 425px){.product-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;width:100%;padding-bottom:5px;border-bottom:1px solid #f0f0f0;margin-bottom:0}}@media (max-width: 375px){.product-header[_ngcontent-%COMP%]{gap:6px;padding-bottom:4px}}@media (max-width: 320px){.product-header[_ngcontent-%COMP%]{gap:5px;padding-bottom:3px}}.product-thumbnail[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:4px;object-fit:cover;flex-shrink:0}@media (max-width: 425px){.product-thumbnail[_ngcontent-%COMP%]{display:none}}.product-header[_ngcontent-%COMP%]   .product-thumbnail[_ngcontent-%COMP%]{width:45px;height:45px;border-radius:6px;display:block}@media (max-width: 375px){.product-header[_ngcontent-%COMP%]   .product-thumbnail[_ngcontent-%COMP%]{width:40px;height:40px}}@media (max-width: 320px){.product-header[_ngcontent-%COMP%]   .product-thumbnail[_ngcontent-%COMP%]{width:35px;height:35px}}.product-details[_ngcontent-%COMP%]{flex:1;display:flex;flex-direction:column;gap:2px;min-width:0}@media (max-width: 425px){.product-details[_ngcontent-%COMP%]{display:none}}.product-header[_ngcontent-%COMP%]   .product-details[_ngcontent-%COMP%]{flex:1;display:flex;flex-direction:column;gap:4px;min-width:0}.product-name[_ngcontent-%COMP%]{font-size:12px;font-weight:600;color:#262626;line-height:1.3;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}@media (max-width: 425px){.product-name[_ngcontent-%COMP%]{font-size:13px}}@media (max-width: 375px){.product-name[_ngcontent-%COMP%]{font-size:12px}}@media (max-width: 320px){.product-name[_ngcontent-%COMP%]{font-size:11px}}.product-price[_ngcontent-%COMP%]{font-size:12px;color:#8e8e8e;font-weight:500}@media (max-width: 425px){.product-price[_ngcontent-%COMP%]{font-size:12px}}@media (max-width: 375px){.product-price[_ngcontent-%COMP%]{font-size:11px}}@media (max-width: 320px){.product-price[_ngcontent-%COMP%]{font-size:10px}}.product-actions[_ngcontent-%COMP%]{display:flex;gap:8px;align-items:center;margin-top:8px;padding:8px 0;overflow:visible;position:relative;z-index:2;min-height:44px}@media (max-width: 768px){.product-actions[_ngcontent-%COMP%]{gap:6px;padding:8px 0 12px;flex-wrap:wrap;justify-content:flex-start}}@media (max-width: 480px){.product-actions[_ngcontent-%COMP%]{gap:4px;padding:6px 0 10px}}@media (max-width: 425px){.product-actions[_ngcontent-%COMP%]{width:100%;justify-content:flex-start;gap:6px;margin-top:5px;padding:5px 0 0;min-height:36px}}@media (max-width: 375px){.product-actions[_ngcontent-%COMP%]{gap:5px;margin-top:4px;padding:4px 0 0;min-height:32px}}@media (max-width: 320px){.product-actions[_ngcontent-%COMP%]{gap:4px;margin-top:3px;padding:3px 0 0;min-height:28px}}.shop-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,#667eea,#764ba2);color:#fff;border:none;padding:8px 16px;border-radius:18px;font-size:12px;font-weight:600;cursor:pointer;transition:all .3s ease;box-shadow:0 2px 8px #667eea4d;position:relative;overflow:hidden;min-width:80px;height:36px;display:flex;align-items:center;justify-content:center;flex-shrink:0}.shop-btn[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,.2),transparent);transition:left .5s}.shop-btn[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#764ba2,#667eea);transform:translateY(-2px);box-shadow:0 4px 15px #667eea66}.shop-btn[_ngcontent-%COMP%]:hover:before{left:100%}.shop-btn[_ngcontent-%COMP%]:active{transform:translateY(0)}@media (max-width: 425px){.shop-btn[_ngcontent-%COMP%]{min-width:70px;height:32px;font-size:11px;padding:6px 12px}}@media (max-width: 375px){.shop-btn[_ngcontent-%COMP%]{min-width:65px;height:30px;font-size:10px;padding:5px 10px}}@media (max-width: 320px){.shop-btn[_ngcontent-%COMP%]{min-width:60px;height:28px;font-size:9px;padding:4px 8px}}.cart-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,#ff6b6b,#ee5a24);color:#fff;border:none;padding:0;border-radius:50%;width:36px;height:36px;cursor:pointer;transition:all .3s ease;box-shadow:0 2px 8px #ff6b6b4d;display:flex!important;align-items:center;justify-content:center;position:relative;overflow:hidden;visibility:visible!important;opacity:1!important;flex-shrink:0}.cart-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:14px;transition:transform .3s ease}.cart-btn[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,.2),transparent);transition:left .5s}.cart-btn[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#ee5a24,#ff6b6b);transform:translateY(-2px) scale(1.05);box-shadow:0 4px 15px #ff6b6b66}.cart-btn[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%]{transform:scale(1.1)}.cart-btn[_ngcontent-%COMP%]:hover:before{left:100%}.cart-btn[_ngcontent-%COMP%]:active{transform:translateY(0) scale(1)}@media (max-width: 425px){.cart-btn[_ngcontent-%COMP%]{width:32px;height:32px}.cart-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:12px}}@media (max-width: 375px){.cart-btn[_ngcontent-%COMP%]{width:30px;height:30px}.cart-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:11px}}@media (max-width: 320px){.cart-btn[_ngcontent-%COMP%]{width:28px;height:28px}.cart-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:10px}}.wishlist-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,#ff9a9e,#fecfef);color:#e91e63;border:none;padding:0;border-radius:50%;width:36px;height:36px;cursor:pointer;transition:all .3s ease;box-shadow:0 2px 8px #ff9a9e4d;display:flex!important;align-items:center;justify-content:center;position:relative;overflow:hidden;visibility:visible!important;opacity:1!important;flex-shrink:0}.wishlist-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:14px;transition:transform .3s ease}.wishlist-btn[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,.2),transparent);transition:left .5s}.wishlist-btn[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#fecfef,#ff9a9e);color:#c2185b;transform:translateY(-2px) scale(1.05);box-shadow:0 4px 15px #ff9a9e66}.wishlist-btn[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%]{transform:scale(1.1)}.wishlist-btn[_ngcontent-%COMP%]:hover:before{left:100%}.wishlist-btn[_ngcontent-%COMP%]:active{transform:translateY(0) scale(1)}.wishlist-btn.active[_ngcontent-%COMP%]{background:linear-gradient(135deg,#e91e63,#c2185b);color:#fff}.wishlist-btn.active[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#c2185b,#e91e63);color:#fff}@media (max-width: 425px){.wishlist-btn[_ngcontent-%COMP%]{width:32px;height:32px}.wishlist-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:12px}}@media (max-width: 375px){.wishlist-btn[_ngcontent-%COMP%]{width:30px;height:30px}.wishlist-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:11px}}@media (max-width: 320px){.wishlist-btn[_ngcontent-%COMP%]{width:28px;height:28px}.wishlist-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:10px}}.buy-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,#4facfe,#00f2fe);color:#fff;border:none;padding:0;border-radius:50%;width:36px;height:36px;cursor:pointer;transition:all .3s ease;box-shadow:0 2px 8px #4facfe4d;display:flex!important;align-items:center;justify-content:center;position:relative;overflow:hidden;visibility:visible!important;opacity:1!important;flex-shrink:0}.buy-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:14px;transition:transform .3s ease}.buy-btn[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,.2),transparent);transition:left .5s}.buy-btn[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#00f2fe,#4facfe);transform:translateY(-2px) scale(1.05);box-shadow:0 4px 15px #4facfe66}.buy-btn[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%]{transform:scale(1.1)}.buy-btn[_ngcontent-%COMP%]:hover:before{left:100%}.buy-btn[_ngcontent-%COMP%]:active{transform:translateY(0) scale(1)}@media (max-width: 425px){.buy-btn[_ngcontent-%COMP%]{width:32px;height:32px}.buy-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:12px}}@media (max-width: 375px){.buy-btn[_ngcontent-%COMP%]{width:30px;height:30px}.buy-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:11px}}@media (max-width: 320px){.buy-btn[_ngcontent-%COMP%]{width:28px;height:28px}.buy-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:10px}}@media (max-width: 768px){.ecommerce-section[_ngcontent-%COMP%]{padding:12px 16px 20px;background:#fff;border-top:1px solid #efefef;margin-bottom:8px}.product-actions[_ngcontent-%COMP%]{gap:8px;padding:10px 0 15px;overflow:visible;position:relative}.shop-btn[_ngcontent-%COMP%]{padding:8px 14px;font-size:12px;border-radius:18px;min-width:60px}.cart-btn[_ngcontent-%COMP%], .wishlist-btn[_ngcontent-%COMP%], .buy-btn[_ngcontent-%COMP%]{width:36px;height:36px;padding:8px}.cart-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .wishlist-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .buy-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:14px}}@media (max-width: 480px){.ecommerce-section[_ngcontent-%COMP%]{padding:12px 8px 25px;margin-bottom:15px;background:#f8f9fa;border-top:2px solid #dee2e6}.featured-product[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start;padding:12px 8px;gap:8px}.product-details[_ngcontent-%COMP%]{width:100%;margin-bottom:8px}.product-actions[_ngcontent-%COMP%]{width:100%;gap:8px;flex-wrap:wrap;padding:10px 0 15px;justify-content:space-between}.shop-btn[_ngcontent-%COMP%]{flex:1;padding:8px 12px;font-size:11px;border-radius:16px;min-width:70px;max-width:120px}.cart-btn[_ngcontent-%COMP%], .wishlist-btn[_ngcontent-%COMP%], .buy-btn[_ngcontent-%COMP%]{width:36px;height:36px;padding:8px;flex-shrink:0}.cart-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .wishlist-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .buy-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:12px}}@media (max-width: 425px){.instagram-post[_ngcontent-%COMP%]{margin-bottom:12px!important;border-radius:0;overflow:visible!important;position:relative}.ecommerce-section[_ngcontent-%COMP%]{padding:10px!important;margin-bottom:10px!important;background:linear-gradient(135deg,#f8f9fa,#e9ecef)!important;border:2px solid #dee2e6!important;border-radius:12px!important;position:relative!important;z-index:100!important;min-height:140px!important;overflow:visible!important;box-shadow:0 6px 20px #0000001a!important;max-width:385px}.product-showcase[_ngcontent-%COMP%]{gap:12px!important;overflow:visible!important}.featured-product[_ngcontent-%COMP%]{display:flex!important;flex-direction:column!important;align-items:stretch!important;padding:12px!important;background:#fff!important;border:1px solid #e9ecef!important;border-radius:10px!important;margin-bottom:10px!important;overflow:visible!important;position:relative!important;box-shadow:0 2px 8px #00000014!important;transition:all .3s ease!important;gap:8px!important}.featured-product[_ngcontent-%COMP%]:hover{transform:translateY(-2px)!important;box-shadow:0 4px 15px #0000001f!important}.featured-product[_ngcontent-%COMP%] > .product-thumbnail[_ngcontent-%COMP%]{display:none!important}.featured-product[_ngcontent-%COMP%] > .product-details[_ngcontent-%COMP%]{display:none!important}.product-header[_ngcontent-%COMP%]{display:flex!important;align-items:center!important;gap:8px!important;width:100%!important;padding-bottom:5px!important;border-bottom:1px solid #f0f0f0!important;margin-bottom:0!important}.product-header[_ngcontent-%COMP%]   .product-thumbnail[_ngcontent-%COMP%]{width:45px!important;height:45px!important;border-radius:6px!important;border:1px solid #e9ecef!important;object-fit:cover!important;flex-shrink:0!important;display:block!important}.product-header[_ngcontent-%COMP%]   .product-details[_ngcontent-%COMP%]{flex:1!important;display:flex!important;flex-direction:column!important;gap:4px!important;min-width:0!important}.product-header[_ngcontent-%COMP%]   .product-name[_ngcontent-%COMP%]{font-size:13px!important;font-weight:600!important;margin-bottom:4px!important;color:#212529!important;display:block!important;line-height:1.3!important;overflow:hidden!important;text-overflow:ellipsis!important;white-space:nowrap!important}.product-header[_ngcontent-%COMP%]   .product-price[_ngcontent-%COMP%]{font-size:12px!important;color:#6c757d!important;font-weight:500!important;display:block!important}.product-actions[_ngcontent-%COMP%]{display:flex!important;flex-direction:row!important;gap:6px!important;padding:5px 0 0!important;align-items:center!important;justify-content:flex-start!important;overflow:visible!important;position:relative!important;z-index:10!important;width:100%!important;margin-top:5px!important}.shop-btn[_ngcontent-%COMP%]{width:70px!important;padding:6px 12px!important;font-size:11px!important;font-weight:600!important;border-radius:16px!important;min-height:32px!important;height:32px!important;display:flex!important;align-items:center!important;justify-content:center!important;margin-bottom:0!important;margin-right:0!important;background:linear-gradient(135deg,#667eea,#764ba2)!important;color:#fff!important;border:none!important;box-shadow:0 2px 8px #667eea4d!important;transition:all .3s ease!important;flex-shrink:0!important;visibility:visible!important;opacity:1!important;position:relative!important;z-index:10!important}.cart-btn[_ngcontent-%COMP%]{width:32px!important;height:32px!important;padding:0!important;border-radius:50%!important;display:flex!important;align-items:center!important;justify-content:center!important;border:none!important;cursor:pointer!important;transition:all .3s ease!important;margin:0!important;flex-shrink:0!important;visibility:visible!important;opacity:1!important;position:relative!important;z-index:10!important;background:linear-gradient(135deg,#ff6b6b,#ee5a24)!important;color:#fff!important;box-shadow:0 2px 6px #ff6b6b4d!important}.cart-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:12px!important;font-weight:700!important}.wishlist-btn[_ngcontent-%COMP%]{width:32px!important;height:32px!important;padding:0!important;border-radius:50%!important;display:flex!important;align-items:center!important;justify-content:center!important;border:none!important;cursor:pointer!important;transition:all .3s ease!important;margin:0!important;flex-shrink:0!important;visibility:visible!important;opacity:1!important;position:relative!important;z-index:10!important;background:linear-gradient(135deg,#ff9a9e,#fecfef)!important;color:#e91e63!important;box-shadow:0 2px 6px #ff9a9e4d!important}.wishlist-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:12px!important;font-weight:700!important}.buy-btn[_ngcontent-%COMP%]{width:32px!important;height:32px!important;padding:0!important;border-radius:50%!important;display:flex!important;align-items:center!important;justify-content:center!important;border:none!important;cursor:pointer!important;transition:all .3s ease!important;margin:0!important;flex-shrink:0!important;visibility:visible!important;opacity:1!important;position:relative!important;z-index:10!important;background:linear-gradient(135deg,#4facfe,#00f2fe)!important;color:#fff!important;box-shadow:0 2px 6px #4facfe4d!important}.buy-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:12px!important;font-weight:700!important}.cart-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,#ff6b6b,#ee5a24)!important;color:#fff!important;box-shadow:0 2px 6px #ff6b6b4d!important}.wishlist-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,#ff9ff3,#f368e0)!important;color:#fff!important;box-shadow:0 2px 6px #ff9ff34d!important}.buy-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,#4facfe,#00f2fe)!important;color:#fff!important;box-shadow:0 2px 6px #4facfe4d!important}.cart-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .wishlist-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .buy-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:14px!important;font-weight:700!important}.shop-btn[_ngcontent-%COMP%]:hover{transform:translateY(-1px)!important;box-shadow:0 4px 12px #667eea80!important}.cart-btn[_ngcontent-%COMP%]:hover, .wishlist-btn[_ngcontent-%COMP%]:hover, .buy-btn[_ngcontent-%COMP%]:hover{transform:translateY(-1px) scale(1.05)!important}}@media (max-width: 375px){.ecommerce-section[_ngcontent-%COMP%]{padding:10px 8px!important;margin-bottom:18px!important;background:linear-gradient(135deg,#f1f3f4,#e8eaed)!important;border:1px solid #dadce0!important;border-radius:10px!important;min-height:130px!important;box-shadow:0 4px 16px #00000014!important;max-width:335px}.featured-product[_ngcontent-%COMP%]{padding:10px!important;margin-bottom:8px!important;border-radius:8px!important}.product-thumbnail[_ngcontent-%COMP%]{width:50px!important;height:50px!important;margin-right:10px!important;border-radius:6px!important}.product-name[_ngcontent-%COMP%]{font-size:12px!important;font-weight:600!important;line-height:1.2!important}.product-price[_ngcontent-%COMP%]{font-size:11px!important;font-weight:500!important}.product-actions[_ngcontent-%COMP%]{min-width:70px!important;gap:4px!important}.shop-btn[_ngcontent-%COMP%]{width:65px!important;padding:6px 8px!important;font-size:10px!important;min-height:28px!important;border-radius:14px!important}.cart-btn[_ngcontent-%COMP%], .wishlist-btn[_ngcontent-%COMP%], .buy-btn[_ngcontent-%COMP%]{width:28px!important;height:28px!important;padding:4px!important}.cart-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .wishlist-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .buy-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:12px!important}}@media (max-width: 350px){.ecommerce-section[_ngcontent-%COMP%]{padding:12px 6px 25px!important;margin-bottom:15px!important;background:linear-gradient(135deg,#f8f9fa,#e9ecef)!important;border:1px solid #ced4da!important;border-radius:8px!important;min-height:120px!important;box-shadow:0 3px 12px #0000000f!important;max-width:290px}.featured-product[_ngcontent-%COMP%]{padding:8px!important;margin-bottom:6px!important;border-radius:6px!important;gap:8px!important}.product-thumbnail[_ngcontent-%COMP%]{width:45px!important;height:45px!important;margin-right:8px!important;border-radius:5px!important}.product-details[_ngcontent-%COMP%]{margin-right:6px!important}.product-name[_ngcontent-%COMP%]{font-size:11px!important;font-weight:600!important;line-height:1.2!important;margin-bottom:2px!important}.product-price[_ngcontent-%COMP%]{font-size:10px!important;font-weight:500!important}.product-actions[_ngcontent-%COMP%]{min-width:60px!important;gap:3px!important}.shop-btn[_ngcontent-%COMP%]{width:55px!important;padding:5px 6px!important;font-size:9px!important;min-height:24px!important;border-radius:12px!important;margin-bottom:2px!important}.cart-btn[_ngcontent-%COMP%], .wishlist-btn[_ngcontent-%COMP%], .buy-btn[_ngcontent-%COMP%]{width:24px!important;height:24px!important;padding:3px!important;margin:.5px!important}.cart-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .wishlist-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .buy-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:10px!important}}@media (max-width: 320px){.ecommerce-section[_ngcontent-%COMP%]{padding:10px 4px!important;margin-bottom:12px!important;background:#f8f9fa!important;border:1px solid #dee2e6!important;border-radius:6px!important;min-height:110px!important;box-shadow:0 2px 8px #0000000d!important;max-width:285px}.featured-product[_ngcontent-%COMP%]{padding:6px!important;margin-bottom:4px!important;border-radius:4px!important;gap:6px!important}.product-thumbnail[_ngcontent-%COMP%]{width:40px!important;height:40px!important;margin-right:6px!important;border-radius:4px!important}.product-details[_ngcontent-%COMP%]{margin-right:4px!important}.product-name[_ngcontent-%COMP%]{font-size:10px!important;font-weight:600!important;line-height:1.1!important;margin-bottom:1px!important}.product-price[_ngcontent-%COMP%]{font-size:9px!important;font-weight:500!important}.product-actions[_ngcontent-%COMP%]{min-width:50px!important;gap:2px!important}.shop-btn[_ngcontent-%COMP%]{width:45px!important;padding:4px 5px!important;font-size:8px!important;min-height:20px!important;border-radius:10px!important;margin-bottom:1px!important}.cart-btn[_ngcontent-%COMP%], .wishlist-btn[_ngcontent-%COMP%], .buy-btn[_ngcontent-%COMP%]{width:20px!important;height:20px!important;padding:2px!important;margin:.5px!important}.cart-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .wishlist-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .buy-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:8px!important}}.load-more[_ngcontent-%COMP%]{text-align:center;padding:20px}.load-more-btn[_ngcontent-%COMP%]{background:#0095f6;color:#fff;border:none;padding:12px 24px;border-radius:8px;font-size:14px;font-weight:600;cursor:pointer;transition:background .2s ease}.load-more-btn[_ngcontent-%COMP%]:hover{background:#00376b}.empty-feed[_ngcontent-%COMP%]{text-align:center;padding:60px 20px;background:#fff;border:1px solid #dbdbdb;border-radius:8px;margin:20px}.empty-content[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:48px;color:#dbdbdb;margin-bottom:16px}.empty-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:20px;font-weight:600;color:#262626;margin:0 0 8px}.empty-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:14px;color:#8e8e8e;margin:0}@media (max-width: 768px){.instagram-feed[_ngcontent-%COMP%]{max-width:100%}.instagram-post[_ngcontent-%COMP%]{border-radius:0;border-left:none;border-right:none;margin-bottom:0}.feed-posts[_ngcontent-%COMP%]{gap:0}}@media (max-width: 480px){.post-header[_ngcontent-%COMP%]{padding:12px}.post-actions[_ngcontent-%COMP%]{padding:6px 12px}.likes-section[_ngcontent-%COMP%], .post-caption[_ngcontent-%COMP%], .comments-preview[_ngcontent-%COMP%], .post-time[_ngcontent-%COMP%]{padding-left:12px;padding-right:12px}.add-comment-section[_ngcontent-%COMP%]{padding:12px}.ecommerce-section[_ngcontent-%COMP%]{padding:12px 16px 20px;background:#f8f9fa;border-top:2px solid #e9ecef;margin-bottom:8px;position:relative;z-index:1;overflow:visible}}\"]\n      });\n    }\n  }\n  return FeedComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}