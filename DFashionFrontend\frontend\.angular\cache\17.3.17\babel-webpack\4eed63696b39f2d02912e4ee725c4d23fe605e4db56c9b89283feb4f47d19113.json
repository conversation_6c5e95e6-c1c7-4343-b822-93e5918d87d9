{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashionFrontend/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CommonModule } from '@angular/common';\nimport { Subscription } from 'rxjs';\nimport { IonicModule } from '@ionic/angular';\nimport { CarouselModule } from 'ngx-owl-carousel-o';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@ionic/angular\";\nconst _c0 = () => [1, 2, 3];\nfunction TopFashionInfluencersComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function TopFashionInfluencersComponent_div_1_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleSectionLike());\n    });\n    i0.ɵɵelement(2, \"ion-icon\", 13);\n    i0.ɵɵelementStart(3, \"span\", 14);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function TopFashionInfluencersComponent_div_1_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openComments());\n    });\n    i0.ɵɵelement(6, \"ion-icon\", 16);\n    i0.ɵɵelementStart(7, \"span\", 14);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function TopFashionInfluencersComponent_div_1_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.shareSection());\n    });\n    i0.ɵɵelement(10, \"ion-icon\", 18);\n    i0.ɵɵelementStart(11, \"span\", 19);\n    i0.ɵɵtext(12, \"Share\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function TopFashionInfluencersComponent_div_1_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleSectionBookmark());\n    });\n    i0.ɵɵelement(14, \"ion-icon\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function TopFashionInfluencersComponent_div_1_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openMusicPlayer());\n    });\n    i0.ɵɵelement(16, \"ion-icon\", 22);\n    i0.ɵɵelementStart(17, \"span\", 19);\n    i0.ɵɵtext(18, \"Music\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"active\", ctx_r1.isSectionLiked);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", ctx_r1.isSectionLiked ? \"heart\" : \"heart-outline\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.formatCount(ctx_r1.sectionLikes));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.formatCount(ctx_r1.sectionComments));\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"active\", ctx_r1.isSectionBookmarked);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", ctx_r1.isSectionBookmarked ? \"bookmark\" : \"bookmark-outline\");\n  }\n}\nfunction TopFashionInfluencersComponent_div_9_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵelement(1, \"div\", 27);\n    i0.ɵɵelementStart(2, \"div\", 28);\n    i0.ɵɵelement(3, \"div\", 29)(4, \"div\", 30)(5, \"div\", 31);\n    i0.ɵɵelementStart(6, \"div\", 32);\n    i0.ɵɵelement(7, \"div\", 33)(8, \"div\", 33);\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction TopFashionInfluencersComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"div\", 24);\n    i0.ɵɵtemplate(2, TopFashionInfluencersComponent_div_9_div_2_Template, 9, 0, \"div\", 25);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c0));\n  }\n}\nfunction TopFashionInfluencersComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵelement(1, \"ion-icon\", 35);\n    i0.ɵɵelementStart(2, \"p\", 36);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function TopFashionInfluencersComponent_div_10_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onRetry());\n    });\n    i0.ɵɵelement(5, \"ion-icon\", 38);\n    i0.ɵɵtext(6, \" Try Again \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction TopFashionInfluencersComponent_div_11_div_7_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65);\n    i0.ɵɵelement(1, \"ion-icon\", 66);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TopFashionInfluencersComponent_div_11_div_7_span_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 67);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const brand_r7 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", brand_r7, \" \");\n  }\n}\nfunction TopFashionInfluencersComponent_div_11_div_7_span_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 68);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const influencer_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" +\", influencer_r6.topBrands.length - 2, \" \");\n  }\n}\nfunction TopFashionInfluencersComponent_div_11_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵlistener(\"click\", function TopFashionInfluencersComponent_div_11_div_7_Template_div_click_0_listener() {\n      const influencer_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onInfluencerClick(influencer_r6));\n    });\n    i0.ɵɵelementStart(1, \"div\", 48);\n    i0.ɵɵelement(2, \"img\", 49);\n    i0.ɵɵtemplate(3, TopFashionInfluencersComponent_div_11_div_7_div_3_Template, 2, 0, \"div\", 50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 51)(5, \"h3\", 52);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 53);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\", 54);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 55)(12, \"div\", 56)(13, \"span\", 57);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\", 58);\n    i0.ɵɵtext(16, \"Followers\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 56)(18, \"span\", 57);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"span\", 58);\n    i0.ɵɵtext(21, \"Engagement\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(22, \"div\", 59)(23, \"span\", 60);\n    i0.ɵɵtext(24, \"Works with:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"div\", 61);\n    i0.ɵɵtemplate(26, TopFashionInfluencersComponent_div_11_div_7_span_26_Template, 2, 1, \"span\", 62)(27, TopFashionInfluencersComponent_div_11_div_7_span_27_Template, 2, 1, \"span\", 63);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(28, \"button\", 64);\n    i0.ɵɵlistener(\"click\", function TopFashionInfluencersComponent_div_11_div_7_Template_button_click_28_listener($event) {\n      const influencer_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onFollowInfluencer(influencer_r6, $event));\n    });\n    i0.ɵɵelementStart(29, \"span\");\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(31, \"ion-icon\", 13);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const influencer_r6 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", influencer_r6.avatar, i0.ɵɵsanitizeUrl)(\"alt\", influencer_r6.fullName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", influencer_r6.isVerified);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(influencer_r6.fullName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"@\", influencer_r6.username, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(influencer_r6.category);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.formatFollowerCount(influencer_r6.followerCount));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", influencer_r6.engagementRate, \"%\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", influencer_r6.topBrands.slice(0, 2));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", influencer_r6.topBrands.length > 2);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"following\", influencer_r6.isFollowing);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(influencer_r6.isFollowing ? \"Following\" : \"Follow\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", influencer_r6.isFollowing ? \"checkmark\" : \"add\");\n  }\n}\nfunction TopFashionInfluencersComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function TopFashionInfluencersComponent_div_11_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.slidePrev());\n    });\n    i0.ɵɵelement(2, \"ion-icon\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function TopFashionInfluencersComponent_div_11_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.slideNext());\n    });\n    i0.ɵɵelement(4, \"ion-icon\", 43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 44);\n    i0.ɵɵlistener(\"mouseenter\", function TopFashionInfluencersComponent_div_11_Template_div_mouseenter_5_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.pauseAutoSlide());\n    })(\"mouseleave\", function TopFashionInfluencersComponent_div_11_Template_div_mouseleave_5_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.resumeAutoSlide());\n    });\n    i0.ɵɵelementStart(6, \"div\", 45);\n    i0.ɵɵtemplate(7, TopFashionInfluencersComponent_div_11_div_7_Template, 32, 14, \"div\", 46);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentSlide === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentSlide >= ctx_r1.maxSlide);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"transform\", \"translateX(\" + ctx_r1.slideOffset + \"px)\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.topInfluencers)(\"ngForTrackBy\", ctx_r1.trackByInfluencerId);\n  }\n}\nfunction TopFashionInfluencersComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 69);\n    i0.ɵɵelement(1, \"ion-icon\", 70);\n    i0.ɵɵelementStart(2, \"h3\", 71);\n    i0.ɵɵtext(3, \"No Influencers\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 72);\n    i0.ɵɵtext(5, \"Check back later for top fashion influencers\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class TopFashionInfluencersComponent {\n  constructor(router) {\n    this.router = router;\n    this.topInfluencers = [];\n    this.isLoading = true;\n    this.error = null;\n    this.subscription = new Subscription();\n    // Slider properties\n    this.currentSlide = 0;\n    this.slideOffset = 0;\n    this.cardWidth = 240; // Width of each influencer card including margin\n    this.visibleCards = 3; // Number of cards visible at once\n    this.maxSlide = 0;\n    this.autoSlideDelay = 6000; // 6 seconds for influencers\n    this.isAutoSliding = true;\n    this.isPaused = false;\n    // Section interaction properties\n    this.isSectionLiked = false;\n    this.isSectionBookmarked = false;\n    this.sectionLikes = 512;\n    this.sectionComments = 234;\n    this.isMobile = false;\n  }\n  ngOnInit() {\n    this.loadTopInfluencers();\n    this.updateResponsiveSettings();\n    this.setupResizeListener();\n    this.checkMobileDevice();\n  }\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n    this.stopAutoSlide();\n  }\n  loadTopInfluencers() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this.isLoading = true;\n        _this.error = null;\n        // Mock data for top fashion influencers\n        _this.topInfluencers = [{\n          id: '1',\n          username: 'fashionista_queen',\n          fullName: 'Priya Sharma',\n          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=150&h=150&fit=crop&crop=face',\n          followerCount: 2500000,\n          category: 'High Fashion',\n          isVerified: true,\n          isFollowing: false,\n          engagementRate: 8.5,\n          recentPosts: 24,\n          topBrands: ['Gucci', 'Prada', 'Versace']\n        }, {\n          id: '2',\n          username: 'street_style_king',\n          fullName: 'Arjun Kapoor',\n          avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',\n          followerCount: 1800000,\n          category: 'Streetwear',\n          isVerified: true,\n          isFollowing: false,\n          engagementRate: 12.3,\n          recentPosts: 18,\n          topBrands: ['Nike', 'Adidas', 'Supreme']\n        }, {\n          id: '3',\n          username: 'boho_goddess',\n          fullName: 'Ananya Singh',\n          avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',\n          followerCount: 1200000,\n          category: 'Boho Chic',\n          isVerified: true,\n          isFollowing: false,\n          engagementRate: 9.7,\n          recentPosts: 32,\n          topBrands: ['Free People', 'Anthropologie', 'Zara']\n        }, {\n          id: '4',\n          username: 'luxury_lifestyle',\n          fullName: 'Kavya Reddy',\n          avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face',\n          followerCount: 3200000,\n          category: 'Luxury',\n          isVerified: true,\n          isFollowing: false,\n          engagementRate: 6.8,\n          recentPosts: 15,\n          topBrands: ['Chanel', 'Dior', 'Louis Vuitton']\n        }, {\n          id: '5',\n          username: 'minimalist_maven',\n          fullName: 'Ravi Kumar',\n          avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',\n          followerCount: 950000,\n          category: 'Minimalist',\n          isVerified: true,\n          isFollowing: false,\n          engagementRate: 11.2,\n          recentPosts: 21,\n          topBrands: ['COS', 'Uniqlo', 'Everlane']\n        }, {\n          id: '6',\n          username: 'vintage_vibes',\n          fullName: 'Meera Patel',\n          avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face',\n          followerCount: 780000,\n          category: 'Vintage',\n          isVerified: true,\n          isFollowing: false,\n          engagementRate: 13.5,\n          recentPosts: 28,\n          topBrands: ['Vintage Stores', 'Thrift Finds', 'Custom']\n        }];\n        _this.isLoading = false;\n        _this.updateSliderOnInfluencersLoad();\n      } catch (error) {\n        console.error('Error loading top influencers:', error);\n        _this.error = 'Failed to load top influencers';\n        _this.isLoading = false;\n      }\n    })();\n  }\n  onInfluencerClick(influencer) {\n    this.router.navigate(['/profile', influencer.username]);\n  }\n  onFollowInfluencer(influencer, event) {\n    event.stopPropagation();\n    influencer.isFollowing = !influencer.isFollowing;\n    if (influencer.isFollowing) {\n      influencer.followerCount++;\n    } else {\n      influencer.followerCount--;\n    }\n  }\n  formatFollowerCount(count) {\n    if (count >= 1000000) {\n      return (count / 1000000).toFixed(1) + 'M';\n    } else if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'K';\n    }\n    return count.toString();\n  }\n  onRetry() {\n    this.loadTopInfluencers();\n  }\n  trackByInfluencerId(index, influencer) {\n    return influencer.id;\n  }\n  // Auto-sliding methods\n  startAutoSlide() {\n    if (!this.isAutoSliding || this.isPaused) return;\n    this.stopAutoSlide();\n    this.autoSlideInterval = setInterval(() => {\n      if (!this.isPaused && this.topInfluencers.length > this.visibleCards) {\n        this.autoSlideNext();\n      }\n    }, this.autoSlideDelay);\n  }\n  stopAutoSlide() {\n    if (this.autoSlideInterval) {\n      clearInterval(this.autoSlideInterval);\n      this.autoSlideInterval = null;\n    }\n  }\n  autoSlideNext() {\n    if (this.currentSlide >= this.maxSlide) {\n      this.currentSlide = 0;\n    } else {\n      this.currentSlide++;\n    }\n    this.updateSlideOffset();\n  }\n  pauseAutoSlide() {\n    this.isPaused = true;\n    this.stopAutoSlide();\n  }\n  resumeAutoSlide() {\n    this.isPaused = false;\n    this.startAutoSlide();\n  }\n  // Responsive methods\n  updateResponsiveSettings() {\n    const width = window.innerWidth;\n    if (width <= 480) {\n      this.cardWidth = 200;\n      this.visibleCards = 1;\n    } else if (width <= 768) {\n      this.cardWidth = 220;\n      this.visibleCards = 2;\n    } else if (width <= 1200) {\n      this.cardWidth = 240;\n      this.visibleCards = 2;\n    } else {\n      this.cardWidth = 260;\n      this.visibleCards = 3;\n    }\n    this.updateSliderLimits();\n    this.updateSlideOffset();\n  }\n  setupResizeListener() {\n    window.addEventListener('resize', () => {\n      this.updateResponsiveSettings();\n    });\n  }\n  // Slider methods\n  updateSliderLimits() {\n    this.maxSlide = Math.max(0, this.topInfluencers.length - this.visibleCards);\n  }\n  slidePrev() {\n    if (this.currentSlide > 0) {\n      this.currentSlide--;\n      this.updateSlideOffset();\n      this.restartAutoSlideAfterInteraction();\n    }\n  }\n  slideNext() {\n    if (this.currentSlide < this.maxSlide) {\n      this.currentSlide++;\n      this.updateSlideOffset();\n      this.restartAutoSlideAfterInteraction();\n    }\n  }\n  updateSlideOffset() {\n    this.slideOffset = -this.currentSlide * this.cardWidth;\n  }\n  restartAutoSlideAfterInteraction() {\n    this.stopAutoSlide();\n    setTimeout(() => {\n      this.startAutoSlide();\n    }, 2000);\n  }\n  // Update slider when influencers load\n  updateSliderOnInfluencersLoad() {\n    setTimeout(() => {\n      this.updateSliderLimits();\n      this.currentSlide = 0;\n      this.slideOffset = 0;\n      this.startAutoSlide();\n    }, 100);\n  }\n  // Section interaction methods\n  toggleSectionLike() {\n    this.isSectionLiked = !this.isSectionLiked;\n    if (this.isSectionLiked) {\n      this.sectionLikes++;\n    } else {\n      this.sectionLikes--;\n    }\n  }\n  toggleSectionBookmark() {\n    this.isSectionBookmarked = !this.isSectionBookmarked;\n  }\n  openComments() {\n    console.log('Opening comments for top fashion influencers section');\n  }\n  shareSection() {\n    if (navigator.share) {\n      navigator.share({\n        title: 'Top Fashion Influencers',\n        text: 'Follow the top fashion trendsetters!',\n        url: window.location.href\n      });\n    } else {\n      navigator.clipboard.writeText(window.location.href);\n      console.log('Link copied to clipboard');\n    }\n  }\n  openMusicPlayer() {\n    console.log('Opening music player for top fashion influencers');\n  }\n  formatCount(count) {\n    if (count >= 1000000) {\n      return (count / 1000000).toFixed(1) + 'M';\n    } else if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'K';\n    }\n    return count.toString();\n  }\n  checkMobileDevice() {\n    this.isMobile = window.innerWidth <= 768;\n  }\n  static {\n    this.ɵfac = function TopFashionInfluencersComponent_Factory(t) {\n      return new (t || TopFashionInfluencersComponent)(i0.ɵɵdirectiveInject(i1.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TopFashionInfluencersComponent,\n      selectors: [[\"app-top-fashion-influencers\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 13,\n      vars: 5,\n      consts: [[1, \"top-influencers-container\"], [\"class\", \"mobile-action-buttons\", 4, \"ngIf\"], [1, \"section-header\"], [1, \"header-content\"], [1, \"section-title\"], [\"name\", \"star\", 1, \"title-icon\"], [1, \"section-subtitle\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"influencers-slider-container\", 4, \"ngIf\"], [\"class\", \"empty-container\", 4, \"ngIf\"], [1, \"mobile-action-buttons\"], [1, \"action-btn\", \"like-btn\", 3, \"click\"], [3, \"name\"], [1, \"action-count\"], [1, \"action-btn\", \"comment-btn\", 3, \"click\"], [\"name\", \"chatbubble-outline\"], [1, \"action-btn\", \"share-btn\", 3, \"click\"], [\"name\", \"arrow-redo-outline\"], [1, \"action-text\"], [1, \"action-btn\", \"bookmark-btn\", 3, \"click\"], [1, \"action-btn\", \"music-btn\", 3, \"click\"], [\"name\", \"musical-notes\"], [1, \"loading-container\"], [1, \"loading-grid\"], [\"class\", \"loading-influencer-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"loading-influencer-card\"], [1, \"loading-avatar\"], [1, \"loading-content\"], [1, \"loading-line\", \"short\"], [1, \"loading-line\", \"medium\"], [1, \"loading-line\", \"long\"], [1, \"loading-stats\"], [1, \"loading-stat\"], [1, \"error-container\"], [\"name\", \"alert-circle\", 1, \"error-icon\"], [1, \"error-message\"], [1, \"retry-btn\", 3, \"click\"], [\"name\", \"refresh\"], [1, \"influencers-slider-container\"], [1, \"slider-nav\", \"prev-btn\", 3, \"click\", \"disabled\"], [\"name\", \"chevron-back\"], [1, \"slider-nav\", \"next-btn\", 3, \"click\", \"disabled\"], [\"name\", \"chevron-forward\"], [1, \"influencers-slider-wrapper\", 3, \"mouseenter\", \"mouseleave\"], [1, \"influencers-slider\"], [\"class\", \"influencer-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"influencer-card\", 3, \"click\"], [1, \"influencer-avatar-container\"], [\"loading\", \"lazy\", 1, \"influencer-avatar\", 3, \"src\", \"alt\"], [\"class\", \"verified-badge\", 4, \"ngIf\"], [1, \"influencer-info\"], [1, \"influencer-name\"], [1, \"username\"], [1, \"category\"], [1, \"stats-container\"], [1, \"stat\"], [1, \"stat-value\"], [1, \"stat-label\"], [1, \"top-brands\"], [1, \"brands-label\"], [1, \"brands-list\"], [\"class\", \"brand-tag\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"more-brands\", 4, \"ngIf\"], [1, \"follow-btn\", 3, \"click\"], [1, \"verified-badge\"], [\"name\", \"checkmark\"], [1, \"brand-tag\"], [1, \"more-brands\"], [1, \"empty-container\"], [\"name\", \"star-outline\", 1, \"empty-icon\"], [1, \"empty-title\"], [1, \"empty-message\"]],\n      template: function TopFashionInfluencersComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, TopFashionInfluencersComponent_div_1_Template, 19, 8, \"div\", 1);\n          i0.ɵɵelementStart(2, \"div\", 2)(3, \"div\", 3)(4, \"h2\", 4);\n          i0.ɵɵelement(5, \"ion-icon\", 5);\n          i0.ɵɵtext(6, \" Top Fashion Influencers \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"p\", 6);\n          i0.ɵɵtext(8, \"Follow the trendsetters\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(9, TopFashionInfluencersComponent_div_9_Template, 3, 2, \"div\", 7)(10, TopFashionInfluencersComponent_div_10_Template, 7, 1, \"div\", 8)(11, TopFashionInfluencersComponent_div_11_Template, 8, 6, \"div\", 9)(12, TopFashionInfluencersComponent_div_12_Template, 6, 0, \"div\", 10);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isMobile);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.error && !ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.topInfluencers.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.topInfluencers.length === 0);\n        }\n      },\n      dependencies: [CommonModule, i2.NgForOf, i2.NgIf, IonicModule, i3.IonIcon, CarouselModule],\n      styles: [\".top-influencers-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n  border-radius: 16px;\\n  margin-bottom: 24px;\\n  position: relative;\\n  max-width: 675px;\\n}\\n\\n.mobile-action-buttons[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 15px;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  display: flex;\\n  flex-direction: column;\\n  gap: 20px;\\n  z-index: 10;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%] {\\n  width: 48px;\\n  height: 48px;\\n  border-radius: 50%;\\n  border: none;\\n  background: rgba(255, 215, 0, 0.2);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  color: #ffd700;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  position: relative;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  margin-bottom: 2px;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   .action-count[_ngcontent-%COMP%], .mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   .action-text[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n  font-weight: 600;\\n  line-height: 1;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1);\\n  background: rgba(255, 215, 0, 0.3);\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active[_ngcontent-%COMP%] {\\n  background: rgba(255, 215, 0, 0.9);\\n  color: white;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active.like-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 48, 64, 0.9);\\n  color: white;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active.like-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_heartBeat 0.6s ease-in-out;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active.bookmark-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 215, 0, 0.9);\\n  color: white;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.like-btn.active[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  color: white;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.music-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ff3040 0%, #ffd700 100%);\\n  color: white;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.music-btn[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1) rotate(15deg);\\n}\\n\\n@keyframes _ngcontent-%COMP%_heartBeat {\\n  0% {\\n    transform: scale(1);\\n  }\\n  25% {\\n    transform: scale(1.3);\\n  }\\n  50% {\\n    transform: scale(1.1);\\n  }\\n  75% {\\n    transform: scale(1.25);\\n  }\\n  100% {\\n    transform: scale(1);\\n  }\\n}\\n@media (min-width: 769px) {\\n  .mobile-action-buttons[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n.section-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 24px;\\n}\\n.section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 700;\\n  color: #1a1a1a;\\n  margin: 0 0 8px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   .title-icon[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  color: #ffd700;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-subtitle[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #666;\\n  margin: 0;\\n}\\n\\n.loading-container[_ngcontent-%COMP%]   .loading-grid[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 20px;\\n  overflow-x: auto;\\n  padding-bottom: 8px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-influencer-card[_ngcontent-%COMP%] {\\n  flex: 0 0 220px;\\n  background: rgba(255, 255, 255, 0.7);\\n  border-radius: 16px;\\n  padding: 20px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-influencer-card[_ngcontent-%COMP%]   .loading-avatar[_ngcontent-%COMP%] {\\n  width: 100px;\\n  height: 100px;\\n  border-radius: 50%;\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n  margin: 0 auto 16px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-influencer-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line[_ngcontent-%COMP%] {\\n  height: 12px;\\n  border-radius: 6px;\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n  margin-bottom: 8px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-influencer-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.short[_ngcontent-%COMP%] {\\n  width: 60%;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-influencer-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.medium[_ngcontent-%COMP%] {\\n  width: 80%;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-influencer-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.long[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-influencer-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10px;\\n  margin-top: 12px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-influencer-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-stats[_ngcontent-%COMP%]   .loading-stat[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 20px;\\n  border-radius: 10px;\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_loading {\\n  0% {\\n    background-position: 200% 0;\\n  }\\n  100% {\\n    background-position: -200% 0;\\n  }\\n}\\n.error-container[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 40px 20px;\\n}\\n.error-container[_ngcontent-%COMP%]   .error-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  color: #e74c3c;\\n  margin-bottom: 16px;\\n}\\n.error-container[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%] {\\n  color: #666;\\n  margin-bottom: 20px;\\n  font-size: 16px;\\n}\\n.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);\\n  color: white;\\n  border: none;\\n  padding: 12px 24px;\\n  border-radius: 25px;\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin: 0 auto;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 8px 25px rgba(231, 76, 60, 0.3);\\n}\\n\\n.influencers-slider-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  margin: 0 -20px;\\n}\\n.influencers-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  z-index: 10;\\n  background: rgba(0, 0, 0, 0.7);\\n  color: white;\\n  border: none;\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.influencers-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: rgba(0, 0, 0, 0.9);\\n  transform: translateY(-50%) scale(1.1);\\n}\\n.influencers-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.3;\\n  cursor: not-allowed;\\n}\\n.influencers-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n}\\n.influencers-slider-container[_ngcontent-%COMP%]   .slider-nav.prev-btn[_ngcontent-%COMP%] {\\n  left: -20px;\\n}\\n.influencers-slider-container[_ngcontent-%COMP%]   .slider-nav.next-btn[_ngcontent-%COMP%] {\\n  right: -20px;\\n}\\n\\n.influencers-slider-wrapper[_ngcontent-%COMP%] {\\n  overflow: hidden;\\n  padding: 0 20px;\\n}\\n\\n.influencers-slider[_ngcontent-%COMP%] {\\n  display: flex;\\n  transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);\\n  gap: 20px;\\n}\\n.influencers-slider[_ngcontent-%COMP%]   .influencer-card[_ngcontent-%COMP%] {\\n  flex: 0 0 220px;\\n  width: 220px;\\n}\\n\\n.influencer-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 16px;\\n  padding: 24px;\\n  text-align: center;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n}\\n.influencer-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-8px);\\n  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);\\n}\\n\\n.influencer-avatar-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  margin-bottom: 20px;\\n}\\n.influencer-avatar-container[_ngcontent-%COMP%]   .influencer-avatar[_ngcontent-%COMP%] {\\n  width: 100px;\\n  height: 100px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n  border: 4px solid #ffd700;\\n  margin: 0 auto;\\n  display: block;\\n}\\n.influencer-avatar-container[_ngcontent-%COMP%]   .verified-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -5px;\\n  right: calc(50% - 55px);\\n  background: linear-gradient(135deg, #00b894 0%, #00a085 100%);\\n  color: white;\\n  width: 28px;\\n  height: 28px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 14px;\\n  border: 3px solid white;\\n}\\n.influencer-avatar-container[_ngcontent-%COMP%]   .verified-badge[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n}\\n\\n.influencer-info[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n.influencer-info[_ngcontent-%COMP%]   .influencer-name[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 700;\\n  color: #1a1a1a;\\n  margin: 0 0 4px 0;\\n}\\n.influencer-info[_ngcontent-%COMP%]   .username[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #6c5ce7;\\n  margin: 0 0 8px 0;\\n  font-weight: 500;\\n}\\n.influencer-info[_ngcontent-%COMP%]   .category[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #ffd700;\\n  background: rgba(255, 215, 0, 0.1);\\n  padding: 4px 12px;\\n  border-radius: 12px;\\n  display: inline-block;\\n  margin: 0 0 16px 0;\\n  font-weight: 600;\\n}\\n\\n.stats-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  margin-bottom: 16px;\\n}\\n.stats-container[_ngcontent-%COMP%]   .stat[_ngcontent-%COMP%] {\\n  flex: 1;\\n  text-align: center;\\n}\\n.stats-container[_ngcontent-%COMP%]   .stat[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 16px;\\n  font-weight: 700;\\n  color: #1a1a1a;\\n  margin-bottom: 2px;\\n}\\n.stats-container[_ngcontent-%COMP%]   .stat[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: #666;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n\\n.top-brands[_ngcontent-%COMP%]   .brands-label[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: #666;\\n  display: block;\\n  margin-bottom: 6px;\\n}\\n.top-brands[_ngcontent-%COMP%]   .brands-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 4px;\\n  justify-content: center;\\n}\\n.top-brands[_ngcontent-%COMP%]   .brands-list[_ngcontent-%COMP%]   .brand-tag[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n  background: rgba(108, 92, 231, 0.1);\\n  color: #6c5ce7;\\n  padding: 2px 6px;\\n  border-radius: 8px;\\n  font-weight: 500;\\n}\\n.top-brands[_ngcontent-%COMP%]   .brands-list[_ngcontent-%COMP%]   .more-brands[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n  color: #999;\\n  font-weight: 500;\\n}\\n\\n.follow-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%);\\n  color: #1a1a1a;\\n  border: none;\\n  padding: 10px 20px;\\n  border-radius: 25px;\\n  font-size: 13px;\\n  font-weight: 700;\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  margin: 0 auto;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.follow-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 8px 25px rgba(255, 215, 0, 0.4);\\n}\\n.follow-btn.following[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #00b894 0%, #00a085 100%);\\n  color: white;\\n}\\n.follow-btn.following[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 8px 25px rgba(0, 184, 148, 0.4);\\n}\\n.follow-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n\\n.empty-container[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 60px 20px;\\n}\\n.empty-container[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%] {\\n  font-size: 64px;\\n  color: #ddd;\\n  margin-bottom: 20px;\\n}\\n.empty-container[_ngcontent-%COMP%]   .empty-title[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: #666;\\n  margin: 0 0 8px 0;\\n}\\n.empty-container[_ngcontent-%COMP%]   .empty-message[_ngcontent-%COMP%] {\\n  color: #999;\\n  margin: 0;\\n}\\n\\n@media (max-width: 1200px) {\\n  .influencers-slider[_ngcontent-%COMP%]   .influencer-card[_ngcontent-%COMP%] {\\n    flex: 0 0 200px;\\n    width: 200px;\\n    padding: 20px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .influencers-slider-container[_ngcontent-%COMP%] {\\n    margin: 0 -10px;\\n  }\\n  .influencers-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%] {\\n    width: 35px;\\n    height: 35px;\\n  }\\n  .influencers-slider-container[_ngcontent-%COMP%]   .slider-nav.prev-btn[_ngcontent-%COMP%] {\\n    left: -15px;\\n  }\\n  .influencers-slider-container[_ngcontent-%COMP%]   .slider-nav.next-btn[_ngcontent-%COMP%] {\\n    right: -15px;\\n  }\\n  .influencers-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n  .influencers-slider-wrapper[_ngcontent-%COMP%] {\\n    padding: 0 10px;\\n  }\\n  .influencers-slider[_ngcontent-%COMP%] {\\n    gap: 15px;\\n  }\\n  .influencers-slider[_ngcontent-%COMP%]   .influencer-card[_ngcontent-%COMP%] {\\n    flex: 0 0 180px;\\n    width: 180px;\\n    padding: 16px;\\n  }\\n  .influencer-avatar-container[_ngcontent-%COMP%]   .influencer-avatar[_ngcontent-%COMP%] {\\n    width: 80px;\\n    height: 80px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .influencers-slider[_ngcontent-%COMP%]   .influencer-card[_ngcontent-%COMP%] {\\n    flex: 0 0 170px;\\n    width: 170px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "Subscription", "IonicModule", "CarouselModule", "i0", "ɵɵelementStart", "ɵɵlistener", "TopFashionInfluencersComponent_div_1_Template_button_click_1_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "toggleSectionLike", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "TopFashionInfluencersComponent_div_1_Template_button_click_5_listener", "openComments", "TopFashionInfluencersComponent_div_1_Template_button_click_9_listener", "shareSection", "TopFashionInfluencersComponent_div_1_Template_button_click_13_listener", "toggleSectionBookmark", "TopFashionInfluencersComponent_div_1_Template_button_click_15_listener", "openMusicPlayer", "ɵɵadvance", "ɵɵclassProp", "isSectionLiked", "ɵɵproperty", "ɵɵtextInterpolate", "formatCount", "sectionLikes", "sectionComments", "isSectionBookmarked", "ɵɵtemplate", "TopFashionInfluencersComponent_div_9_div_2_Template", "ɵɵpureFunction0", "_c0", "TopFashionInfluencersComponent_div_10_Template_button_click_4_listener", "_r3", "onRetry", "error", "ɵɵtextInterpolate1", "brand_r7", "influencer_r6", "topBrands", "length", "TopFashionInfluencersComponent_div_11_div_7_Template_div_click_0_listener", "_r5", "$implicit", "onInfluencerClick", "TopFashionInfluencersComponent_div_11_div_7_div_3_Template", "TopFashionInfluencersComponent_div_11_div_7_span_26_Template", "TopFashionInfluencersComponent_div_11_div_7_span_27_Template", "TopFashionInfluencersComponent_div_11_div_7_Template_button_click_28_listener", "$event", "onFollowInfluencer", "avatar", "ɵɵsanitizeUrl", "fullName", "isVerified", "username", "category", "formatFollowerCount", "followerCount", "engagementRate", "slice", "isFollowing", "TopFashionInfluencersComponent_div_11_Template_button_click_1_listener", "_r4", "slidePrev", "TopFashionInfluencersComponent_div_11_Template_button_click_3_listener", "slideNext", "TopFashionInfluencersComponent_div_11_Template_div_mouseenter_5_listener", "pauseAutoSlide", "TopFashionInfluencersComponent_div_11_Template_div_mouseleave_5_listener", "resumeAutoSlide", "TopFashionInfluencersComponent_div_11_div_7_Template", "currentSlide", "maxSlide", "ɵɵstyleProp", "slideOffset", "topInfluencers", "trackByInfluencerId", "TopFashionInfluencersComponent", "constructor", "router", "isLoading", "subscription", "<PERSON><PERSON><PERSON><PERSON>", "visibleCards", "autoSlideDelay", "isAutoSliding", "isPaused", "isMobile", "ngOnInit", "loadTopInfluencers", "updateResponsiveSettings", "setupResizeListener", "checkMobileDevice", "ngOnDestroy", "unsubscribe", "stopAutoSlide", "_this", "_asyncToGenerator", "id", "recentPosts", "updateSliderOnInfluencersLoad", "console", "influencer", "navigate", "event", "stopPropagation", "count", "toFixed", "toString", "index", "startAutoSlide", "autoSlideInterval", "setInterval", "autoSlideNext", "clearInterval", "updateSlideOffset", "width", "window", "innerWidth", "updateSliderLimits", "addEventListener", "Math", "max", "restartAutoSlideAfterInteraction", "setTimeout", "log", "navigator", "share", "title", "text", "url", "location", "href", "clipboard", "writeText", "ɵɵdirectiveInject", "i1", "Router", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "TopFashionInfluencersComponent_Template", "rf", "ctx", "TopFashionInfluencersComponent_div_1_Template", "TopFashionInfluencersComponent_div_9_Template", "TopFashionInfluencersComponent_div_10_Template", "TopFashionInfluencersComponent_div_11_Template", "TopFashionInfluencersComponent_div_12_Template", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i3", "IonIcon", "styles"], "sources": ["E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\features\\home\\components\\top-fashion-influencers\\top-fashion-influencers.component.ts", "E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\features\\home\\components\\top-fashion-influencers\\top-fashion-influencers.component.html"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { Router } from '@angular/router';\r\nimport { Subscription } from 'rxjs';\r\nimport { IonicModule } from '@ionic/angular';\r\nimport { CarouselModule } from 'ngx-owl-carousel-o';\r\n\r\ninterface TopInfluencer {\r\n  id: string;\r\n  username: string;\r\n  fullName: string;\r\n  avatar: string;\r\n  followerCount: number;\r\n  category: string;\r\n  isVerified: boolean;\r\n  isFollowing: boolean;\r\n  engagementRate: number;\r\n  recentPosts: number;\r\n  topBrands: string[];\r\n}\r\n\r\n@Component({\r\n  selector: 'app-top-fashion-influencers',\r\n  standalone: true,\r\n  imports: [CommonModule, IonicModule, CarouselModule],\r\n  templateUrl: './top-fashion-influencers.component.html',\r\n  styleUrls: ['./top-fashion-influencers.component.scss']\r\n})\r\nexport class TopFashionInfluencersComponent implements OnInit, OnDestroy {\r\n  topInfluencers: TopInfluencer[] = [];\r\n  isLoading = true;\r\n  error: string | null = null;\r\n  private subscription: Subscription = new Subscription();\r\n\r\n  // Slider properties\r\n  currentSlide = 0;\r\n  slideOffset = 0;\r\n  cardWidth = 240; // Width of each influencer card including margin\r\n  visibleCards = 3; // Number of cards visible at once\r\n  maxSlide = 0;\r\n  \r\n  // Auto-sliding properties\r\n  autoSlideInterval: any;\r\n  autoSlideDelay = 6000; // 6 seconds for influencers\r\n  isAutoSliding = true;\r\n  isPaused = false;\r\n\r\n  // Section interaction properties\r\n  isSectionLiked = false;\r\n  isSectionBookmarked = false;\r\n  sectionLikes = 512;\r\n  sectionComments = 234;\r\n  isMobile = false;\r\n\r\n  constructor(private router: Router) {}\r\n\r\n  ngOnInit() {\r\n    this.loadTopInfluencers();\r\n    this.updateResponsiveSettings();\r\n    this.setupResizeListener();\r\n    this.checkMobileDevice();\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.subscription.unsubscribe();\r\n    this.stopAutoSlide();\r\n  }\r\n\r\n  private async loadTopInfluencers() {\r\n    try {\r\n      this.isLoading = true;\r\n      this.error = null;\r\n      \r\n      // Mock data for top fashion influencers\r\n      this.topInfluencers = [\r\n        {\r\n          id: '1',\r\n          username: 'fashionista_queen',\r\n          fullName: 'Priya Sharma',\r\n          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=150&h=150&fit=crop&crop=face',\r\n          followerCount: 2500000,\r\n          category: 'High Fashion',\r\n          isVerified: true,\r\n          isFollowing: false,\r\n          engagementRate: 8.5,\r\n          recentPosts: 24,\r\n          topBrands: ['Gucci', 'Prada', 'Versace']\r\n        },\r\n        {\r\n          id: '2',\r\n          username: 'street_style_king',\r\n          fullName: 'Arjun Kapoor',\r\n          avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',\r\n          followerCount: 1800000,\r\n          category: 'Streetwear',\r\n          isVerified: true,\r\n          isFollowing: false,\r\n          engagementRate: 12.3,\r\n          recentPosts: 18,\r\n          topBrands: ['Nike', 'Adidas', 'Supreme']\r\n        },\r\n        {\r\n          id: '3',\r\n          username: 'boho_goddess',\r\n          fullName: 'Ananya Singh',\r\n          avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',\r\n          followerCount: 1200000,\r\n          category: 'Boho Chic',\r\n          isVerified: true,\r\n          isFollowing: false,\r\n          engagementRate: 9.7,\r\n          recentPosts: 32,\r\n          topBrands: ['Free People', 'Anthropologie', 'Zara']\r\n        },\r\n        {\r\n          id: '4',\r\n          username: 'luxury_lifestyle',\r\n          fullName: 'Kavya Reddy',\r\n          avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face',\r\n          followerCount: 3200000,\r\n          category: 'Luxury',\r\n          isVerified: true,\r\n          isFollowing: false,\r\n          engagementRate: 6.8,\r\n          recentPosts: 15,\r\n          topBrands: ['Chanel', 'Dior', 'Louis Vuitton']\r\n        },\r\n        {\r\n          id: '5',\r\n          username: 'minimalist_maven',\r\n          fullName: 'Ravi Kumar',\r\n          avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',\r\n          followerCount: 950000,\r\n          category: 'Minimalist',\r\n          isVerified: true,\r\n          isFollowing: false,\r\n          engagementRate: 11.2,\r\n          recentPosts: 21,\r\n          topBrands: ['COS', 'Uniqlo', 'Everlane']\r\n        },\r\n        {\r\n          id: '6',\r\n          username: 'vintage_vibes',\r\n          fullName: 'Meera Patel',\r\n          avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face',\r\n          followerCount: 780000,\r\n          category: 'Vintage',\r\n          isVerified: true,\r\n          isFollowing: false,\r\n          engagementRate: 13.5,\r\n          recentPosts: 28,\r\n          topBrands: ['Vintage Stores', 'Thrift Finds', 'Custom']\r\n        }\r\n      ];\r\n      \r\n      this.isLoading = false;\r\n      this.updateSliderOnInfluencersLoad();\r\n    } catch (error) {\r\n      console.error('Error loading top influencers:', error);\r\n      this.error = 'Failed to load top influencers';\r\n      this.isLoading = false;\r\n    }\r\n  }\r\n\r\n  onInfluencerClick(influencer: TopInfluencer) {\r\n    this.router.navigate(['/profile', influencer.username]);\r\n  }\r\n\r\n  onFollowInfluencer(influencer: TopInfluencer, event: Event) {\r\n    event.stopPropagation();\r\n    influencer.isFollowing = !influencer.isFollowing;\r\n    \r\n    if (influencer.isFollowing) {\r\n      influencer.followerCount++;\r\n    } else {\r\n      influencer.followerCount--;\r\n    }\r\n  }\r\n\r\n  formatFollowerCount(count: number): string {\r\n    if (count >= 1000000) {\r\n      return (count / 1000000).toFixed(1) + 'M';\r\n    } else if (count >= 1000) {\r\n      return (count / 1000).toFixed(1) + 'K';\r\n    }\r\n    return count.toString();\r\n  }\r\n\r\n  onRetry() {\r\n    this.loadTopInfluencers();\r\n  }\r\n\r\n  trackByInfluencerId(index: number, influencer: TopInfluencer): string {\r\n    return influencer.id;\r\n  }\r\n\r\n  // Auto-sliding methods\r\n  private startAutoSlide() {\r\n    if (!this.isAutoSliding || this.isPaused) return;\r\n    \r\n    this.stopAutoSlide();\r\n    this.autoSlideInterval = setInterval(() => {\r\n      if (!this.isPaused && this.topInfluencers.length > this.visibleCards) {\r\n        this.autoSlideNext();\r\n      }\r\n    }, this.autoSlideDelay);\r\n  }\r\n\r\n  private stopAutoSlide() {\r\n    if (this.autoSlideInterval) {\r\n      clearInterval(this.autoSlideInterval);\r\n      this.autoSlideInterval = null;\r\n    }\r\n  }\r\n\r\n  private autoSlideNext() {\r\n    if (this.currentSlide >= this.maxSlide) {\r\n      this.currentSlide = 0;\r\n    } else {\r\n      this.currentSlide++;\r\n    }\r\n    this.updateSlideOffset();\r\n  }\r\n\r\n  pauseAutoSlide() {\r\n    this.isPaused = true;\r\n    this.stopAutoSlide();\r\n  }\r\n\r\n  resumeAutoSlide() {\r\n    this.isPaused = false;\r\n    this.startAutoSlide();\r\n  }\r\n\r\n  // Responsive methods\r\n  private updateResponsiveSettings() {\r\n    const width = window.innerWidth;\r\n    if (width <= 480) {\r\n      this.cardWidth = 200;\r\n      this.visibleCards = 1;\r\n    } else if (width <= 768) {\r\n      this.cardWidth = 220;\r\n      this.visibleCards = 2;\r\n    } else if (width <= 1200) {\r\n      this.cardWidth = 240;\r\n      this.visibleCards = 2;\r\n    } else {\r\n      this.cardWidth = 260;\r\n      this.visibleCards = 3;\r\n    }\r\n    this.updateSliderLimits();\r\n    this.updateSlideOffset();\r\n  }\r\n\r\n  private setupResizeListener() {\r\n    window.addEventListener('resize', () => {\r\n      this.updateResponsiveSettings();\r\n    });\r\n  }\r\n\r\n  // Slider methods\r\n  updateSliderLimits() {\r\n    this.maxSlide = Math.max(0, this.topInfluencers.length - this.visibleCards);\r\n  }\r\n\r\n  slidePrev() {\r\n    if (this.currentSlide > 0) {\r\n      this.currentSlide--;\r\n      this.updateSlideOffset();\r\n      this.restartAutoSlideAfterInteraction();\r\n    }\r\n  }\r\n\r\n  slideNext() {\r\n    if (this.currentSlide < this.maxSlide) {\r\n      this.currentSlide++;\r\n      this.updateSlideOffset();\r\n      this.restartAutoSlideAfterInteraction();\r\n    }\r\n  }\r\n\r\n  private updateSlideOffset() {\r\n    this.slideOffset = -this.currentSlide * this.cardWidth;\r\n  }\r\n\r\n  private restartAutoSlideAfterInteraction() {\r\n    this.stopAutoSlide();\r\n    setTimeout(() => {\r\n      this.startAutoSlide();\r\n    }, 2000);\r\n  }\r\n\r\n  // Update slider when influencers load\r\n  private updateSliderOnInfluencersLoad() {\r\n    setTimeout(() => {\r\n      this.updateSliderLimits();\r\n      this.currentSlide = 0;\r\n      this.slideOffset = 0;\r\n      this.startAutoSlide();\r\n    }, 100);\r\n  }\r\n\r\n  // Section interaction methods\r\n  toggleSectionLike() {\r\n    this.isSectionLiked = !this.isSectionLiked;\r\n    if (this.isSectionLiked) {\r\n      this.sectionLikes++;\r\n    } else {\r\n      this.sectionLikes--;\r\n    }\r\n  }\r\n\r\n  toggleSectionBookmark() {\r\n    this.isSectionBookmarked = !this.isSectionBookmarked;\r\n  }\r\n\r\n  openComments() {\r\n    console.log('Opening comments for top fashion influencers section');\r\n  }\r\n\r\n  shareSection() {\r\n    if (navigator.share) {\r\n      navigator.share({\r\n        title: 'Top Fashion Influencers',\r\n        text: 'Follow the top fashion trendsetters!',\r\n        url: window.location.href\r\n      });\r\n    } else {\r\n      navigator.clipboard.writeText(window.location.href);\r\n      console.log('Link copied to clipboard');\r\n    }\r\n  }\r\n\r\n  openMusicPlayer() {\r\n    console.log('Opening music player for top fashion influencers');\r\n  }\r\n\r\n  formatCount(count: number): string {\r\n    if (count >= 1000000) {\r\n      return (count / 1000000).toFixed(1) + 'M';\r\n    } else if (count >= 1000) {\r\n      return (count / 1000).toFixed(1) + 'K';\r\n    }\r\n    return count.toString();\r\n  }\r\n\r\n  private checkMobileDevice() {\r\n    this.isMobile = window.innerWidth <= 768;\r\n  }\r\n}\r\n", "<div class=\"top-influencers-container\">\r\n  <!-- Mobile Action Buttons (TikTok/Instagram Style) -->\r\n  <div class=\"mobile-action-buttons\" *ngIf=\"isMobile\">\r\n    <button class=\"action-btn like-btn\"\r\n            [class.active]=\"isSectionLiked\"\r\n            (click)=\"toggleSectionLike()\">\r\n      <ion-icon [name]=\"isSectionLiked ? 'heart' : 'heart-outline'\"></ion-icon>\r\n      <span class=\"action-count\">{{ formatCount(sectionLikes) }}</span>\r\n    </button>\r\n\r\n    <button class=\"action-btn comment-btn\" (click)=\"openComments()\">\r\n      <ion-icon name=\"chatbubble-outline\"></ion-icon>\r\n      <span class=\"action-count\">{{ formatCount(sectionComments) }}</span>\r\n    </button>\r\n\r\n    <button class=\"action-btn share-btn\" (click)=\"shareSection()\">\r\n      <ion-icon name=\"arrow-redo-outline\"></ion-icon>\r\n      <span class=\"action-text\">Share</span>\r\n    </button>\r\n\r\n    <button class=\"action-btn bookmark-btn\"\r\n            [class.active]=\"isSectionBookmarked\"\r\n            (click)=\"toggleSectionBookmark()\">\r\n      <ion-icon [name]=\"isSectionBookmarked ? 'bookmark' : 'bookmark-outline'\"></ion-icon>\r\n    </button>\r\n\r\n    <button class=\"action-btn music-btn\" (click)=\"openMusicPlayer()\">\r\n      <ion-icon name=\"musical-notes\"></ion-icon>\r\n      <span class=\"action-text\">Music</span>\r\n    </button>\r\n  </div>\r\n\r\n  <!-- Header -->\r\n  <div class=\"section-header\">\r\n    <div class=\"header-content\">\r\n      <h2 class=\"section-title\">\r\n        <ion-icon name=\"star\" class=\"title-icon\"></ion-icon>\r\n        Top Fashion Influencers\r\n      </h2>\r\n      <p class=\"section-subtitle\">Follow the trendsetters</p>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Loading State -->\r\n  <div *ngIf=\"isLoading\" class=\"loading-container\">\r\n    <div class=\"loading-grid\">\r\n      <div *ngFor=\"let item of [1,2,3]\" class=\"loading-influencer-card\">\r\n        <div class=\"loading-avatar\"></div>\r\n        <div class=\"loading-content\">\r\n          <div class=\"loading-line short\"></div>\r\n          <div class=\"loading-line medium\"></div>\r\n          <div class=\"loading-line long\"></div>\r\n          <div class=\"loading-stats\">\r\n            <div class=\"loading-stat\"></div>\r\n            <div class=\"loading-stat\"></div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Error State -->\r\n  <div *ngIf=\"error && !isLoading\" class=\"error-container\">\r\n    <ion-icon name=\"alert-circle\" class=\"error-icon\"></ion-icon>\r\n    <p class=\"error-message\">{{ error }}</p>\r\n    <button class=\"retry-btn\" (click)=\"onRetry()\">\r\n      <ion-icon name=\"refresh\"></ion-icon>\r\n      Try Again\r\n    </button>\r\n  </div>\r\n\r\n  <!-- Influencers Slider -->\r\n  <div *ngIf=\"!isLoading && !error && topInfluencers.length > 0\" class=\"influencers-slider-container\">\r\n    <!-- Navigation Buttons -->\r\n    <button class=\"slider-nav prev-btn\" (click)=\"slidePrev()\" [disabled]=\"currentSlide === 0\">\r\n      <ion-icon name=\"chevron-back\"></ion-icon>\r\n    </button>\r\n    <button class=\"slider-nav next-btn\" (click)=\"slideNext()\" [disabled]=\"currentSlide >= maxSlide\">\r\n      <ion-icon name=\"chevron-forward\"></ion-icon>\r\n    </button>\r\n    \r\n    <!-- Slider Wrapper -->\r\n    <div class=\"influencers-slider-wrapper\" (mouseenter)=\"pauseAutoSlide()\" (mouseleave)=\"resumeAutoSlide()\">\r\n      <div class=\"influencers-slider\" [style.transform]=\"'translateX(' + slideOffset + 'px)'\">\r\n        <div \r\n          *ngFor=\"let influencer of topInfluencers; trackBy: trackByInfluencerId\" \r\n          class=\"influencer-card\"\r\n          (click)=\"onInfluencerClick(influencer)\"\r\n        >\r\n          <!-- Influencer Avatar -->\r\n          <div class=\"influencer-avatar-container\">\r\n            <img \r\n              [src]=\"influencer.avatar\"\r\n              [alt]=\"influencer.fullName\"\r\n              class=\"influencer-avatar\"\r\n              loading=\"lazy\"\r\n            />\r\n            <div *ngIf=\"influencer.isVerified\" class=\"verified-badge\">\r\n              <ion-icon name=\"checkmark\"></ion-icon>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Influencer Info -->\r\n          <div class=\"influencer-info\">\r\n            <h3 class=\"influencer-name\">{{ influencer.fullName }}</h3>\r\n            <p class=\"username\">&#64;{{ influencer.username }}</p>\r\n            <p class=\"category\">{{ influencer.category }}</p>\r\n            \r\n            <!-- Stats -->\r\n            <div class=\"stats-container\">\r\n              <div class=\"stat\">\r\n                <span class=\"stat-value\">{{ formatFollowerCount(influencer.followerCount) }}</span>\r\n                <span class=\"stat-label\">Followers</span>\r\n              </div>\r\n              <div class=\"stat\">\r\n                <span class=\"stat-value\">{{ influencer.engagementRate }}%</span>\r\n                <span class=\"stat-label\">Engagement</span>\r\n              </div>\r\n            </div>\r\n            \r\n            <!-- Top Brands -->\r\n            <div class=\"top-brands\">\r\n              <span class=\"brands-label\">Works with:</span>\r\n              <div class=\"brands-list\">\r\n                <span *ngFor=\"let brand of influencer.topBrands.slice(0, 2)\" class=\"brand-tag\">\r\n                  {{ brand }}\r\n                </span>\r\n                <span *ngIf=\"influencer.topBrands.length > 2\" class=\"more-brands\">\r\n                  +{{ influencer.topBrands.length - 2 }}\r\n                </span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Follow Button -->\r\n          <button \r\n            class=\"follow-btn\"\r\n            [class.following]=\"influencer.isFollowing\"\r\n            (click)=\"onFollowInfluencer(influencer, $event)\"\r\n          >\r\n            <span>{{ influencer.isFollowing ? 'Following' : 'Follow' }}</span>\r\n            <ion-icon [name]=\"influencer.isFollowing ? 'checkmark' : 'add'\"></ion-icon>\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div> <!-- End influencers-slider-wrapper -->\r\n  </div> <!-- End influencers-slider-container -->\r\n\r\n  <!-- Empty State -->\r\n  <div *ngIf=\"!isLoading && !error && topInfluencers.length === 0\" class=\"empty-container\">\r\n    <ion-icon name=\"star-outline\" class=\"empty-icon\"></ion-icon>\r\n    <h3 class=\"empty-title\">No Influencers</h3>\r\n    <p class=\"empty-message\">Check back later for top fashion influencers</p>\r\n  </div>\r\n</div>\r\n"], "mappings": ";AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,YAAY,QAAQ,MAAM;AACnC,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,cAAc,QAAQ,oBAAoB;;;;;;;;;ICF/CC,EADF,CAAAC,cAAA,cAAoD,iBAGZ;IAA9BD,EAAA,CAAAE,UAAA,mBAAAC,sEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,iBAAA,EAAmB;IAAA,EAAC;IACnCT,EAAA,CAAAU,SAAA,mBAAyE;IACzEV,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAW,MAAA,GAA+B;IAC5DX,EAD4D,CAAAY,YAAA,EAAO,EAC1D;IAETZ,EAAA,CAAAC,cAAA,iBAAgE;IAAzBD,EAAA,CAAAE,UAAA,mBAAAW,sEAAA;MAAAb,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAQ,YAAA,EAAc;IAAA,EAAC;IAC7Dd,EAAA,CAAAU,SAAA,mBAA+C;IAC/CV,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAW,MAAA,GAAkC;IAC/DX,EAD+D,CAAAY,YAAA,EAAO,EAC7D;IAETZ,EAAA,CAAAC,cAAA,iBAA8D;IAAzBD,EAAA,CAAAE,UAAA,mBAAAa,sEAAA;MAAAf,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAU,YAAA,EAAc;IAAA,EAAC;IAC3DhB,EAAA,CAAAU,SAAA,oBAA+C;IAC/CV,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAW,MAAA,aAAK;IACjCX,EADiC,CAAAY,YAAA,EAAO,EAC/B;IAETZ,EAAA,CAAAC,cAAA,kBAE0C;IAAlCD,EAAA,CAAAE,UAAA,mBAAAe,uEAAA;MAAAjB,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAY,qBAAA,EAAuB;IAAA,EAAC;IACvClB,EAAA,CAAAU,SAAA,oBAAoF;IACtFV,EAAA,CAAAY,YAAA,EAAS;IAETZ,EAAA,CAAAC,cAAA,kBAAiE;IAA5BD,EAAA,CAAAE,UAAA,mBAAAiB,uEAAA;MAAAnB,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAc,eAAA,EAAiB;IAAA,EAAC;IAC9DpB,EAAA,CAAAU,SAAA,oBAA0C;IAC1CV,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAW,MAAA,aAAK;IAEnCX,EAFmC,CAAAY,YAAA,EAAO,EAC/B,EACL;;;;IA1BIZ,EAAA,CAAAqB,SAAA,EAA+B;IAA/BrB,EAAA,CAAAsB,WAAA,WAAAhB,MAAA,CAAAiB,cAAA,CAA+B;IAE3BvB,EAAA,CAAAqB,SAAA,EAAmD;IAAnDrB,EAAA,CAAAwB,UAAA,SAAAlB,MAAA,CAAAiB,cAAA,6BAAmD;IAClCvB,EAAA,CAAAqB,SAAA,GAA+B;IAA/BrB,EAAA,CAAAyB,iBAAA,CAAAnB,MAAA,CAAAoB,WAAA,CAAApB,MAAA,CAAAqB,YAAA,EAA+B;IAK/B3B,EAAA,CAAAqB,SAAA,GAAkC;IAAlCrB,EAAA,CAAAyB,iBAAA,CAAAnB,MAAA,CAAAoB,WAAA,CAAApB,MAAA,CAAAsB,eAAA,EAAkC;IASvD5B,EAAA,CAAAqB,SAAA,GAAoC;IAApCrB,EAAA,CAAAsB,WAAA,WAAAhB,MAAA,CAAAuB,mBAAA,CAAoC;IAEhC7B,EAAA,CAAAqB,SAAA,EAA8D;IAA9DrB,EAAA,CAAAwB,UAAA,SAAAlB,MAAA,CAAAuB,mBAAA,mCAA8D;;;;;IAuBxE7B,EAAA,CAAAC,cAAA,cAAkE;IAChED,EAAA,CAAAU,SAAA,cAAkC;IAClCV,EAAA,CAAAC,cAAA,cAA6B;IAG3BD,EAFA,CAAAU,SAAA,cAAsC,cACC,cACF;IACrCV,EAAA,CAAAC,cAAA,cAA2B;IAEzBD,EADA,CAAAU,SAAA,cAAgC,cACA;IAGtCV,EAFI,CAAAY,YAAA,EAAM,EACF,EACF;;;;;IAZRZ,EADF,CAAAC,cAAA,cAAiD,cACrB;IACxBD,EAAA,CAAA8B,UAAA,IAAAC,mDAAA,kBAAkE;IAatE/B,EADE,CAAAY,YAAA,EAAM,EACF;;;IAboBZ,EAAA,CAAAqB,SAAA,GAAU;IAAVrB,EAAA,CAAAwB,UAAA,YAAAxB,EAAA,CAAAgC,eAAA,IAAAC,GAAA,EAAU;;;;;;IAgBpCjC,EAAA,CAAAC,cAAA,cAAyD;IACvDD,EAAA,CAAAU,SAAA,mBAA4D;IAC5DV,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAW,MAAA,GAAW;IAAAX,EAAA,CAAAY,YAAA,EAAI;IACxCZ,EAAA,CAAAC,cAAA,iBAA8C;IAApBD,EAAA,CAAAE,UAAA,mBAAAgC,uEAAA;MAAAlC,EAAA,CAAAI,aAAA,CAAA+B,GAAA;MAAA,MAAA7B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA8B,OAAA,EAAS;IAAA,EAAC;IAC3CpC,EAAA,CAAAU,SAAA,mBAAoC;IACpCV,EAAA,CAAAW,MAAA,kBACF;IACFX,EADE,CAAAY,YAAA,EAAS,EACL;;;;IALqBZ,EAAA,CAAAqB,SAAA,GAAW;IAAXrB,EAAA,CAAAyB,iBAAA,CAAAnB,MAAA,CAAA+B,KAAA,CAAW;;;;;IAiC5BrC,EAAA,CAAAC,cAAA,cAA0D;IACxDD,EAAA,CAAAU,SAAA,mBAAsC;IACxCV,EAAA,CAAAY,YAAA,EAAM;;;;;IAyBFZ,EAAA,CAAAC,cAAA,eAA+E;IAC7ED,EAAA,CAAAW,MAAA,GACF;IAAAX,EAAA,CAAAY,YAAA,EAAO;;;;IADLZ,EAAA,CAAAqB,SAAA,EACF;IADErB,EAAA,CAAAsC,kBAAA,MAAAC,QAAA,MACF;;;;;IACAvC,EAAA,CAAAC,cAAA,eAAkE;IAChED,EAAA,CAAAW,MAAA,GACF;IAAAX,EAAA,CAAAY,YAAA,EAAO;;;;IADLZ,EAAA,CAAAqB,SAAA,EACF;IADErB,EAAA,CAAAsC,kBAAA,OAAAE,aAAA,CAAAC,SAAA,CAAAC,MAAA,UACF;;;;;;IA7CR1C,EAAA,CAAAC,cAAA,cAIC;IADCD,EAAA,CAAAE,UAAA,mBAAAyC,0EAAA;MAAA,MAAAH,aAAA,GAAAxC,EAAA,CAAAI,aAAA,CAAAwC,GAAA,EAAAC,SAAA;MAAA,MAAAvC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAwC,iBAAA,CAAAN,aAAA,CAA6B;IAAA,EAAC;IAGvCxC,EAAA,CAAAC,cAAA,cAAyC;IACvCD,EAAA,CAAAU,SAAA,cAKE;IACFV,EAAA,CAAA8B,UAAA,IAAAiB,0DAAA,kBAA0D;IAG5D/C,EAAA,CAAAY,YAAA,EAAM;IAIJZ,EADF,CAAAC,cAAA,cAA6B,aACC;IAAAD,EAAA,CAAAW,MAAA,GAAyB;IAAAX,EAAA,CAAAY,YAAA,EAAK;IAC1DZ,EAAA,CAAAC,cAAA,YAAoB;IAAAD,EAAA,CAAAW,MAAA,GAA8B;IAAAX,EAAA,CAAAY,YAAA,EAAI;IACtDZ,EAAA,CAAAC,cAAA,YAAoB;IAAAD,EAAA,CAAAW,MAAA,IAAyB;IAAAX,EAAA,CAAAY,YAAA,EAAI;IAK7CZ,EAFJ,CAAAC,cAAA,eAA6B,eACT,gBACS;IAAAD,EAAA,CAAAW,MAAA,IAAmD;IAAAX,EAAA,CAAAY,YAAA,EAAO;IACnFZ,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAW,MAAA,iBAAS;IACpCX,EADoC,CAAAY,YAAA,EAAO,EACrC;IAEJZ,EADF,CAAAC,cAAA,eAAkB,gBACS;IAAAD,EAAA,CAAAW,MAAA,IAAgC;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAChEZ,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAW,MAAA,kBAAU;IAEvCX,EAFuC,CAAAY,YAAA,EAAO,EACtC,EACF;IAIJZ,EADF,CAAAC,cAAA,eAAwB,gBACK;IAAAD,EAAA,CAAAW,MAAA,mBAAW;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAC7CZ,EAAA,CAAAC,cAAA,eAAyB;IAIvBD,EAHA,CAAA8B,UAAA,KAAAkB,4DAAA,mBAA+E,KAAAC,4DAAA,mBAGb;IAKxEjD,EAFI,CAAAY,YAAA,EAAM,EACF,EACF;IAGNZ,EAAA,CAAAC,cAAA,kBAIC;IADCD,EAAA,CAAAE,UAAA,mBAAAgD,8EAAAC,MAAA;MAAA,MAAAX,aAAA,GAAAxC,EAAA,CAAAI,aAAA,CAAAwC,GAAA,EAAAC,SAAA;MAAA,MAAAvC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA8C,kBAAA,CAAAZ,aAAA,EAAAW,MAAA,CAAsC;IAAA,EAAC;IAEhDnD,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAW,MAAA,IAAqD;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAClEZ,EAAA,CAAAU,SAAA,oBAA2E;IAE/EV,EADE,CAAAY,YAAA,EAAS,EACL;;;;;IAnDAZ,EAAA,CAAAqB,SAAA,GAAyB;IACzBrB,EADA,CAAAwB,UAAA,QAAAgB,aAAA,CAAAa,MAAA,EAAArD,EAAA,CAAAsD,aAAA,CAAyB,QAAAd,aAAA,CAAAe,QAAA,CACE;IAIvBvD,EAAA,CAAAqB,SAAA,EAA2B;IAA3BrB,EAAA,CAAAwB,UAAA,SAAAgB,aAAA,CAAAgB,UAAA,CAA2B;IAOLxD,EAAA,CAAAqB,SAAA,GAAyB;IAAzBrB,EAAA,CAAAyB,iBAAA,CAAAe,aAAA,CAAAe,QAAA,CAAyB;IACjCvD,EAAA,CAAAqB,SAAA,GAA8B;IAA9BrB,EAAA,CAAAsC,kBAAA,MAAAE,aAAA,CAAAiB,QAAA,KAA8B;IAC9BzD,EAAA,CAAAqB,SAAA,GAAyB;IAAzBrB,EAAA,CAAAyB,iBAAA,CAAAe,aAAA,CAAAkB,QAAA,CAAyB;IAKhB1D,EAAA,CAAAqB,SAAA,GAAmD;IAAnDrB,EAAA,CAAAyB,iBAAA,CAAAnB,MAAA,CAAAqD,mBAAA,CAAAnB,aAAA,CAAAoB,aAAA,EAAmD;IAInD5D,EAAA,CAAAqB,SAAA,GAAgC;IAAhCrB,EAAA,CAAAsC,kBAAA,KAAAE,aAAA,CAAAqB,cAAA,MAAgC;IASjC7D,EAAA,CAAAqB,SAAA,GAAmC;IAAnCrB,EAAA,CAAAwB,UAAA,YAAAgB,aAAA,CAAAC,SAAA,CAAAqB,KAAA,OAAmC;IAGpD9D,EAAA,CAAAqB,SAAA,EAAqC;IAArCrB,EAAA,CAAAwB,UAAA,SAAAgB,aAAA,CAAAC,SAAA,CAAAC,MAAA,KAAqC;IAUhD1C,EAAA,CAAAqB,SAAA,EAA0C;IAA1CrB,EAAA,CAAAsB,WAAA,cAAAkB,aAAA,CAAAuB,WAAA,CAA0C;IAGpC/D,EAAA,CAAAqB,SAAA,GAAqD;IAArDrB,EAAA,CAAAyB,iBAAA,CAAAe,aAAA,CAAAuB,WAAA,0BAAqD;IACjD/D,EAAA,CAAAqB,SAAA,EAAqD;IAArDrB,EAAA,CAAAwB,UAAA,SAAAgB,aAAA,CAAAuB,WAAA,uBAAqD;;;;;;IAnEvE/D,EAFF,CAAAC,cAAA,cAAoG,iBAER;IAAtDD,EAAA,CAAAE,UAAA,mBAAA8D,uEAAA;MAAAhE,EAAA,CAAAI,aAAA,CAAA6D,GAAA;MAAA,MAAA3D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA4D,SAAA,EAAW;IAAA,EAAC;IACvDlE,EAAA,CAAAU,SAAA,mBAAyC;IAC3CV,EAAA,CAAAY,YAAA,EAAS;IACTZ,EAAA,CAAAC,cAAA,iBAAgG;IAA5DD,EAAA,CAAAE,UAAA,mBAAAiE,uEAAA;MAAAnE,EAAA,CAAAI,aAAA,CAAA6D,GAAA;MAAA,MAAA3D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA8D,SAAA,EAAW;IAAA,EAAC;IACvDpE,EAAA,CAAAU,SAAA,mBAA4C;IAC9CV,EAAA,CAAAY,YAAA,EAAS;IAGTZ,EAAA,CAAAC,cAAA,cAAyG;IAAjCD,EAAhC,CAAAE,UAAA,wBAAAmE,yEAAA;MAAArE,EAAA,CAAAI,aAAA,CAAA6D,GAAA;MAAA,MAAA3D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAcF,MAAA,CAAAgE,cAAA,EAAgB;IAAA,EAAC,wBAAAC,yEAAA;MAAAvE,EAAA,CAAAI,aAAA,CAAA6D,GAAA;MAAA,MAAA3D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAeF,MAAA,CAAAkE,eAAA,EAAiB;IAAA,EAAC;IACtGxE,EAAA,CAAAC,cAAA,cAAwF;IACtFD,EAAA,CAAA8B,UAAA,IAAA2C,oDAAA,oBAIC;IA0DPzE,EAFI,CAAAY,YAAA,EAAM,EACF,EACF;;;;IAxEsDZ,EAAA,CAAAqB,SAAA,EAA+B;IAA/BrB,EAAA,CAAAwB,UAAA,aAAAlB,MAAA,CAAAoE,YAAA,OAA+B;IAG/B1E,EAAA,CAAAqB,SAAA,GAAqC;IAArCrB,EAAA,CAAAwB,UAAA,aAAAlB,MAAA,CAAAoE,YAAA,IAAApE,MAAA,CAAAqE,QAAA,CAAqC;IAM7D3E,EAAA,CAAAqB,SAAA,GAAuD;IAAvDrB,EAAA,CAAA4E,WAAA,8BAAAtE,MAAA,CAAAuE,WAAA,SAAuD;IAE5D7E,EAAA,CAAAqB,SAAA,EAAmB;IAAArB,EAAnB,CAAAwB,UAAA,YAAAlB,MAAA,CAAAwE,cAAA,CAAmB,iBAAAxE,MAAA,CAAAyE,mBAAA,CAA4B;;;;;IAgE9E/E,EAAA,CAAAC,cAAA,cAAyF;IACvFD,EAAA,CAAAU,SAAA,mBAA4D;IAC5DV,EAAA,CAAAC,cAAA,aAAwB;IAAAD,EAAA,CAAAW,MAAA,qBAAc;IAAAX,EAAA,CAAAY,YAAA,EAAK;IAC3CZ,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAW,MAAA,mDAA4C;IACvEX,EADuE,CAAAY,YAAA,EAAI,EACrE;;;AD7HR,OAAM,MAAOoE,8BAA8B;EA0BzCC,YAAoBC,MAAc;IAAd,KAAAA,MAAM,GAANA,MAAM;IAzB1B,KAAAJ,cAAc,GAAoB,EAAE;IACpC,KAAAK,SAAS,GAAG,IAAI;IAChB,KAAA9C,KAAK,GAAkB,IAAI;IACnB,KAAA+C,YAAY,GAAiB,IAAIvF,YAAY,EAAE;IAEvD;IACA,KAAA6E,YAAY,GAAG,CAAC;IAChB,KAAAG,WAAW,GAAG,CAAC;IACf,KAAAQ,SAAS,GAAG,GAAG,CAAC,CAAC;IACjB,KAAAC,YAAY,GAAG,CAAC,CAAC,CAAC;IAClB,KAAAX,QAAQ,GAAG,CAAC;IAIZ,KAAAY,cAAc,GAAG,IAAI,CAAC,CAAC;IACvB,KAAAC,aAAa,GAAG,IAAI;IACpB,KAAAC,QAAQ,GAAG,KAAK;IAEhB;IACA,KAAAlE,cAAc,GAAG,KAAK;IACtB,KAAAM,mBAAmB,GAAG,KAAK;IAC3B,KAAAF,YAAY,GAAG,GAAG;IAClB,KAAAC,eAAe,GAAG,GAAG;IACrB,KAAA8D,QAAQ,GAAG,KAAK;EAEqB;EAErCC,QAAQA,CAAA;IACN,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAACC,wBAAwB,EAAE;IAC/B,IAAI,CAACC,mBAAmB,EAAE;IAC1B,IAAI,CAACC,iBAAiB,EAAE;EAC1B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACZ,YAAY,CAACa,WAAW,EAAE;IAC/B,IAAI,CAACC,aAAa,EAAE;EACtB;EAEcN,kBAAkBA,CAAA;IAAA,IAAAO,KAAA;IAAA,OAAAC,iBAAA;MAC9B,IAAI;QACFD,KAAI,CAAChB,SAAS,GAAG,IAAI;QACrBgB,KAAI,CAAC9D,KAAK,GAAG,IAAI;QAEjB;QACA8D,KAAI,CAACrB,cAAc,GAAG,CACpB;UACEuB,EAAE,EAAE,GAAG;UACP5C,QAAQ,EAAE,mBAAmB;UAC7BF,QAAQ,EAAE,cAAc;UACxBF,MAAM,EAAE,6FAA6F;UACrGO,aAAa,EAAE,OAAO;UACtBF,QAAQ,EAAE,cAAc;UACxBF,UAAU,EAAE,IAAI;UAChBO,WAAW,EAAE,KAAK;UAClBF,cAAc,EAAE,GAAG;UACnByC,WAAW,EAAE,EAAE;UACf7D,SAAS,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,SAAS;SACxC,EACD;UACE4D,EAAE,EAAE,GAAG;UACP5C,QAAQ,EAAE,mBAAmB;UAC7BF,QAAQ,EAAE,cAAc;UACxBF,MAAM,EAAE,6FAA6F;UACrGO,aAAa,EAAE,OAAO;UACtBF,QAAQ,EAAE,YAAY;UACtBF,UAAU,EAAE,IAAI;UAChBO,WAAW,EAAE,KAAK;UAClBF,cAAc,EAAE,IAAI;UACpByC,WAAW,EAAE,EAAE;UACf7D,SAAS,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,SAAS;SACxC,EACD;UACE4D,EAAE,EAAE,GAAG;UACP5C,QAAQ,EAAE,cAAc;UACxBF,QAAQ,EAAE,cAAc;UACxBF,MAAM,EAAE,6FAA6F;UACrGO,aAAa,EAAE,OAAO;UACtBF,QAAQ,EAAE,WAAW;UACrBF,UAAU,EAAE,IAAI;UAChBO,WAAW,EAAE,KAAK;UAClBF,cAAc,EAAE,GAAG;UACnByC,WAAW,EAAE,EAAE;UACf7D,SAAS,EAAE,CAAC,aAAa,EAAE,eAAe,EAAE,MAAM;SACnD,EACD;UACE4D,EAAE,EAAE,GAAG;UACP5C,QAAQ,EAAE,kBAAkB;UAC5BF,QAAQ,EAAE,aAAa;UACvBF,MAAM,EAAE,0FAA0F;UAClGO,aAAa,EAAE,OAAO;UACtBF,QAAQ,EAAE,QAAQ;UAClBF,UAAU,EAAE,IAAI;UAChBO,WAAW,EAAE,KAAK;UAClBF,cAAc,EAAE,GAAG;UACnByC,WAAW,EAAE,EAAE;UACf7D,SAAS,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,eAAe;SAC9C,EACD;UACE4D,EAAE,EAAE,GAAG;UACP5C,QAAQ,EAAE,kBAAkB;UAC5BF,QAAQ,EAAE,YAAY;UACtBF,MAAM,EAAE,6FAA6F;UACrGO,aAAa,EAAE,MAAM;UACrBF,QAAQ,EAAE,YAAY;UACtBF,UAAU,EAAE,IAAI;UAChBO,WAAW,EAAE,KAAK;UAClBF,cAAc,EAAE,IAAI;UACpByC,WAAW,EAAE,EAAE;UACf7D,SAAS,EAAE,CAAC,KAAK,EAAE,QAAQ,EAAE,UAAU;SACxC,EACD;UACE4D,EAAE,EAAE,GAAG;UACP5C,QAAQ,EAAE,eAAe;UACzBF,QAAQ,EAAE,aAAa;UACvBF,MAAM,EAAE,6FAA6F;UACrGO,aAAa,EAAE,MAAM;UACrBF,QAAQ,EAAE,SAAS;UACnBF,UAAU,EAAE,IAAI;UAChBO,WAAW,EAAE,KAAK;UAClBF,cAAc,EAAE,IAAI;UACpByC,WAAW,EAAE,EAAE;UACf7D,SAAS,EAAE,CAAC,gBAAgB,EAAE,cAAc,EAAE,QAAQ;SACvD,CACF;QAED0D,KAAI,CAAChB,SAAS,GAAG,KAAK;QACtBgB,KAAI,CAACI,6BAA6B,EAAE;OACrC,CAAC,OAAOlE,KAAK,EAAE;QACdmE,OAAO,CAACnE,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD8D,KAAI,CAAC9D,KAAK,GAAG,gCAAgC;QAC7C8D,KAAI,CAAChB,SAAS,GAAG,KAAK;;IACvB;EACH;EAEArC,iBAAiBA,CAAC2D,UAAyB;IACzC,IAAI,CAACvB,MAAM,CAACwB,QAAQ,CAAC,CAAC,UAAU,EAAED,UAAU,CAAChD,QAAQ,CAAC,CAAC;EACzD;EAEAL,kBAAkBA,CAACqD,UAAyB,EAAEE,KAAY;IACxDA,KAAK,CAACC,eAAe,EAAE;IACvBH,UAAU,CAAC1C,WAAW,GAAG,CAAC0C,UAAU,CAAC1C,WAAW;IAEhD,IAAI0C,UAAU,CAAC1C,WAAW,EAAE;MAC1B0C,UAAU,CAAC7C,aAAa,EAAE;KAC3B,MAAM;MACL6C,UAAU,CAAC7C,aAAa,EAAE;;EAE9B;EAEAD,mBAAmBA,CAACkD,KAAa;IAC/B,IAAIA,KAAK,IAAI,OAAO,EAAE;MACpB,OAAO,CAACA,KAAK,GAAG,OAAO,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;KAC1C,MAAM,IAAID,KAAK,IAAI,IAAI,EAAE;MACxB,OAAO,CAACA,KAAK,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAExC,OAAOD,KAAK,CAACE,QAAQ,EAAE;EACzB;EAEA3E,OAAOA,CAAA;IACL,IAAI,CAACwD,kBAAkB,EAAE;EAC3B;EAEAb,mBAAmBA,CAACiC,KAAa,EAAEP,UAAyB;IAC1D,OAAOA,UAAU,CAACJ,EAAE;EACtB;EAEA;EACQY,cAAcA,CAAA;IACpB,IAAI,CAAC,IAAI,CAACzB,aAAa,IAAI,IAAI,CAACC,QAAQ,EAAE;IAE1C,IAAI,CAACS,aAAa,EAAE;IACpB,IAAI,CAACgB,iBAAiB,GAAGC,WAAW,CAAC,MAAK;MACxC,IAAI,CAAC,IAAI,CAAC1B,QAAQ,IAAI,IAAI,CAACX,cAAc,CAACpC,MAAM,GAAG,IAAI,CAAC4C,YAAY,EAAE;QACpE,IAAI,CAAC8B,aAAa,EAAE;;IAExB,CAAC,EAAE,IAAI,CAAC7B,cAAc,CAAC;EACzB;EAEQW,aAAaA,CAAA;IACnB,IAAI,IAAI,CAACgB,iBAAiB,EAAE;MAC1BG,aAAa,CAAC,IAAI,CAACH,iBAAiB,CAAC;MACrC,IAAI,CAACA,iBAAiB,GAAG,IAAI;;EAEjC;EAEQE,aAAaA,CAAA;IACnB,IAAI,IAAI,CAAC1C,YAAY,IAAI,IAAI,CAACC,QAAQ,EAAE;MACtC,IAAI,CAACD,YAAY,GAAG,CAAC;KACtB,MAAM;MACL,IAAI,CAACA,YAAY,EAAE;;IAErB,IAAI,CAAC4C,iBAAiB,EAAE;EAC1B;EAEAhD,cAAcA,CAAA;IACZ,IAAI,CAACmB,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACS,aAAa,EAAE;EACtB;EAEA1B,eAAeA,CAAA;IACb,IAAI,CAACiB,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACwB,cAAc,EAAE;EACvB;EAEA;EACQpB,wBAAwBA,CAAA;IAC9B,MAAM0B,KAAK,GAAGC,MAAM,CAACC,UAAU;IAC/B,IAAIF,KAAK,IAAI,GAAG,EAAE;MAChB,IAAI,CAAClC,SAAS,GAAG,GAAG;MACpB,IAAI,CAACC,YAAY,GAAG,CAAC;KACtB,MAAM,IAAIiC,KAAK,IAAI,GAAG,EAAE;MACvB,IAAI,CAAClC,SAAS,GAAG,GAAG;MACpB,IAAI,CAACC,YAAY,GAAG,CAAC;KACtB,MAAM,IAAIiC,KAAK,IAAI,IAAI,EAAE;MACxB,IAAI,CAAClC,SAAS,GAAG,GAAG;MACpB,IAAI,CAACC,YAAY,GAAG,CAAC;KACtB,MAAM;MACL,IAAI,CAACD,SAAS,GAAG,GAAG;MACpB,IAAI,CAACC,YAAY,GAAG,CAAC;;IAEvB,IAAI,CAACoC,kBAAkB,EAAE;IACzB,IAAI,CAACJ,iBAAiB,EAAE;EAC1B;EAEQxB,mBAAmBA,CAAA;IACzB0B,MAAM,CAACG,gBAAgB,CAAC,QAAQ,EAAE,MAAK;MACrC,IAAI,CAAC9B,wBAAwB,EAAE;IACjC,CAAC,CAAC;EACJ;EAEA;EACA6B,kBAAkBA,CAAA;IAChB,IAAI,CAAC/C,QAAQ,GAAGiD,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC/C,cAAc,CAACpC,MAAM,GAAG,IAAI,CAAC4C,YAAY,CAAC;EAC7E;EAEApB,SAASA,CAAA;IACP,IAAI,IAAI,CAACQ,YAAY,GAAG,CAAC,EAAE;MACzB,IAAI,CAACA,YAAY,EAAE;MACnB,IAAI,CAAC4C,iBAAiB,EAAE;MACxB,IAAI,CAACQ,gCAAgC,EAAE;;EAE3C;EAEA1D,SAASA,CAAA;IACP,IAAI,IAAI,CAACM,YAAY,GAAG,IAAI,CAACC,QAAQ,EAAE;MACrC,IAAI,CAACD,YAAY,EAAE;MACnB,IAAI,CAAC4C,iBAAiB,EAAE;MACxB,IAAI,CAACQ,gCAAgC,EAAE;;EAE3C;EAEQR,iBAAiBA,CAAA;IACvB,IAAI,CAACzC,WAAW,GAAG,CAAC,IAAI,CAACH,YAAY,GAAG,IAAI,CAACW,SAAS;EACxD;EAEQyC,gCAAgCA,CAAA;IACtC,IAAI,CAAC5B,aAAa,EAAE;IACpB6B,UAAU,CAAC,MAAK;MACd,IAAI,CAACd,cAAc,EAAE;IACvB,CAAC,EAAE,IAAI,CAAC;EACV;EAEA;EACQV,6BAA6BA,CAAA;IACnCwB,UAAU,CAAC,MAAK;MACd,IAAI,CAACL,kBAAkB,EAAE;MACzB,IAAI,CAAChD,YAAY,GAAG,CAAC;MACrB,IAAI,CAACG,WAAW,GAAG,CAAC;MACpB,IAAI,CAACoC,cAAc,EAAE;IACvB,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;EACAxG,iBAAiBA,CAAA;IACf,IAAI,CAACc,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAC1C,IAAI,IAAI,CAACA,cAAc,EAAE;MACvB,IAAI,CAACI,YAAY,EAAE;KACpB,MAAM;MACL,IAAI,CAACA,YAAY,EAAE;;EAEvB;EAEAT,qBAAqBA,CAAA;IACnB,IAAI,CAACW,mBAAmB,GAAG,CAAC,IAAI,CAACA,mBAAmB;EACtD;EAEAf,YAAYA,CAAA;IACV0F,OAAO,CAACwB,GAAG,CAAC,sDAAsD,CAAC;EACrE;EAEAhH,YAAYA,CAAA;IACV,IAAIiH,SAAS,CAACC,KAAK,EAAE;MACnBD,SAAS,CAACC,KAAK,CAAC;QACdC,KAAK,EAAE,yBAAyB;QAChCC,IAAI,EAAE,sCAAsC;QAC5CC,GAAG,EAAEb,MAAM,CAACc,QAAQ,CAACC;OACtB,CAAC;KACH,MAAM;MACLN,SAAS,CAACO,SAAS,CAACC,SAAS,CAACjB,MAAM,CAACc,QAAQ,CAACC,IAAI,CAAC;MACnD/B,OAAO,CAACwB,GAAG,CAAC,0BAA0B,CAAC;;EAE3C;EAEA5G,eAAeA,CAAA;IACboF,OAAO,CAACwB,GAAG,CAAC,kDAAkD,CAAC;EACjE;EAEAtG,WAAWA,CAACmF,KAAa;IACvB,IAAIA,KAAK,IAAI,OAAO,EAAE;MACpB,OAAO,CAACA,KAAK,GAAG,OAAO,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;KAC1C,MAAM,IAAID,KAAK,IAAI,IAAI,EAAE;MACxB,OAAO,CAACA,KAAK,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAExC,OAAOD,KAAK,CAACE,QAAQ,EAAE;EACzB;EAEQhB,iBAAiBA,CAAA;IACvB,IAAI,CAACL,QAAQ,GAAG8B,MAAM,CAACC,UAAU,IAAI,GAAG;EAC1C;;;uBAhUWzC,8BAA8B,EAAAhF,EAAA,CAAA0I,iBAAA,CAAAC,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAA9B5D,8BAA8B;MAAA6D,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA/I,EAAA,CAAAgJ,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC5B3CtJ,EAAA,CAAAC,cAAA,aAAuC;UAErCD,EAAA,CAAA8B,UAAA,IAAA0H,6CAAA,kBAAoD;UAiChDxJ,EAFJ,CAAAC,cAAA,aAA4B,aACE,YACA;UACxBD,EAAA,CAAAU,SAAA,kBAAoD;UACpDV,EAAA,CAAAW,MAAA,gCACF;UAAAX,EAAA,CAAAY,YAAA,EAAK;UACLZ,EAAA,CAAAC,cAAA,WAA4B;UAAAD,EAAA,CAAAW,MAAA,8BAAuB;UAEvDX,EAFuD,CAAAY,YAAA,EAAI,EACnD,EACF;UA4GNZ,EAzGA,CAAA8B,UAAA,IAAA2H,6CAAA,iBAAiD,KAAAC,8CAAA,iBAkBQ,KAAAC,8CAAA,iBAU2C,KAAAC,8CAAA,kBA6EX;UAK3F5J,EAAA,CAAAY,YAAA,EAAM;;;UAxJgCZ,EAAA,CAAAqB,SAAA,EAAc;UAAdrB,EAAA,CAAAwB,UAAA,SAAA+H,GAAA,CAAA7D,QAAA,CAAc;UA0C5C1F,EAAA,CAAAqB,SAAA,GAAe;UAAfrB,EAAA,CAAAwB,UAAA,SAAA+H,GAAA,CAAApE,SAAA,CAAe;UAkBfnF,EAAA,CAAAqB,SAAA,EAAyB;UAAzBrB,EAAA,CAAAwB,UAAA,SAAA+H,GAAA,CAAAlH,KAAA,KAAAkH,GAAA,CAAApE,SAAA,CAAyB;UAUzBnF,EAAA,CAAAqB,SAAA,EAAuD;UAAvDrB,EAAA,CAAAwB,UAAA,UAAA+H,GAAA,CAAApE,SAAA,KAAAoE,GAAA,CAAAlH,KAAA,IAAAkH,GAAA,CAAAzE,cAAA,CAAApC,MAAA,KAAuD;UA6EvD1C,EAAA,CAAAqB,SAAA,EAAyD;UAAzDrB,EAAA,CAAAwB,UAAA,UAAA+H,GAAA,CAAApE,SAAA,KAAAoE,GAAA,CAAAlH,KAAA,IAAAkH,GAAA,CAAAzE,cAAA,CAAApC,MAAA,OAAyD;;;qBD7HrD9C,YAAY,EAAAiK,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAEjK,WAAW,EAAAkK,EAAA,CAAAC,OAAA,EAAElK,cAAc;MAAAmK,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}