import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { environment } from '../../../environments/environment';
import { Product } from '../models/product.model';

export interface TrendingResponse {
  success: boolean;
  products: Product[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
}

export interface FeaturedBrand {
  brand: string;
  productCount: number;
  avgRating: number;
  totalViews: number;
  topProducts: Product[];
}

export interface FeaturedBrandsResponse {
  success: boolean;
  brands: FeaturedBrand[];
}

export interface Influencer {
  _id: string;
  username: string;
  fullName: string;
  avatar: string;
  bio: string;
  socialStats: {
    followersCount: number;
    postsCount: number;
    followingCount: number;
  };
  isInfluencer: boolean;
}

export interface InfluencersResponse {
  success: boolean;
  influencers: Influencer[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
}

@Injectable({
  providedIn: 'root'
})
export class TrendingService {
  private readonly API_URL = 'http://localhost:3001/api'; // Updated to correct port

  // BehaviorSubjects for caching
  private trendingProductsSubject = new BehaviorSubject<Product[]>([]);
  private suggestedProductsSubject = new BehaviorSubject<Product[]>([]);
  private newArrivalsSubject = new BehaviorSubject<Product[]>([]);
  private featuredBrandsSubject = new BehaviorSubject<FeaturedBrand[]>([]);
  private influencersSubject = new BehaviorSubject<Influencer[]>([]);

  // Public observables
  public trendingProducts$ = this.trendingProductsSubject.asObservable();
  public suggestedProducts$ = this.suggestedProductsSubject.asObservable();
  public newArrivals$ = this.newArrivalsSubject.asObservable();
  public featuredBrands$ = this.featuredBrandsSubject.asObservable();
  public influencers$ = this.influencersSubject.asObservable();

  constructor(private http: HttpClient) {}

  // Get trending products
  getTrendingProducts(page: number = 1, limit: number = 12): Observable<TrendingResponse> {
    return this.http.get<TrendingResponse>(`${this.API_URL}/v1/products/trending`, {
      params: { page: page.toString(), limit: limit.toString() }
    });
  }

  // Get suggested products
  getSuggestedProducts(page: number = 1, limit: number = 12): Observable<TrendingResponse> {
    return this.http.get<TrendingResponse>(`${this.API_URL}/v1/products/suggested`, {
      params: { page: page.toString(), limit: limit.toString() }
    });
  }

  // Get new arrivals
  getNewArrivals(page: number = 1, limit: number = 12): Observable<TrendingResponse> {
    return this.http.get<TrendingResponse>(`${this.API_URL}/v1/products/new-arrivals`, {
      params: { page: page.toString(), limit: limit.toString() }
    });
  }

  // Get featured brands
  getFeaturedBrands(): Observable<FeaturedBrandsResponse> {
    return this.http.get<FeaturedBrandsResponse>(`${this.API_URL}/v1/products/featured-brands`);
  }

  // Get influencers
  getInfluencers(page: number = 1, limit: number = 10): Observable<InfluencersResponse> {
    return this.http.get<InfluencersResponse>(`${this.API_URL}/v1/users/influencers`, {
      params: { page: page.toString(), limit: limit.toString() }
    });
  }

  // Load and cache trending products
  async loadTrendingProducts(page: number = 1, limit: number = 12): Promise<void> {
    try {
      const response = await this.getTrendingProducts(page, limit).toPromise();
      if (response?.success && response?.products) {
        this.trendingProductsSubject.next(response.products);
      }
    } catch (error) {
      console.error('Error loading trending products:', error);
      // Provide mock data as fallback
      const mockProducts: Product[] = [
        {
          id: 'tp1',
          name: 'Trending Sneakers',
          price: 5999,
          originalPrice: 7999,
          discount: 25,
          image: 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400&h=400&fit=crop',
          brand: 'Nike',
          rating: 4.7,
          reviews: 256,
          isNew: false,
          isTrending: true,
          category: 'Shoes',
          sizes: ['7', '8', '9', '10', '11'],
          colors: ['White', 'Black', 'Red'],
          description: 'Most popular sneakers this season'
        },
        {
          id: 'tp2',
          name: 'Stylish Handbag',
          price: 4999,
          originalPrice: 6999,
          discount: 29,
          image: 'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400&h=400&fit=crop',
          brand: 'Zara',
          rating: 4.4,
          reviews: 189,
          isNew: false,
          isTrending: true,
          category: 'Bags',
          sizes: ['One Size'],
          colors: ['Brown', 'Black', 'Tan'],
          description: 'Elegant handbag for every occasion'
        }
      ];
      this.trendingProductsSubject.next(mockProducts);
    }
  }

  // Load and cache suggested products
  async loadSuggestedProducts(page: number = 1, limit: number = 12): Promise<void> {
    try {
      const response = await this.getSuggestedProducts(page, limit).toPromise();
      if (response?.success && response?.products) {
        this.suggestedProductsSubject.next(response.products);
      }
    } catch (error) {
      console.error('Error loading suggested products:', error);
    }
  }

  // Load and cache new arrivals
  async loadNewArrivals(page: number = 1, limit: number = 12): Promise<void> {
    try {
      const response = await this.getNewArrivals(page, limit).toPromise();
      if (response?.success && response?.products) {
        this.newArrivalsSubject.next(response.products);
      }
    } catch (error) {
      console.error('Error loading new arrivals:', error);
      // Provide mock data as fallback
      const mockProducts: Product[] = [
        {
          id: 'na1',
          name: 'Summer Floral Dress',
          price: 2999,
          originalPrice: 3999,
          discount: 25,
          image: 'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=400&h=400&fit=crop',
          brand: 'Zara',
          rating: 4.5,
          reviews: 128,
          isNew: true,
          isTrending: false,
          category: 'Dresses',
          sizes: ['S', 'M', 'L', 'XL'],
          colors: ['Red', 'Blue', 'Green'],
          description: 'Beautiful summer floral dress perfect for any occasion'
        },
        {
          id: 'na2',
          name: 'Casual Denim Jacket',
          price: 3499,
          originalPrice: 4499,
          discount: 22,
          image: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=400&h=400&fit=crop',
          brand: 'H&M',
          rating: 4.3,
          reviews: 89,
          isNew: true,
          isTrending: false,
          category: 'Jackets',
          sizes: ['S', 'M', 'L', 'XL'],
          colors: ['Blue', 'Black'],
          description: 'Classic denim jacket for casual wear'
        }
      ];
      this.newArrivalsSubject.next(mockProducts);
    }
  }

  // Load and cache featured brands
  async loadFeaturedBrands(): Promise<void> {
    try {
      const response = await this.getFeaturedBrands().toPromise();
      if (response?.success && response?.brands) {
        this.featuredBrandsSubject.next(response.brands);
      }
    } catch (error) {
      console.error('Error loading featured brands:', error);
      // Provide mock data as fallback
      const mockBrands: FeaturedBrand[] = [
        {
          brand: 'Zara',
          logo: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=200&h=200&fit=crop',
          description: 'Fast fashion with trendy designs',
          productCount: 1250,
          avgRating: 4.3,
          totalViews: 125000,
          topProducts: []
        },
        {
          brand: 'H&M',
          logo: 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=200&h=200&fit=crop',
          description: 'Affordable fashion for everyone',
          productCount: 980,
          avgRating: 4.1,
          totalViews: 98000,
          topProducts: []
        },
        {
          brand: 'Nike',
          logo: 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=200&h=200&fit=crop',
          description: 'Just Do It - Athletic wear',
          productCount: 750,
          avgRating: 4.6,
          totalViews: 156000,
          topProducts: []
        },
        {
          brand: 'Adidas',
          logo: 'https://images.unsplash.com/photo-1556906781-9a412961c28c?w=200&h=200&fit=crop',
          description: 'Three stripes lifestyle',
          productCount: 680,
          avgRating: 4.4,
          totalViews: 134000,
          topProducts: []
        }
      ];
      this.featuredBrandsSubject.next(mockBrands);
    }
  }

  // Load and cache influencers
  async loadInfluencers(page: number = 1, limit: number = 10): Promise<void> {
    try {
      const response = await this.getInfluencers(page, limit).toPromise();
      if (response?.success && response?.influencers) {
        this.influencersSubject.next(response.influencers);
      }
    } catch (error) {
      console.error('Error loading influencers:', error);
      // Provide mock data as fallback
      const mockInfluencers: Influencer[] = [
        {
          id: 'inf1',
          username: 'fashionista_maya',
          fullName: 'Maya Chen',
          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=150&h=150&fit=crop&crop=face',
          followerCount: 125000,
          followingCount: 890,
          postCount: 456,
          bio: 'Fashion enthusiast & style blogger',
          isVerified: true,
          isFollowing: false,
          category: 'Fashion'
        },
        {
          id: 'inf2',
          username: 'style_guru_alex',
          fullName: 'Alex Rodriguez',
          avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
          followerCount: 89000,
          followingCount: 567,
          postCount: 234,
          bio: 'Street style & urban fashion',
          isVerified: true,
          isFollowing: false,
          category: 'Streetwear'
        }
      ];
      this.influencersSubject.next(mockInfluencers);
    }
  }

  // Clear all cached data
  clearCache(): void {
    this.trendingProductsSubject.next([]);
    this.suggestedProductsSubject.next([]);
    this.newArrivalsSubject.next([]);
    this.featuredBrandsSubject.next([]);
    this.influencersSubject.next([]);
  }

  // Get current cached data
  getCurrentTrendingProducts(): Product[] {
    return this.trendingProductsSubject.value;
  }

  getCurrentSuggestedProducts(): Product[] {
    return this.suggestedProductsSubject.value;
  }

  getCurrentNewArrivals(): Product[] {
    return this.newArrivalsSubject.value;
  }

  getCurrentFeaturedBrands(): FeaturedBrand[] {
    return this.featuredBrandsSubject.value;
  }

  getCurrentInfluencers(): Influencer[] {
    return this.influencersSubject.value;
  }
}
