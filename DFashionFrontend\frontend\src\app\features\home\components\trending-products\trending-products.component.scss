.trending-products-container {
  padding: 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 16px;
  margin-bottom: 24px;
  position: relative;
  max-width: 675px;
}

// Mobile Action Buttons (TikTok/Instagram Style)
.mobile-action-buttons {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  gap: 20px;
  z-index: 10;

  .action-btn {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    border: none;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    color: white;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;

    ion-icon {
      font-size: 24px;
      margin-bottom: 2px;
    }

    .action-count, .action-text {
      font-size: 10px;
      font-weight: 600;
      line-height: 1;
    }

    &:hover {
      transform: scale(1.1);
      background: rgba(255, 255, 255, 0.3);
    }

    &.active {
      background: rgba(255, 255, 255, 0.9);
      color: #ff6b35;

      &.like-btn {
        color: #ff3040;

        ion-icon {
          animation: heartBeat 0.6s ease-in-out;
        }
      }

      &.bookmark-btn {
        color: #ffd700;
      }
    }

    &.like-btn.active ion-icon {
      color: #ff3040;
    }

    &.comment-btn:hover {
      background: rgba(255, 255, 255, 0.3);
    }

    &.share-btn:hover {
      background: rgba(255, 255, 255, 0.3);
    }

    &.music-btn {
      background: linear-gradient(135deg, #ff3040 0%, #ff6b35 100%);

      &:hover {
        transform: scale(1.1) rotate(15deg);
      }
    }
  }
}

@keyframes heartBeat {
  0% { transform: scale(1); }
  25% { transform: scale(1.3); }
  50% { transform: scale(1.1); }
  75% { transform: scale(1.25); }
  100% { transform: scale(1); }
}

// Hide action buttons on desktop
@media (min-width: 769px) {
  .mobile-action-buttons {
    display: none;
  }
}

// Header Section
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  
  .header-content {
    flex: 1;
  }
  
  .section-title {
    font-size: 24px;
    font-weight: 700;
    color: #1a1a1a;
    margin: 0 0 8px 0;
    display: flex;
    align-items: center;
    gap: 12px;
    
    .title-icon {
      font-size: 28px;
      color: #ff6b35;
    }
  }
  
  .section-subtitle {
    font-size: 14px;
    color: #666;
    margin: 0;
  }
  
  .view-all-btn {
    background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 25px;
    font-weight: 600;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4);
    }
    
    ion-icon {
      font-size: 16px;
    }
  }
}

// Loading State
.loading-container {
  .loading-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
  }
  
  .loading-card {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    
    .loading-image {
      width: 100%;
      height: 200px;
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200% 100%;
      animation: loading 1.5s infinite;
    }
    
    .loading-content {
      padding: 16px;
      
      .loading-line {
        height: 12px;
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
        border-radius: 6px;
        margin-bottom: 8px;
        
        &.short { width: 40%; }
        &.medium { width: 60%; }
        &.long { width: 80%; }
      }
    }
  }
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

// Error State
.error-container {
  text-align: center;
  padding: 40px 20px;
  
  .error-icon {
    font-size: 48px;
    color: #dc3545;
    margin-bottom: 16px;
  }
  
  .error-message {
    font-size: 16px;
    color: #666;
    margin-bottom: 20px;
  }
  
  .retry-btn {
    background: #007bff;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0 auto;
    transition: background 0.3s ease;
    
    &:hover {
      background: #0056b3;
    }
  }
}

// Products Slider with Owl Carousel
.products-slider-container {
  position: relative;
  padding: 0 30px; // Space for navigation buttons

  // Hover effect to indicate auto-sliding pause
  &:hover {
    .owl-carousel .owl-dots .owl-dot.active::after {
      animation-play-state: paused;
      border-color: rgba(255, 107, 53, 0.5);
    }
  }

  // Owl Carousel Custom Styling
  ::ng-deep {
    .owl-carousel {
      position: relative;

      // Smooth transitions for auto-sliding
      .owl-stage {
        transition: transform 1s ease-in-out !important;
      }

      .owl-nav {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        width: 100%;
        z-index: 10;

        .owl-prev,
        .owl-next {
          position: absolute;
          background: rgba(0, 0, 0, 0.7) !important;
          color: white !important;
          border: none !important;
          width: 40px !important;
          height: 40px !important;
          border-radius: 50% !important;
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;
          cursor: pointer !important;
          transition: all 0.3s ease !important;
          font-size: 16px !important;
          outline: none !important;

          ion-icon {
            font-size: 18px;
          }

          &:hover {
            background: rgba(0, 0, 0, 0.9) !important;
            transform: scale(1.1) !important;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3) !important;
          }

          &.disabled {
            opacity: 0.3 !important;
            cursor: not-allowed !important;
          }
        }

        .owl-prev {
          left: -30px;
        }

        .owl-next {
          right: -30px;
        }
      }

      .owl-dots {
        text-align: center;
        margin-top: 20px;
        padding: 10px 0;

        .owl-dot {
          width: 10px !important;
          height: 10px !important;
          border-radius: 50% !important;
          background: rgba(255, 107, 53, 0.3) !important;
          margin: 0 6px !important;
          cursor: pointer !important;
          transition: all 0.4s ease !important;
          border: 2px solid transparent !important;
          outline: none !important;
          position: relative !important;

          &.active {
            background: #ff6b35 !important;
            transform: scale(1.3) !important;
            border-color: rgba(255, 107, 53, 0.3) !important;

            // Auto-sliding progress indicator
            &::after {
              content: '';
              position: absolute;
              top: -3px;
              left: -3px;
              right: -3px;
              bottom: -3px;
              border: 2px solid #ff6b35;
              border-radius: 50%;
              animation: progress-ring 4s linear infinite;
            }
          }

          &:hover:not(.active) {
            background: rgba(255, 107, 53, 0.6) !important;
            transform: scale(1.1) !important;
          }
        }
      }

      .owl-stage-outer {
        padding: 0; // Remove default padding
        overflow: visible; // Allow navigation buttons to show
      }

      // Auto-sliding progress animation
      @keyframes progress-ring {
        0% {
          transform: rotate(0deg);
          border-color: #ff6b35 transparent transparent transparent;
        }
        25% {
          border-color: #ff6b35 #ff6b35 transparent transparent;
        }
        50% {
          border-color: #ff6b35 #ff6b35 #ff6b35 transparent;
        }
        75% {
          border-color: #ff6b35 #ff6b35 #ff6b35 #ff6b35;
        }
        100% {
          transform: rotate(360deg);
          border-color: #ff6b35 transparent transparent transparent;
        }
      }
    }
  }
}

// Slider Styles
.products-slider-container {
  position: relative;
  margin: 0 -20px; // Extend beyond container padding

  .slider-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 10;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover:not(:disabled) {
      background: rgba(0, 0, 0, 0.9);
      transform: translateY(-50%) scale(1.1);
    }

    &:disabled {
      opacity: 0.3;
      cursor: not-allowed;
    }

    ion-icon {
      font-size: 18px;
    }

    &.prev-btn {
      left: -20px;
    }

    &.next-btn {
      right: -20px;
    }
  }
}

.products-slider-wrapper {
  overflow: hidden;
  padding: 0 20px; // Restore container padding
}

.products-slider {
  display: flex;
  transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  gap: 20px;

  .product-card {
    flex: 0 0 260px; // Fixed width for each card
    width: 260px;
  }
}

// Responsive adjustments
@media (max-width: 1200px) {
  .products-slider {
    .product-card {
      flex: 0 0 240px;
      width: 240px;
    }
  }
}

@media (max-width: 768px) {
  .products-slider-container {
    margin: 0 -10px;

    .slider-nav {
      width: 35px;
      height: 35px;

      &.prev-btn {
        left: -15px;
      }

      &.next-btn {
        right: -15px;
      }

      ion-icon {
        font-size: 16px;
      }
    }
  }

  .products-slider-wrapper {
    padding: 0 10px;
  }

  .products-slider {
    gap: 15px;

    .product-card {
      flex: 0 0 200px;
      width: 200px;
    }
  }
}

@media (max-width: 480px) {
  .products-slider {
    .product-card {
      flex: 0 0 180px;
      width: 180px;
    }
  }
}

.product-card {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  cursor: pointer;
  width: 100%; // Full width within carousel item
  height: auto;
  
  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    
    .action-buttons {
      opacity: 1;
      transform: translateY(0);
    }
  }
}

.product-image-container {
  position: relative;
  overflow: hidden;
  
  .product-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: transform 0.3s ease;
  }
  
  .trending-badge {
    position: absolute;
    top: 12px;
    left: 12px;
    background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 4px;
    
    ion-icon {
      font-size: 14px;
    }
  }
  
  .discount-badge {
    position: absolute;
    top: 12px;
    right: 12px;
    background: #dc3545;
    color: white;
    padding: 6px 10px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 700;
  }
  
  .action-buttons {
    position: absolute;
    top: 50%;
    right: 12px;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    gap: 8px;
    opacity: 0;
    transition: all 0.3s ease;
    
    .action-btn {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      border: none;
      background: rgba(255, 255, 255, 0.9);
      backdrop-filter: blur(10px);
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;
      
      ion-icon {
        font-size: 20px;
        color: #333;
      }
      
      &:hover {
        background: white;
        transform: scale(1.1);
      }
      
      &.like-btn {
        &:hover ion-icon {
          color: #dc3545;
        }

        &.liked {
          background: rgba(220, 53, 69, 0.1);

          ion-icon {
            color: #dc3545;
          }
        }
      }

      &.share-btn:hover ion-icon {
        color: #007bff;
      }
    }
  }
}

.product-info {
  padding: 16px;
  
  .product-brand {
    font-size: 12px;
    color: #666;
    text-transform: uppercase;
    font-weight: 600;
    letter-spacing: 0.5px;
    margin-bottom: 4px;
  }
  
  .product-name {
    font-size: 16px;
    font-weight: 600;
    color: #1a1a1a;
    margin: 0 0 12px 0;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .price-section {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
    
    .current-price {
      font-size: 18px;
      font-weight: 700;
      color: #ff6b35;
    }
    
    .original-price {
      font-size: 14px;
      color: #999;
      text-decoration: line-through;
    }
  }
  
  .rating-section {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;
    
    .stars {
      display: flex;
      gap: 2px;
      
      ion-icon {
        font-size: 14px;
        color: #ddd;
        
        &.filled {
          color: #ffc107;
        }
      }
    }
    
    .rating-text {
      font-size: 12px;
      color: #666;
    }
  }
  
  .product-actions {
    display: flex;
    gap: 8px;
    
    .cart-btn {
      flex: 1;
      background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
      color: white;
      border: none;
      padding: 12px 16px;
      border-radius: 8px;
      font-weight: 600;
      font-size: 14px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      cursor: pointer;
      transition: all 0.3s ease;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
      }
      
      ion-icon {
        font-size: 16px;
      }
    }
    
    .wishlist-btn {
      width: 44px;
      height: 44px;
      border: 2px solid #e9ecef;
      background: white;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;
      
      ion-icon {
        font-size: 20px;
        color: #666;
      }
      
      &:hover {
        border-color: #ff6b35;
        
        ion-icon {
          color: #ff6b35;
        }
      }
    }
  }
}

// Empty State
.empty-container {
  text-align: center;
  padding: 60px 20px;
  
  .empty-icon {
    font-size: 64px;
    color: #ccc;
    margin-bottom: 20px;
  }
  
  .empty-title {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
  }
  
  .empty-message {
    font-size: 14px;
    color: #666;
  }
}

// Responsive Design
@media (max-width: 768px) {
  .trending-products-container {
    padding: 16px;
  }
  
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    
    .view-all-btn {
      align-self: flex-end;
    }
  }
  
  .products-slider-container {
    ::ng-deep {
      .owl-carousel {
        .owl-stage-outer {
          padding: 0 10px; // Reduced padding for mobile
        }

        .owl-nav {
          .owl-prev,
          .owl-next {
            width: 35px !important;
            height: 35px !important;
            font-size: 16px !important;
          }

          .owl-prev {
            left: -15px;
          }

          .owl-next {
            right: -15px;
          }
        }
      }
    }
  }
  
  .section-title {
    font-size: 20px;
  }
}

// Extra small devices (phones, less than 576px)
@media (max-width: 575.98px) {
  .products-slider-container {
    ::ng-deep {
      .owl-carousel {
        .owl-stage-outer {
          padding: 0 5px;
        }

        .owl-nav {
          .owl-prev,
          .owl-next {
            width: 30px !important;
            height: 30px !important;
            font-size: 14px !important;
          }

          .owl-prev {
            left: -10px;
          }

          .owl-next {
            right: -10px;
          }
        }
      }
    }
  }
}
