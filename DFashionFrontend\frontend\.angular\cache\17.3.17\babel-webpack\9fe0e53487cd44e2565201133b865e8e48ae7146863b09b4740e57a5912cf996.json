{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashionFrontend/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CommonModule } from '@angular/common';\nimport { Subscription } from 'rxjs';\nimport { IonicModule } from '@ionic/angular';\nimport { CarouselModule } from 'ngx-owl-carousel-o';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/trending.service\";\nimport * as i2 from \"../../../../core/services/social-interactions.service\";\nimport * as i3 from \"../../../../core/services/cart.service\";\nimport * as i4 from \"../../../../core/services/wishlist.service\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@ionic/angular\";\nconst _c0 = () => [1, 2, 3, 4, 5, 6, 7, 8];\nconst _c1 = () => [1, 2, 3, 4, 5];\nfunction TrendingProductsComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function TrendingProductsComponent_div_3_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleSectionLike());\n    });\n    i0.ɵɵelement(2, \"ion-icon\", 16);\n    i0.ɵɵelementStart(3, \"span\", 17);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function TrendingProductsComponent_div_3_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openComments());\n    });\n    i0.ɵɵelement(6, \"ion-icon\", 19);\n    i0.ɵɵelementStart(7, \"span\", 17);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function TrendingProductsComponent_div_3_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.shareSection());\n    });\n    i0.ɵɵelement(10, \"ion-icon\", 21);\n    i0.ɵɵelementStart(11, \"span\", 22);\n    i0.ɵɵtext(12, \"Share\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function TrendingProductsComponent_div_3_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleSectionBookmark());\n    });\n    i0.ɵɵelement(14, \"ion-icon\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function TrendingProductsComponent_div_3_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openMusicPlayer());\n    });\n    i0.ɵɵelement(16, \"ion-icon\", 25);\n    i0.ɵɵelementStart(17, \"span\", 22);\n    i0.ɵɵtext(18, \"Music\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"active\", ctx_r1.isSectionLiked);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", ctx_r1.isSectionLiked ? \"heart\" : \"heart-outline\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.formatCount(ctx_r1.sectionLikes));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.formatCount(ctx_r1.sectionComments));\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"active\", ctx_r1.isSectionBookmarked);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", ctx_r1.isSectionBookmarked ? \"bookmark\" : \"bookmark-outline\");\n  }\n}\nfunction TrendingProductsComponent_div_14_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵelement(1, \"div\", 30);\n    i0.ɵɵelementStart(2, \"div\", 31);\n    i0.ɵɵelement(3, \"div\", 32)(4, \"div\", 33)(5, \"div\", 34);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TrendingProductsComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"div\", 27);\n    i0.ɵɵtemplate(2, TrendingProductsComponent_div_14_div_2_Template, 6, 0, \"div\", 28);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c0));\n  }\n}\nfunction TrendingProductsComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵelement(1, \"ion-icon\", 36);\n    i0.ɵɵelementStart(2, \"p\", 37);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function TrendingProductsComponent_div_15_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onRetry());\n    });\n    i0.ɵɵelement(5, \"ion-icon\", 39);\n    i0.ɵɵtext(6, \" Try Again \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction TrendingProductsComponent_div_16_div_7_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 70);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r6 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getDiscountPercentage(product_r6), \"% OFF \");\n  }\n}\nfunction TrendingProductsComponent_div_16_div_7_span_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 71);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r6 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.formatPrice(product_r6.originalPrice));\n  }\n}\nfunction TrendingProductsComponent_div_16_div_7_ion_icon_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ion-icon\", 16);\n  }\n  if (rf & 2) {\n    const star_r7 = ctx.$implicit;\n    const product_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵclassProp(\"filled\", star_r7 <= product_r6.rating.average);\n    i0.ɵɵproperty(\"name\", star_r7 <= product_r6.rating.average ? \"star\" : \"star-outline\");\n  }\n}\nfunction TrendingProductsComponent_div_16_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵlistener(\"click\", function TrendingProductsComponent_div_16_div_7_Template_div_click_0_listener() {\n      const product_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onProductClick(product_r6));\n    });\n    i0.ɵɵelementStart(1, \"div\", 48);\n    i0.ɵɵelement(2, \"img\", 49);\n    i0.ɵɵelementStart(3, \"div\", 50);\n    i0.ɵɵelement(4, \"ion-icon\", 51);\n    i0.ɵɵtext(5, \" Trending \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, TrendingProductsComponent_div_16_div_7_div_6_Template, 2, 1, \"div\", 52);\n    i0.ɵɵelementStart(7, \"div\", 53)(8, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function TrendingProductsComponent_div_16_div_7_Template_button_click_8_listener($event) {\n      const product_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onLikeProduct(product_r6, $event));\n    });\n    i0.ɵɵelement(9, \"ion-icon\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function TrendingProductsComponent_div_16_div_7_Template_button_click_10_listener($event) {\n      const product_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onShareProduct(product_r6, $event));\n    });\n    i0.ɵɵelement(11, \"ion-icon\", 54);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"div\", 55)(13, \"div\", 56);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"h3\", 57);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 58)(18, \"span\", 59);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(20, TrendingProductsComponent_div_16_div_7_span_20_Template, 2, 1, \"span\", 60);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 61)(22, \"div\", 62);\n    i0.ɵɵtemplate(23, TrendingProductsComponent_div_16_div_7_ion_icon_23_Template, 1, 3, \"ion-icon\", 63);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"span\", 64);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\", 65)(27, \"button\", 66);\n    i0.ɵɵlistener(\"click\", function TrendingProductsComponent_div_16_div_7_Template_button_click_27_listener($event) {\n      const product_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onAddToCart(product_r6, $event));\n    });\n    i0.ɵɵelement(28, \"ion-icon\", 67);\n    i0.ɵɵtext(29, \" Add to Cart \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"button\", 68);\n    i0.ɵɵlistener(\"click\", function TrendingProductsComponent_div_16_div_7_Template_button_click_30_listener($event) {\n      const product_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onAddToWishlist(product_r6, $event));\n    });\n    i0.ɵɵelement(31, \"ion-icon\", 69);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const product_r6 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", product_r6.images[0].url, i0.ɵɵsanitizeUrl)(\"alt\", product_r6.images[0].alt || product_r6.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getDiscountPercentage(product_r6) > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"liked\", ctx_r1.isProductLiked(product_r6._id));\n    i0.ɵɵattribute(\"aria-label\", \"Like \" + product_r6.name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", ctx_r1.isProductLiked(product_r6._id) ? \"heart\" : \"heart-outline\");\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"aria-label\", \"Share \" + product_r6.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(product_r6.brand);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r6.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.formatPrice(product_r6.price));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r6.originalPrice && product_r6.originalPrice > product_r6.price);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(14, _c1));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", product_r6.rating.count, \")\");\n  }\n}\nfunction TrendingProductsComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function TrendingProductsComponent_div_16_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.slidePrev());\n    });\n    i0.ɵɵelement(2, \"ion-icon\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function TrendingProductsComponent_div_16_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.slideNext());\n    });\n    i0.ɵɵelement(4, \"ion-icon\", 9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 44);\n    i0.ɵɵlistener(\"mouseenter\", function TrendingProductsComponent_div_16_Template_div_mouseenter_5_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.pauseAutoSlide());\n    })(\"mouseleave\", function TrendingProductsComponent_div_16_Template_div_mouseleave_5_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.resumeAutoSlide());\n    });\n    i0.ɵɵelementStart(6, \"div\", 45);\n    i0.ɵɵtemplate(7, TrendingProductsComponent_div_16_div_7_Template, 32, 15, \"div\", 46);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentSlide === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentSlide >= ctx_r1.maxSlide);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"transform\", \"translateX(\" + ctx_r1.slideOffset + \"px)\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.trendingProducts)(\"ngForTrackBy\", ctx_r1.trackByProductId);\n  }\n}\nfunction TrendingProductsComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 72);\n    i0.ɵɵelement(1, \"ion-icon\", 73);\n    i0.ɵɵelementStart(2, \"h3\", 74);\n    i0.ɵɵtext(3, \"No Trending Products\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 75);\n    i0.ɵɵtext(5, \"Check back later for trending items\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class TrendingProductsComponent {\n  constructor(trendingService, socialService, cartService, wishlistService, router) {\n    this.trendingService = trendingService;\n    this.socialService = socialService;\n    this.cartService = cartService;\n    this.wishlistService = wishlistService;\n    this.router = router;\n    this.trendingProducts = [];\n    this.isLoading = true;\n    this.error = null;\n    this.likedProducts = new Set();\n    this.subscription = new Subscription();\n    // Slider properties\n    this.currentSlide = 0;\n    this.slideOffset = 0;\n    this.cardWidth = 280; // Width of each product card including margin\n    this.visibleCards = 4; // Number of cards visible at once\n    this.maxSlide = 0;\n    this.autoSlideDelay = 3000; // 3 seconds\n    this.isAutoSliding = true;\n    this.isPaused = false;\n    // Section interaction properties\n    this.isSectionLiked = false;\n    this.isSectionBookmarked = false;\n    this.sectionLikes = 365;\n    this.sectionComments = 105;\n    this.isMobile = false;\n    // Owl Carousel Options with Auto-sliding\n    this.carouselOptions = {\n      loop: true,\n      mouseDrag: true,\n      touchDrag: true,\n      pullDrag: false,\n      dots: true,\n      navSpeed: 700,\n      navText: ['<ion-icon name=\"chevron-back\"></ion-icon>', '<ion-icon name=\"chevron-forward\"></ion-icon>'],\n      autoplay: true,\n      autoplayTimeout: 4000,\n      autoplayHoverPause: true,\n      autoplaySpeed: 1000,\n      smartSpeed: 1000,\n      fluidSpeed: true,\n      responsive: {\n        0: {\n          items: 1,\n          margin: 10,\n          nav: false,\n          dots: true\n        },\n        576: {\n          items: 2,\n          margin: 15,\n          nav: true,\n          dots: true\n        },\n        768: {\n          items: 3,\n          margin: 20,\n          nav: true,\n          dots: true\n        },\n        992: {\n          items: 4,\n          margin: 20,\n          nav: true,\n          dots: false // Hide dots on desktop, show nav instead\n        }\n      },\n      nav: true,\n      margin: 20,\n      stagePadding: 0,\n      center: false,\n      animateOut: false,\n      animateIn: false\n    };\n  }\n  ngOnInit() {\n    this.loadTrendingProducts();\n    this.subscribeTrendingProducts();\n    this.subscribeLikedProducts();\n    this.updateResponsiveSettings();\n    this.setupResizeListener();\n    this.checkMobileDevice();\n  }\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n    this.stopAutoSlide();\n  }\n  subscribeTrendingProducts() {\n    this.subscription.add(this.trendingService.trendingProducts$.subscribe(products => {\n      this.trendingProducts = products;\n      this.isLoading = false;\n      this.updateSliderOnProductsLoad();\n    }));\n  }\n  subscribeLikedProducts() {\n    this.subscription.add(this.socialService.likedProducts$.subscribe(likedProducts => {\n      this.likedProducts = likedProducts;\n    }));\n  }\n  loadTrendingProducts() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this.isLoading = true;\n        _this.error = null;\n        yield _this.trendingService.loadTrendingProducts(1, 8);\n      } catch (error) {\n        console.error('Error loading trending products:', error);\n        _this.error = 'Failed to load trending products';\n        _this.isLoading = false;\n      }\n    })();\n  }\n  onProductClick(product) {\n    this.router.navigate(['/product', product._id]);\n  }\n  onLikeProduct(product, event) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      event.stopPropagation();\n      try {\n        const result = yield _this2.socialService.likeProduct(product._id);\n        if (result.success) {\n          console.log(result.message);\n        } else {\n          console.error('Failed to like product:', result.message);\n        }\n      } catch (error) {\n        console.error('Error liking product:', error);\n      }\n    })();\n  }\n  onShareProduct(product, event) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      event.stopPropagation();\n      try {\n        // For now, copy link to clipboard\n        const productUrl = `${window.location.origin}/product/${product._id}`;\n        yield navigator.clipboard.writeText(productUrl);\n        // Track the share\n        yield _this3.socialService.shareProduct(product._id, {\n          platform: 'copy_link',\n          message: `Check out this amazing ${product.name} from ${product.brand}!`\n        });\n        console.log('Product link copied to clipboard!');\n      } catch (error) {\n        console.error('Error sharing product:', error);\n      }\n    })();\n  }\n  onAddToCart(product, event) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      event.stopPropagation();\n      try {\n        yield _this4.cartService.addToCart(product._id, 1);\n        console.log('Product added to cart!');\n      } catch (error) {\n        console.error('Error adding to cart:', error);\n      }\n    })();\n  }\n  onAddToWishlist(product, event) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      event.stopPropagation();\n      try {\n        yield _this5.wishlistService.addToWishlist(product._id);\n        console.log('Product added to wishlist!');\n      } catch (error) {\n        console.error('Error adding to wishlist:', error);\n      }\n    })();\n  }\n  getDiscountPercentage(product) {\n    if (product.originalPrice && product.originalPrice > product.price) {\n      return Math.round((product.originalPrice - product.price) / product.originalPrice * 100);\n    }\n    return 0;\n  }\n  formatPrice(price) {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0\n    }).format(price);\n  }\n  onRetry() {\n    this.loadTrendingProducts();\n  }\n  onViewAll() {\n    this.router.navigate(['/products'], {\n      queryParams: {\n        filter: 'trending'\n      }\n    });\n  }\n  isProductLiked(productId) {\n    return this.likedProducts.has(productId);\n  }\n  trackByProductId(index, product) {\n    return product._id;\n  }\n  // Auto-sliding methods\n  startAutoSlide() {\n    if (!this.isAutoSliding || this.isPaused) return;\n    this.stopAutoSlide();\n    this.autoSlideInterval = setInterval(() => {\n      if (!this.isPaused && this.trendingProducts.length > this.visibleCards) {\n        this.autoSlideNext();\n      }\n    }, this.autoSlideDelay);\n  }\n  stopAutoSlide() {\n    if (this.autoSlideInterval) {\n      clearInterval(this.autoSlideInterval);\n      this.autoSlideInterval = null;\n    }\n  }\n  autoSlideNext() {\n    if (this.currentSlide >= this.maxSlide) {\n      // Reset to beginning for infinite loop\n      this.currentSlide = 0;\n    } else {\n      this.currentSlide++;\n    }\n    this.updateSlideOffset();\n  }\n  pauseAutoSlide() {\n    this.isPaused = true;\n    this.stopAutoSlide();\n  }\n  resumeAutoSlide() {\n    this.isPaused = false;\n    this.startAutoSlide();\n  }\n  // Responsive methods\n  updateResponsiveSettings() {\n    const width = window.innerWidth;\n    if (width <= 480) {\n      this.cardWidth = 195; // 180px + 15px gap\n      this.visibleCards = 1;\n    } else if (width <= 768) {\n      this.cardWidth = 215; // 200px + 15px gap\n      this.visibleCards = 2;\n    } else if (width <= 1200) {\n      this.cardWidth = 260; // 240px + 20px gap\n      this.visibleCards = 3;\n    } else {\n      this.cardWidth = 280; // 260px + 20px gap\n      this.visibleCards = 4;\n    }\n    this.updateSliderLimits();\n    this.updateSlideOffset();\n  }\n  setupResizeListener() {\n    window.addEventListener('resize', () => {\n      this.updateResponsiveSettings();\n    });\n  }\n  // Slider methods\n  updateSliderLimits() {\n    this.maxSlide = Math.max(0, this.trendingProducts.length - this.visibleCards);\n  }\n  slidePrev() {\n    if (this.currentSlide > 0) {\n      this.currentSlide--;\n      this.updateSlideOffset();\n      this.restartAutoSlideAfterInteraction();\n    }\n  }\n  slideNext() {\n    if (this.currentSlide < this.maxSlide) {\n      this.currentSlide++;\n      this.updateSlideOffset();\n      this.restartAutoSlideAfterInteraction();\n    }\n  }\n  restartAutoSlideAfterInteraction() {\n    this.stopAutoSlide();\n    setTimeout(() => {\n      this.startAutoSlide();\n    }, 2000); // Wait 2 seconds before resuming auto-slide\n  }\n  updateSlideOffset() {\n    this.slideOffset = -this.currentSlide * this.cardWidth;\n  }\n  // Update slider when products load\n  updateSliderOnProductsLoad() {\n    setTimeout(() => {\n      this.updateSliderLimits();\n      this.currentSlide = 0;\n      this.slideOffset = 0;\n      this.startAutoSlide();\n    }, 100);\n  }\n  // Section interaction methods\n  toggleSectionLike() {\n    this.isSectionLiked = !this.isSectionLiked;\n    if (this.isSectionLiked) {\n      this.sectionLikes++;\n    } else {\n      this.sectionLikes--;\n    }\n  }\n  toggleSectionBookmark() {\n    this.isSectionBookmarked = !this.isSectionBookmarked;\n  }\n  openComments() {\n    // Open comments modal/sheet\n    console.log('Opening comments for trending products section');\n  }\n  shareSection() {\n    // Share section functionality\n    if (navigator.share) {\n      navigator.share({\n        title: 'Trending Products',\n        text: 'Check out these trending fashion products!',\n        url: window.location.href\n      });\n    } else {\n      // Fallback for browsers that don't support Web Share API\n      navigator.clipboard.writeText(window.location.href);\n      console.log('Link copied to clipboard');\n    }\n  }\n  openMusicPlayer() {\n    // Open music player for section\n    console.log('Opening music player for trending products');\n  }\n  formatCount(count) {\n    if (count >= 1000000) {\n      return (count / 1000000).toFixed(1) + 'M';\n    } else if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'K';\n    }\n    return count.toString();\n  }\n  checkMobileDevice() {\n    this.isMobile = window.innerWidth <= 768;\n  }\n  static {\n    this.ɵfac = function TrendingProductsComponent_Factory(t) {\n      return new (t || TrendingProductsComponent)(i0.ɵɵdirectiveInject(i1.TrendingService), i0.ɵɵdirectiveInject(i2.SocialInteractionsService), i0.ɵɵdirectiveInject(i3.CartService), i0.ɵɵdirectiveInject(i4.WishlistService), i0.ɵɵdirectiveInject(i5.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TrendingProductsComponent,\n      selectors: [[\"app-trending-products\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 18,\n      vars: 5,\n      consts: [[1, \"trending-products-container\"], [2, \"background\", \"cyan\", \"color\", \"black\", \"padding\", \"20px\", \"margin\", \"10px 0\", \"font-weight\", \"bold\"], [\"class\", \"mobile-action-buttons\", 4, \"ngIf\"], [1, \"section-header\"], [1, \"header-content\"], [1, \"section-title\"], [\"name\", \"trending-up\", 1, \"title-icon\"], [1, \"section-subtitle\"], [1, \"view-all-btn\", 3, \"click\"], [\"name\", \"chevron-forward\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"products-slider-container\", 4, \"ngIf\"], [\"class\", \"empty-container\", 4, \"ngIf\"], [1, \"mobile-action-buttons\"], [1, \"action-btn\", \"like-btn\", 3, \"click\"], [3, \"name\"], [1, \"action-count\"], [1, \"action-btn\", \"comment-btn\", 3, \"click\"], [\"name\", \"chatbubble-outline\"], [1, \"action-btn\", \"share-btn\", 3, \"click\"], [\"name\", \"arrow-redo-outline\"], [1, \"action-text\"], [1, \"action-btn\", \"bookmark-btn\", 3, \"click\"], [1, \"action-btn\", \"music-btn\", 3, \"click\"], [\"name\", \"musical-notes\"], [1, \"loading-container\"], [1, \"loading-grid\"], [\"class\", \"loading-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"loading-card\"], [1, \"loading-image\"], [1, \"loading-content\"], [1, \"loading-line\", \"short\"], [1, \"loading-line\", \"medium\"], [1, \"loading-line\", \"long\"], [1, \"error-container\"], [\"name\", \"alert-circle\", 1, \"error-icon\"], [1, \"error-message\"], [1, \"retry-btn\", 3, \"click\"], [\"name\", \"refresh\"], [1, \"products-slider-container\"], [1, \"slider-nav\", \"prev-btn\", 3, \"click\", \"disabled\"], [\"name\", \"chevron-back\"], [1, \"slider-nav\", \"next-btn\", 3, \"click\", \"disabled\"], [1, \"products-slider-wrapper\", 3, \"mouseenter\", \"mouseleave\"], [1, \"products-slider\"], [\"class\", \"product-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"product-card\", 3, \"click\"], [1, \"product-image-container\"], [\"loading\", \"lazy\", 1, \"product-image\", 3, \"src\", \"alt\"], [1, \"trending-badge\"], [\"name\", \"trending-up\"], [\"class\", \"discount-badge\", 4, \"ngIf\"], [1, \"action-buttons\"], [\"name\", \"share-outline\"], [1, \"product-info\"], [1, \"product-brand\"], [1, \"product-name\"], [1, \"price-section\"], [1, \"current-price\"], [\"class\", \"original-price\", 4, \"ngIf\"], [1, \"rating-section\"], [1, \"stars\"], [3, \"name\", \"filled\", 4, \"ngFor\", \"ngForOf\"], [1, \"rating-text\"], [1, \"product-actions\"], [1, \"cart-btn\", 3, \"click\"], [\"name\", \"bag-add-outline\"], [1, \"wishlist-btn\", 3, \"click\"], [\"name\", \"heart-outline\"], [1, \"discount-badge\"], [1, \"original-price\"], [1, \"empty-container\"], [\"name\", \"trending-up-outline\", 1, \"empty-icon\"], [1, \"empty-title\"], [1, \"empty-message\"]],\n      template: function TrendingProductsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵtext(2, \" TRENDING PRODUCTS COMPONENT IS VISIBLE \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(3, TrendingProductsComponent_div_3_Template, 19, 8, \"div\", 2);\n          i0.ɵɵelementStart(4, \"div\", 3)(5, \"div\", 4)(6, \"h2\", 5);\n          i0.ɵɵelement(7, \"ion-icon\", 6);\n          i0.ɵɵtext(8, \" Trending Now \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"p\", 7);\n          i0.ɵɵtext(10, \"Most popular products this week\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"button\", 8);\n          i0.ɵɵlistener(\"click\", function TrendingProductsComponent_Template_button_click_11_listener() {\n            return ctx.onViewAll();\n          });\n          i0.ɵɵtext(12, \" View All \");\n          i0.ɵɵelement(13, \"ion-icon\", 9);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(14, TrendingProductsComponent_div_14_Template, 3, 2, \"div\", 10)(15, TrendingProductsComponent_div_15_Template, 7, 1, \"div\", 11)(16, TrendingProductsComponent_div_16_Template, 8, 6, \"div\", 12)(17, TrendingProductsComponent_div_17_Template, 6, 0, \"div\", 13);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.isMobile);\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.error && !ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.trendingProducts.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.trendingProducts.length === 0);\n        }\n      },\n      dependencies: [CommonModule, i6.NgForOf, i6.NgIf, IonicModule, i7.IonIcon, CarouselModule],\n      styles: [\".trending-products-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n  border-radius: 16px;\\n  margin-bottom: 24px;\\n  position: relative;\\n  max-width: 675px;\\n}\\n\\n.mobile-action-buttons[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 15px;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  display: flex;\\n  flex-direction: column;\\n  gap: 20px;\\n  z-index: 10;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%] {\\n  width: 48px;\\n  height: 48px;\\n  border-radius: 50%;\\n  border: none;\\n  background: rgba(255, 255, 255, 0.2);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  color: white;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  position: relative;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  margin-bottom: 2px;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   .action-count[_ngcontent-%COMP%], .mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   .action-text[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n  font-weight: 600;\\n  line-height: 1;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1);\\n  background: rgba(255, 255, 255, 0.3);\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.9);\\n  color: #ff6b35;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active.like-btn[_ngcontent-%COMP%] {\\n  color: #ff3040;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active.like-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_heartBeat 0.6s ease-in-out;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active.bookmark-btn[_ngcontent-%COMP%] {\\n  color: #ffd700;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.like-btn.active[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  color: #ff3040;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.comment-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3);\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.share-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3);\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.music-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ff3040 0%, #ff6b35 100%);\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.music-btn[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1) rotate(15deg);\\n}\\n\\n@keyframes _ngcontent-%COMP%_heartBeat {\\n  0% {\\n    transform: scale(1);\\n  }\\n  25% {\\n    transform: scale(1.3);\\n  }\\n  50% {\\n    transform: scale(1.1);\\n  }\\n  75% {\\n    transform: scale(1.25);\\n  }\\n  100% {\\n    transform: scale(1);\\n  }\\n}\\n@media (min-width: 769px) {\\n  .mobile-action-buttons[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n.section-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 24px;\\n}\\n.section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 700;\\n  color: #1a1a1a;\\n  margin: 0 0 8px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   .title-icon[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  color: #ff6b35;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-subtitle[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #666;\\n  margin: 0;\\n}\\n.section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);\\n  color: white;\\n  border: none;\\n  padding: 12px 20px;\\n  border-radius: 25px;\\n  font-weight: 600;\\n  font-size: 14px;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);\\n}\\n.section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4);\\n}\\n.section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n\\n.loading-container[_ngcontent-%COMP%]   .loading-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\\n  gap: 20px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 16px;\\n  overflow: hidden;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 200px;\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%] {\\n  padding: 16px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line[_ngcontent-%COMP%] {\\n  height: 12px;\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n  border-radius: 6px;\\n  margin-bottom: 8px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.short[_ngcontent-%COMP%] {\\n  width: 40%;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.medium[_ngcontent-%COMP%] {\\n  width: 60%;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.long[_ngcontent-%COMP%] {\\n  width: 80%;\\n}\\n\\n@keyframes _ngcontent-%COMP%_loading {\\n  0% {\\n    background-position: 200% 0;\\n  }\\n  100% {\\n    background-position: -200% 0;\\n  }\\n}\\n.error-container[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 40px 20px;\\n}\\n.error-container[_ngcontent-%COMP%]   .error-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  color: #dc3545;\\n  margin-bottom: 16px;\\n}\\n.error-container[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: #666;\\n  margin-bottom: 20px;\\n}\\n.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%] {\\n  background: #007bff;\\n  color: white;\\n  border: none;\\n  padding: 12px 24px;\\n  border-radius: 8px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin: 0 auto;\\n  transition: background 0.3s ease;\\n}\\n.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]:hover {\\n  background: #0056b3;\\n}\\n\\n.products-slider-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  padding: 0 30px;\\n}\\n.products-slider-container[_ngcontent-%COMP%]:hover   .owl-carousel[_ngcontent-%COMP%]   .owl-dots[_ngcontent-%COMP%]   .owl-dot.active[_ngcontent-%COMP%]::after {\\n  animation-play-state: paused;\\n  border-color: rgba(255, 107, 53, 0.5);\\n}\\n.products-slider-container[_ngcontent-%COMP%]     .owl-carousel {\\n  position: relative;\\n}\\n.products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-stage {\\n  transition: transform 1s ease-in-out !important;\\n}\\n.products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav {\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  width: 100%;\\n  z-index: 10;\\n}\\n.products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev, .products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next {\\n  position: absolute;\\n  background: rgba(0, 0, 0, 0.7) !important;\\n  color: white !important;\\n  border: none !important;\\n  width: 40px !important;\\n  height: 40px !important;\\n  border-radius: 50% !important;\\n  display: flex !important;\\n  align-items: center !important;\\n  justify-content: center !important;\\n  cursor: pointer !important;\\n  transition: all 0.3s ease !important;\\n  font-size: 16px !important;\\n  outline: none !important;\\n}\\n.products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev ion-icon, .products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next ion-icon {\\n  font-size: 18px;\\n}\\n.products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev:hover, .products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next:hover {\\n  background: rgba(0, 0, 0, 0.9) !important;\\n  transform: scale(1.1) !important;\\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3) !important;\\n}\\n.products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev.disabled, .products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next.disabled {\\n  opacity: 0.3 !important;\\n  cursor: not-allowed !important;\\n}\\n.products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev {\\n  left: -30px;\\n}\\n.products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next {\\n  right: -30px;\\n}\\n.products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-dots {\\n  text-align: center;\\n  margin-top: 20px;\\n  padding: 10px 0;\\n}\\n.products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-dots .owl-dot {\\n  width: 10px !important;\\n  height: 10px !important;\\n  border-radius: 50% !important;\\n  background: rgba(255, 107, 53, 0.3) !important;\\n  margin: 0 6px !important;\\n  cursor: pointer !important;\\n  transition: all 0.4s ease !important;\\n  border: 2px solid transparent !important;\\n  outline: none !important;\\n  position: relative !important;\\n}\\n.products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-dots .owl-dot.active {\\n  background: #ff6b35 !important;\\n  transform: scale(1.3) !important;\\n  border-color: rgba(255, 107, 53, 0.3) !important;\\n}\\n.products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-dots .owl-dot.active::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: -3px;\\n  left: -3px;\\n  right: -3px;\\n  bottom: -3px;\\n  border: 2px solid #ff6b35;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_progress-ring 4s linear infinite;\\n}\\n.products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-dots .owl-dot:hover:not(.active) {\\n  background: rgba(255, 107, 53, 0.6) !important;\\n  transform: scale(1.1) !important;\\n}\\n.products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-stage-outer {\\n  padding: 0;\\n  overflow: visible;\\n}\\n@keyframes _ngcontent-%COMP%_progress-ring {\\n  0% {\\n    transform: rotate(0deg);\\n    border-color: #ff6b35 transparent transparent transparent;\\n  }\\n  25% {\\n    border-color: #ff6b35 #ff6b35 transparent transparent;\\n  }\\n  50% {\\n    border-color: #ff6b35 #ff6b35 #ff6b35 transparent;\\n  }\\n  75% {\\n    border-color: #ff6b35 #ff6b35 #ff6b35 #ff6b35;\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n    border-color: #ff6b35 transparent transparent transparent;\\n  }\\n}\\n\\n.products-slider-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  margin: 0 -20px;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  z-index: 10;\\n  background: rgba(0, 0, 0, 0.7);\\n  color: white;\\n  border: none;\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: rgba(0, 0, 0, 0.9);\\n  transform: translateY(-50%) scale(1.1);\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.3;\\n  cursor: not-allowed;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .slider-nav.prev-btn[_ngcontent-%COMP%] {\\n  left: -20px;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .slider-nav.next-btn[_ngcontent-%COMP%] {\\n  right: -20px;\\n}\\n\\n.products-slider-wrapper[_ngcontent-%COMP%] {\\n  overflow: hidden;\\n  padding: 0 20px;\\n}\\n\\n.products-slider[_ngcontent-%COMP%] {\\n  display: flex;\\n  transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);\\n  gap: 20px;\\n}\\n.products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%] {\\n  flex: 0 0 260px;\\n  width: 260px;\\n}\\n\\n@media (max-width: 1200px) {\\n  .products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%] {\\n    flex: 0 0 240px;\\n    width: 240px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .products-slider-container[_ngcontent-%COMP%] {\\n    margin: 0 -10px;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%] {\\n    width: 35px;\\n    height: 35px;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]   .slider-nav.prev-btn[_ngcontent-%COMP%] {\\n    left: -15px;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]   .slider-nav.next-btn[_ngcontent-%COMP%] {\\n    right: -15px;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n  .products-slider-wrapper[_ngcontent-%COMP%] {\\n    padding: 0 10px;\\n  }\\n  .products-slider[_ngcontent-%COMP%] {\\n    gap: 15px;\\n  }\\n  .products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%] {\\n    flex: 0 0 200px;\\n    width: 200px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%] {\\n    flex: 0 0 180px;\\n    width: 180px;\\n  }\\n}\\n.product-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 16px;\\n  overflow: hidden;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n  width: 100%;\\n  height: auto;\\n}\\n.product-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-8px);\\n  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);\\n}\\n.product-card[_ngcontent-%COMP%]:hover   .action-buttons[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  transform: translateY(0);\\n}\\n\\n.product-image-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  overflow: hidden;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 200px;\\n  object-fit: cover;\\n  transition: transform 0.3s ease;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .trending-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 12px;\\n  left: 12px;\\n  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);\\n  color: white;\\n  padding: 6px 12px;\\n  border-radius: 20px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .trending-badge[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .discount-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 12px;\\n  right: 12px;\\n  background: #dc3545;\\n  color: white;\\n  padding: 6px 10px;\\n  border-radius: 12px;\\n  font-size: 12px;\\n  font-weight: 700;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  right: 12px;\\n  transform: translateY(-50%);\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n  opacity: 0;\\n  transition: all 0.3s ease;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  border: none;\\n  background: rgba(255, 255, 255, 0.9);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  color: #333;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:hover {\\n  background: white;\\n  transform: scale(1.1);\\n}\\n.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn.like-btn[_ngcontent-%COMP%]:hover   ion-icon[_ngcontent-%COMP%] {\\n  color: #dc3545;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn.like-btn.liked[_ngcontent-%COMP%] {\\n  background: rgba(220, 53, 69, 0.1);\\n}\\n.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn.like-btn.liked[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  color: #dc3545;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn.share-btn[_ngcontent-%COMP%]:hover   ion-icon[_ngcontent-%COMP%] {\\n  color: #007bff;\\n}\\n\\n.product-info[_ngcontent-%COMP%] {\\n  padding: 16px;\\n}\\n.product-info[_ngcontent-%COMP%]   .product-brand[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #666;\\n  text-transform: uppercase;\\n  font-weight: 600;\\n  letter-spacing: 0.5px;\\n  margin-bottom: 4px;\\n}\\n.product-info[_ngcontent-%COMP%]   .product-name[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #1a1a1a;\\n  margin: 0 0 12px 0;\\n  line-height: 1.4;\\n  display: -webkit-box;\\n  -webkit-line-clamp: 2;\\n  -webkit-box-orient: vertical;\\n  overflow: hidden;\\n}\\n.product-info[_ngcontent-%COMP%]   .price-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin-bottom: 12px;\\n}\\n.product-info[_ngcontent-%COMP%]   .price-section[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 700;\\n  color: #ff6b35;\\n}\\n.product-info[_ngcontent-%COMP%]   .price-section[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #999;\\n  text-decoration: line-through;\\n}\\n.product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin-bottom: 16px;\\n}\\n.product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 2px;\\n}\\n.product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #ddd;\\n}\\n.product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   ion-icon.filled[_ngcontent-%COMP%] {\\n  color: #ffc107;\\n}\\n.product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%]   .rating-text[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #666;\\n}\\n.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .cart-btn[_ngcontent-%COMP%] {\\n  flex: 1;\\n  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);\\n  color: white;\\n  border: none;\\n  padding: 12px 16px;\\n  border-radius: 8px;\\n  font-weight: 600;\\n  font-size: 14px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 8px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .cart-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);\\n}\\n.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .cart-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .wishlist-btn[_ngcontent-%COMP%] {\\n  width: 44px;\\n  height: 44px;\\n  border: 2px solid #e9ecef;\\n  background: white;\\n  border-radius: 8px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .wishlist-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  color: #666;\\n}\\n.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .wishlist-btn[_ngcontent-%COMP%]:hover {\\n  border-color: #ff6b35;\\n}\\n.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .wishlist-btn[_ngcontent-%COMP%]:hover   ion-icon[_ngcontent-%COMP%] {\\n  color: #ff6b35;\\n}\\n\\n.empty-container[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 60px 20px;\\n}\\n.empty-container[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%] {\\n  font-size: 64px;\\n  color: #ccc;\\n  margin-bottom: 20px;\\n}\\n.empty-container[_ngcontent-%COMP%]   .empty-title[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: #333;\\n  margin-bottom: 8px;\\n}\\n.empty-container[_ngcontent-%COMP%]   .empty-message[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #666;\\n}\\n\\n@media (max-width: 768px) {\\n  .trending-products-container[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .section-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 16px;\\n  }\\n  .section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%] {\\n    align-self: flex-end;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-stage-outer {\\n    padding: 0 10px;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev, .products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next {\\n    width: 35px !important;\\n    height: 35px !important;\\n    font-size: 16px !important;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev {\\n    left: -15px;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next {\\n    right: -15px;\\n  }\\n  .section-title[_ngcontent-%COMP%] {\\n    font-size: 20px;\\n  }\\n}\\n@media (max-width: 575.98px) {\\n  .products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-stage-outer {\\n    padding: 0 5px;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev, .products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next {\\n    width: 30px !important;\\n    height: 30px !important;\\n    font-size: 14px !important;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev {\\n    left: -10px;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next {\\n    right: -10px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "Subscription", "IonicModule", "CarouselModule", "i0", "ɵɵelementStart", "ɵɵlistener", "TrendingProductsComponent_div_3_Template_button_click_1_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "toggleSectionLike", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "TrendingProductsComponent_div_3_Template_button_click_5_listener", "openComments", "TrendingProductsComponent_div_3_Template_button_click_9_listener", "shareSection", "TrendingProductsComponent_div_3_Template_button_click_13_listener", "toggleSectionBookmark", "TrendingProductsComponent_div_3_Template_button_click_15_listener", "openMusicPlayer", "ɵɵadvance", "ɵɵclassProp", "isSectionLiked", "ɵɵproperty", "ɵɵtextInterpolate", "formatCount", "sectionLikes", "sectionComments", "isSectionBookmarked", "ɵɵtemplate", "TrendingProductsComponent_div_14_div_2_Template", "ɵɵpureFunction0", "_c0", "TrendingProductsComponent_div_15_Template_button_click_4_listener", "_r3", "onRetry", "error", "ɵɵtextInterpolate1", "getDiscountPercentage", "product_r6", "formatPrice", "originalPrice", "star_r7", "rating", "average", "TrendingProductsComponent_div_16_div_7_Template_div_click_0_listener", "_r5", "$implicit", "onProductClick", "TrendingProductsComponent_div_16_div_7_div_6_Template", "TrendingProductsComponent_div_16_div_7_Template_button_click_8_listener", "$event", "onLikeProduct", "TrendingProductsComponent_div_16_div_7_Template_button_click_10_listener", "onShareProduct", "TrendingProductsComponent_div_16_div_7_span_20_Template", "TrendingProductsComponent_div_16_div_7_ion_icon_23_Template", "TrendingProductsComponent_div_16_div_7_Template_button_click_27_listener", "onAddToCart", "TrendingProductsComponent_div_16_div_7_Template_button_click_30_listener", "onAddToWishlist", "images", "url", "ɵɵsanitizeUrl", "alt", "name", "isProductLiked", "_id", "brand", "price", "_c1", "count", "TrendingProductsComponent_div_16_Template_button_click_1_listener", "_r4", "slidePrev", "TrendingProductsComponent_div_16_Template_button_click_3_listener", "slideNext", "TrendingProductsComponent_div_16_Template_div_mouseenter_5_listener", "pauseAutoSlide", "TrendingProductsComponent_div_16_Template_div_mouseleave_5_listener", "resumeAutoSlide", "TrendingProductsComponent_div_16_div_7_Template", "currentSlide", "maxSlide", "ɵɵstyleProp", "slideOffset", "trendingProducts", "trackByProductId", "TrendingProductsComponent", "constructor", "trendingService", "socialService", "cartService", "wishlistService", "router", "isLoading", "likedProducts", "Set", "subscription", "<PERSON><PERSON><PERSON><PERSON>", "visibleCards", "autoSlideDelay", "isAutoSliding", "isPaused", "isMobile", "carouselOptions", "loop", "mouseDrag", "touchDrag", "pullDrag", "dots", "navSpeed", "navText", "autoplay", "autoplayTimeout", "autoplayHoverPause", "autoplaySpeed", "smartSpeed", "fluidSpeed", "responsive", "items", "margin", "nav", "stagePadding", "center", "animateOut", "animateIn", "ngOnInit", "loadTrendingProducts", "subscribeTrendingProducts", "subscribeLikedProducts", "updateResponsiveSettings", "setupResizeListener", "checkMobileDevice", "ngOnDestroy", "unsubscribe", "stopAutoSlide", "add", "trendingProducts$", "subscribe", "products", "updateSliderOnProductsLoad", "likedProducts$", "_this", "_asyncToGenerator", "console", "product", "navigate", "event", "_this2", "stopPropagation", "result", "likeProduct", "success", "log", "message", "_this3", "productUrl", "window", "location", "origin", "navigator", "clipboard", "writeText", "shareProduct", "platform", "_this4", "addToCart", "_this5", "addToWishlist", "Math", "round", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "format", "onViewAll", "queryParams", "filter", "productId", "has", "index", "startAutoSlide", "autoSlideInterval", "setInterval", "length", "autoSlideNext", "clearInterval", "updateSlideOffset", "width", "innerWidth", "updateSliderLimits", "addEventListener", "max", "restartAutoSlideAfterInteraction", "setTimeout", "share", "title", "text", "href", "toFixed", "toString", "ɵɵdirectiveInject", "i1", "TrendingService", "i2", "SocialInteractionsService", "i3", "CartService", "i4", "WishlistService", "i5", "Router", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "TrendingProductsComponent_Template", "rf", "ctx", "TrendingProductsComponent_div_3_Template", "TrendingProductsComponent_Template_button_click_11_listener", "TrendingProductsComponent_div_14_Template", "TrendingProductsComponent_div_15_Template", "TrendingProductsComponent_div_16_Template", "TrendingProductsComponent_div_17_Template", "i6", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i7", "IonIcon", "styles"], "sources": ["E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\features\\home\\components\\trending-products\\trending-products.component.ts", "E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\features\\home\\components\\trending-products\\trending-products.component.html"], "sourcesContent": ["import { Component, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { Router } from '@angular/router';\r\nimport { Subscription } from 'rxjs';\r\nimport { TrendingService } from '../../../../core/services/trending.service';\r\nimport { Product } from '../../../../core/models/product.model';\r\nimport { SocialInteractionsService } from '../../../../core/services/social-interactions.service';\r\nimport { CartService } from '../../../../core/services/cart.service';\r\nimport { WishlistService } from '../../../../core/services/wishlist.service';\r\nimport { IonicModule } from '@ionic/angular';\r\nimport { CarouselModule, OwlOptions } from 'ngx-owl-carousel-o';\r\n\r\n@Component({\r\n  selector: 'app-trending-products',\r\n  standalone: true,\r\n  imports: [CommonModule, IonicModule, CarouselModule],\r\n  templateUrl: './trending-products.component.html',\r\n  styleUrls: ['./trending-products.component.scss']\r\n})\r\nexport class TrendingProductsComponent implements OnInit, OnDestroy {\r\n  trendingProducts: Product[] = [];\r\n  isLoading = true;\r\n  error: string | null = null;\r\n  likedProducts = new Set<string>();\r\n  private subscription: Subscription = new Subscription();\r\n\r\n  // Slider properties\r\n  currentSlide = 0;\r\n  slideOffset = 0;\r\n  cardWidth = 280; // Width of each product card including margin\r\n  visibleCards = 4; // Number of cards visible at once\r\n  maxSlide = 0;\r\n\r\n  // Auto-sliding properties\r\n  autoSlideInterval: any;\r\n  autoSlideDelay = 3000; // 3 seconds\r\n  isAutoSliding = true;\r\n  isPaused = false;\r\n\r\n  // Section interaction properties\r\n  isSectionLiked = false;\r\n  isSectionBookmarked = false;\r\n  sectionLikes = 365;\r\n  sectionComments = 105;\r\n  isMobile = false;\r\n\r\n  // Owl Carousel Options with Auto-sliding\r\n  carouselOptions: OwlOptions = {\r\n    loop: true,\r\n    mouseDrag: true,\r\n    touchDrag: true,\r\n    pullDrag: false,\r\n    dots: true,\r\n    navSpeed: 700,\r\n    navText: [\r\n      '<ion-icon name=\"chevron-back\"></ion-icon>',\r\n      '<ion-icon name=\"chevron-forward\"></ion-icon>'\r\n    ],\r\n    autoplay: true,\r\n    autoplayTimeout: 4000,        // 4 seconds between slides\r\n    autoplayHoverPause: true,     // Pause on hover\r\n    autoplaySpeed: 1000,          // Smooth 1 second transition\r\n    smartSpeed: 1000,             // Smart speed for better UX\r\n    fluidSpeed: true,             // Fluid speed calculation\r\n    responsive: {\r\n      0: {\r\n        items: 1,\r\n        margin: 10,\r\n        nav: false,               // Hide nav on mobile for cleaner look\r\n        dots: true\r\n      },\r\n      576: {\r\n        items: 2,\r\n        margin: 15,\r\n        nav: true,\r\n        dots: true\r\n      },\r\n      768: {\r\n        items: 3,\r\n        margin: 20,\r\n        nav: true,\r\n        dots: true\r\n      },\r\n      992: {\r\n        items: 4,\r\n        margin: 20,\r\n        nav: true,\r\n        dots: false               // Hide dots on desktop, show nav instead\r\n      }\r\n    },\r\n    nav: true,\r\n    margin: 20,\r\n    stagePadding: 0,\r\n    center: false,\r\n    animateOut: false,\r\n    animateIn: false\r\n  };\r\n\r\n  constructor(\r\n    private trendingService: TrendingService,\r\n    private socialService: SocialInteractionsService,\r\n    private cartService: CartService,\r\n    private wishlistService: WishlistService,\r\n    private router: Router\r\n  ) {}\r\n\r\n  ngOnInit() {\r\n    this.loadTrendingProducts();\r\n    this.subscribeTrendingProducts();\r\n    this.subscribeLikedProducts();\r\n    this.updateResponsiveSettings();\r\n    this.setupResizeListener();\r\n    this.checkMobileDevice();\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.subscription.unsubscribe();\r\n    this.stopAutoSlide();\r\n  }\r\n\r\n  private subscribeTrendingProducts() {\r\n    this.subscription.add(\r\n      this.trendingService.trendingProducts$.subscribe(products => {\r\n        this.trendingProducts = products;\r\n        this.isLoading = false;\r\n        this.updateSliderOnProductsLoad();\r\n      })\r\n    );\r\n  }\r\n\r\n  private subscribeLikedProducts() {\r\n    this.subscription.add(\r\n      this.socialService.likedProducts$.subscribe(likedProducts => {\r\n        this.likedProducts = likedProducts;\r\n      })\r\n    );\r\n  }\r\n\r\n  private async loadTrendingProducts() {\r\n    try {\r\n      this.isLoading = true;\r\n      this.error = null;\r\n      await this.trendingService.loadTrendingProducts(1, 8);\r\n    } catch (error) {\r\n      console.error('Error loading trending products:', error);\r\n      this.error = 'Failed to load trending products';\r\n      this.isLoading = false;\r\n    }\r\n  }\r\n\r\n  onProductClick(product: Product) {\r\n    this.router.navigate(['/product', product._id]);\r\n  }\r\n\r\n  async onLikeProduct(product: Product, event: Event) {\r\n    event.stopPropagation();\r\n    try {\r\n      const result = await this.socialService.likeProduct(product._id);\r\n      if (result.success) {\r\n        console.log(result.message);\r\n      } else {\r\n        console.error('Failed to like product:', result.message);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error liking product:', error);\r\n    }\r\n  }\r\n\r\n  async onShareProduct(product: Product, event: Event) {\r\n    event.stopPropagation();\r\n    try {\r\n      // For now, copy link to clipboard\r\n      const productUrl = `${window.location.origin}/product/${product._id}`;\r\n      await navigator.clipboard.writeText(productUrl);\r\n\r\n      // Track the share\r\n      await this.socialService.shareProduct(product._id, {\r\n        platform: 'copy_link',\r\n        message: `Check out this amazing ${product.name} from ${product.brand}!`\r\n      });\r\n\r\n      console.log('Product link copied to clipboard!');\r\n    } catch (error) {\r\n      console.error('Error sharing product:', error);\r\n    }\r\n  }\r\n\r\n  async onAddToCart(product: Product, event: Event) {\r\n    event.stopPropagation();\r\n    try {\r\n      await this.cartService.addToCart(product._id, 1);\r\n      console.log('Product added to cart!');\r\n    } catch (error) {\r\n      console.error('Error adding to cart:', error);\r\n    }\r\n  }\r\n\r\n  async onAddToWishlist(product: Product, event: Event) {\r\n    event.stopPropagation();\r\n    try {\r\n      await this.wishlistService.addToWishlist(product._id);\r\n      console.log('Product added to wishlist!');\r\n    } catch (error) {\r\n      console.error('Error adding to wishlist:', error);\r\n    }\r\n  }\r\n\r\n  getDiscountPercentage(product: Product): number {\r\n    if (product.originalPrice && product.originalPrice > product.price) {\r\n      return Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100);\r\n    }\r\n    return 0;\r\n  }\r\n\r\n  formatPrice(price: number): string {\r\n    return new Intl.NumberFormat('en-IN', {\r\n      style: 'currency',\r\n      currency: 'INR',\r\n      minimumFractionDigits: 0\r\n    }).format(price);\r\n  }\r\n\r\n  onRetry() {\r\n    this.loadTrendingProducts();\r\n  }\r\n\r\n  onViewAll() {\r\n    this.router.navigate(['/products'], {\r\n      queryParams: { filter: 'trending' }\r\n    });\r\n  }\r\n\r\n  isProductLiked(productId: string): boolean {\r\n    return this.likedProducts.has(productId);\r\n  }\r\n\r\n  trackByProductId(index: number, product: Product): string {\r\n    return product._id;\r\n  }\r\n\r\n  // Auto-sliding methods\r\n  private startAutoSlide() {\r\n    if (!this.isAutoSliding || this.isPaused) return;\r\n\r\n    this.stopAutoSlide();\r\n    this.autoSlideInterval = setInterval(() => {\r\n      if (!this.isPaused && this.trendingProducts.length > this.visibleCards) {\r\n        this.autoSlideNext();\r\n      }\r\n    }, this.autoSlideDelay);\r\n  }\r\n\r\n  private stopAutoSlide() {\r\n    if (this.autoSlideInterval) {\r\n      clearInterval(this.autoSlideInterval);\r\n      this.autoSlideInterval = null;\r\n    }\r\n  }\r\n\r\n  private autoSlideNext() {\r\n    if (this.currentSlide >= this.maxSlide) {\r\n      // Reset to beginning for infinite loop\r\n      this.currentSlide = 0;\r\n    } else {\r\n      this.currentSlide++;\r\n    }\r\n    this.updateSlideOffset();\r\n  }\r\n\r\n  pauseAutoSlide() {\r\n    this.isPaused = true;\r\n    this.stopAutoSlide();\r\n  }\r\n\r\n  resumeAutoSlide() {\r\n    this.isPaused = false;\r\n    this.startAutoSlide();\r\n  }\r\n\r\n  // Responsive methods\r\n  private updateResponsiveSettings() {\r\n    const width = window.innerWidth;\r\n    if (width <= 480) {\r\n      this.cardWidth = 195; // 180px + 15px gap\r\n      this.visibleCards = 1;\r\n    } else if (width <= 768) {\r\n      this.cardWidth = 215; // 200px + 15px gap\r\n      this.visibleCards = 2;\r\n    } else if (width <= 1200) {\r\n      this.cardWidth = 260; // 240px + 20px gap\r\n      this.visibleCards = 3;\r\n    } else {\r\n      this.cardWidth = 280; // 260px + 20px gap\r\n      this.visibleCards = 4;\r\n    }\r\n    this.updateSliderLimits();\r\n    this.updateSlideOffset();\r\n  }\r\n\r\n  private setupResizeListener() {\r\n    window.addEventListener('resize', () => {\r\n      this.updateResponsiveSettings();\r\n    });\r\n  }\r\n\r\n  // Slider methods\r\n  updateSliderLimits() {\r\n    this.maxSlide = Math.max(0, this.trendingProducts.length - this.visibleCards);\r\n  }\r\n\r\n  slidePrev() {\r\n    if (this.currentSlide > 0) {\r\n      this.currentSlide--;\r\n      this.updateSlideOffset();\r\n      this.restartAutoSlideAfterInteraction();\r\n    }\r\n  }\r\n\r\n  slideNext() {\r\n    if (this.currentSlide < this.maxSlide) {\r\n      this.currentSlide++;\r\n      this.updateSlideOffset();\r\n      this.restartAutoSlideAfterInteraction();\r\n    }\r\n  }\r\n\r\n  private restartAutoSlideAfterInteraction() {\r\n    this.stopAutoSlide();\r\n    setTimeout(() => {\r\n      this.startAutoSlide();\r\n    }, 2000); // Wait 2 seconds before resuming auto-slide\r\n  }\r\n\r\n  private updateSlideOffset() {\r\n    this.slideOffset = -this.currentSlide * this.cardWidth;\r\n  }\r\n\r\n  // Update slider when products load\r\n  private updateSliderOnProductsLoad() {\r\n    setTimeout(() => {\r\n      this.updateSliderLimits();\r\n      this.currentSlide = 0;\r\n      this.slideOffset = 0;\r\n      this.startAutoSlide();\r\n    }, 100);\r\n  }\r\n\r\n  // Section interaction methods\r\n  toggleSectionLike() {\r\n    this.isSectionLiked = !this.isSectionLiked;\r\n    if (this.isSectionLiked) {\r\n      this.sectionLikes++;\r\n    } else {\r\n      this.sectionLikes--;\r\n    }\r\n  }\r\n\r\n  toggleSectionBookmark() {\r\n    this.isSectionBookmarked = !this.isSectionBookmarked;\r\n  }\r\n\r\n  openComments() {\r\n    // Open comments modal/sheet\r\n    console.log('Opening comments for trending products section');\r\n  }\r\n\r\n  shareSection() {\r\n    // Share section functionality\r\n    if (navigator.share) {\r\n      navigator.share({\r\n        title: 'Trending Products',\r\n        text: 'Check out these trending fashion products!',\r\n        url: window.location.href\r\n      });\r\n    } else {\r\n      // Fallback for browsers that don't support Web Share API\r\n      navigator.clipboard.writeText(window.location.href);\r\n      console.log('Link copied to clipboard');\r\n    }\r\n  }\r\n\r\n  openMusicPlayer() {\r\n    // Open music player for section\r\n    console.log('Opening music player for trending products');\r\n  }\r\n\r\n  formatCount(count: number): string {\r\n    if (count >= 1000000) {\r\n      return (count / 1000000).toFixed(1) + 'M';\r\n    } else if (count >= 1000) {\r\n      return (count / 1000).toFixed(1) + 'K';\r\n    }\r\n    return count.toString();\r\n  }\r\n\r\n  private checkMobileDevice() {\r\n    this.isMobile = window.innerWidth <= 768;\r\n  }\r\n}\r\n", "<div class=\"trending-products-container\">\n  <!-- Test visibility -->\n  <div style=\"background: cyan; color: black; padding: 20px; margin: 10px 0; font-weight: bold;\">\n    TRENDING PRODUCTS COMPONENT IS VISIBLE\n  </div>\n  <!-- Mobile Action Buttons (TikTok/Instagram Style) -->\n  <div class=\"mobile-action-buttons\" *ngIf=\"isMobile\">\n    <button class=\"action-btn like-btn\"\n            [class.active]=\"isSectionLiked\"\n            (click)=\"toggleSectionLike()\">\n      <ion-icon [name]=\"isSectionLiked ? 'heart' : 'heart-outline'\"></ion-icon>\n      <span class=\"action-count\">{{ formatCount(sectionLikes) }}</span>\n    </button>\n\n    <button class=\"action-btn comment-btn\" (click)=\"openComments()\">\n      <ion-icon name=\"chatbubble-outline\"></ion-icon>\n      <span class=\"action-count\">{{ formatCount(sectionComments) }}</span>\n    </button>\n\n    <button class=\"action-btn share-btn\" (click)=\"shareSection()\">\n      <ion-icon name=\"arrow-redo-outline\"></ion-icon>\n      <span class=\"action-text\">Share</span>\n    </button>\n\n    <button class=\"action-btn bookmark-btn\"\n            [class.active]=\"isSectionBookmarked\"\n            (click)=\"toggleSectionBookmark()\">\n      <ion-icon [name]=\"isSectionBookmarked ? 'bookmark' : 'bookmark-outline'\"></ion-icon>\n    </button>\n\n    <button class=\"action-btn music-btn\" (click)=\"openMusicPlayer()\">\n      <ion-icon name=\"musical-notes\"></ion-icon>\n      <span class=\"action-text\">Music</span>\n    </button>\n  </div>\n\n  <!-- Header -->\n  <div class=\"section-header\">\n    <div class=\"header-content\">\n      <h2 class=\"section-title\">\n        <ion-icon name=\"trending-up\" class=\"title-icon\"></ion-icon>\n        Trending Now\n      </h2>\n      <p class=\"section-subtitle\">Most popular products this week</p>\n    </div>\n    <button class=\"view-all-btn\" (click)=\"onViewAll()\">\n      View All\n      <ion-icon name=\"chevron-forward\"></ion-icon>\n    </button>\n  </div>\n\n  <!-- Loading State -->\n  <div *ngIf=\"isLoading\" class=\"loading-container\">\n    <div class=\"loading-grid\">\n      <div *ngFor=\"let item of [1,2,3,4,5,6,7,8]\" class=\"loading-card\">\n        <div class=\"loading-image\"></div>\n        <div class=\"loading-content\">\n          <div class=\"loading-line short\"></div>\n          <div class=\"loading-line medium\"></div>\n          <div class=\"loading-line long\"></div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Error State -->\n  <div *ngIf=\"error && !isLoading\" class=\"error-container\">\n    <ion-icon name=\"alert-circle\" class=\"error-icon\"></ion-icon>\n    <p class=\"error-message\">{{ error }}</p>\n    <button class=\"retry-btn\" (click)=\"onRetry()\">\n      <ion-icon name=\"refresh\"></ion-icon>\n      Try Again\n    </button>\n  </div>\n\n  <!-- Products Slider -->\n  <div *ngIf=\"!isLoading && !error && trendingProducts.length > 0\" class=\"products-slider-container\">\n    <!-- Navigation Buttons -->\n    <button class=\"slider-nav prev-btn\" (click)=\"slidePrev()\" [disabled]=\"currentSlide === 0\">\n      <ion-icon name=\"chevron-back\"></ion-icon>\n    </button>\n    <button class=\"slider-nav next-btn\" (click)=\"slideNext()\" [disabled]=\"currentSlide >= maxSlide\">\n      <ion-icon name=\"chevron-forward\"></ion-icon>\n    </button>\n\n    <!-- Slider Wrapper -->\n    <div class=\"products-slider-wrapper\" (mouseenter)=\"pauseAutoSlide()\" (mouseleave)=\"resumeAutoSlide()\">\n      <div class=\"products-slider\" [style.transform]=\"'translateX(' + slideOffset + 'px)'\">\n        <div\n          *ngFor=\"let product of trendingProducts; trackBy: trackByProductId\"\n          class=\"product-card\"\n          (click)=\"onProductClick(product)\"\n        >\n\n      <div class=\"product-image-container\">\n        <img \n          [src]=\"product.images[0].url\"\n          [alt]=\"product.images[0].alt || product.name\"\n          class=\"product-image\"\n          loading=\"lazy\"\n        />\n        \n      \n        <div class=\"trending-badge\">\n          <ion-icon name=\"trending-up\"></ion-icon>\n          Trending\n        </div>\n\n      \n        <div *ngIf=\"getDiscountPercentage(product) > 0\" class=\"discount-badge\">\n          {{ getDiscountPercentage(product) }}% OFF\n        </div>\n\n       \n        <div class=\"action-buttons\">\n          <button\n            class=\"action-btn like-btn\"\n            [class.liked]=\"isProductLiked(product._id)\"\n            (click)=\"onLikeProduct(product, $event)\"\n            [attr.aria-label]=\"'Like ' + product.name\"\n          >\n            <ion-icon [name]=\"isProductLiked(product._id) ? 'heart' : 'heart-outline'\"></ion-icon>\n          </button>\n          <button \n            class=\"action-btn share-btn\" \n            (click)=\"onShareProduct(product, $event)\"\n            [attr.aria-label]=\"'Share ' + product.name\"\n          >\n            <ion-icon name=\"share-outline\"></ion-icon>\n          </button>\n        </div>\n      </div>\n\n    \n      <div class=\"product-info\">\n        <div class=\"product-brand\">{{ product.brand }}</div>\n        <h3 class=\"product-name\">{{ product.name }}</h3>\n        \n      \n        <div class=\"price-section\">\n          <span class=\"current-price\">{{ formatPrice(product.price) }}</span>\n          <span *ngIf=\"product.originalPrice && product.originalPrice > product.price\" \n                class=\"original-price\">{{ formatPrice(product.originalPrice) }}</span>\n        </div>\n\n       \n        <div class=\"rating-section\">\n          <div class=\"stars\">\n            <ion-icon \n              *ngFor=\"let star of [1,2,3,4,5]\" \n              [name]=\"star <= product.rating.average ? 'star' : 'star-outline'\"\n              [class.filled]=\"star <= product.rating.average\"\n            ></ion-icon>\n          </div>\n          <span class=\"rating-text\">({{ product.rating.count }})</span>\n        </div>\n\n        <!-- Action Buttons -->\n        <div class=\"product-actions\">\n          <button \n            class=\"cart-btn\" \n            (click)=\"onAddToCart(product, $event)\"\n          >\n            <ion-icon name=\"bag-add-outline\"></ion-icon>\n            Add to Cart\n          </button>\n          <button \n            class=\"wishlist-btn\" \n            (click)=\"onAddToWishlist(product, $event)\"\n          >\n            <ion-icon name=\"heart-outline\"></ion-icon>\n          </button>\n        </div>\n      </div>\n    </div>\n    </div> <!-- End products-slider -->\n    </div> <!-- End products-slider-wrapper -->\n  </div> <!-- End products-slider-container -->\n\n  <!-- Empty State -->\n  <div *ngIf=\"!isLoading && !error && trendingProducts.length === 0\" class=\"empty-container\">\n    <ion-icon name=\"trending-up-outline\" class=\"empty-icon\"></ion-icon>\n    <h3 class=\"empty-title\">No Trending Products</h3>\n    <p class=\"empty-message\">Check back later for trending items</p>\n  </div>\n</div>\n"], "mappings": ";AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,YAAY,QAAQ,MAAM;AAMnC,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,cAAc,QAAoB,oBAAoB;;;;;;;;;;;;;;ICH3DC,EADF,CAAAC,cAAA,cAAoD,iBAGZ;IAA9BD,EAAA,CAAAE,UAAA,mBAAAC,iEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,iBAAA,EAAmB;IAAA,EAAC;IACnCT,EAAA,CAAAU,SAAA,mBAAyE;IACzEV,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAW,MAAA,GAA+B;IAC5DX,EAD4D,CAAAY,YAAA,EAAO,EAC1D;IAETZ,EAAA,CAAAC,cAAA,iBAAgE;IAAzBD,EAAA,CAAAE,UAAA,mBAAAW,iEAAA;MAAAb,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAQ,YAAA,EAAc;IAAA,EAAC;IAC7Dd,EAAA,CAAAU,SAAA,mBAA+C;IAC/CV,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAW,MAAA,GAAkC;IAC/DX,EAD+D,CAAAY,YAAA,EAAO,EAC7D;IAETZ,EAAA,CAAAC,cAAA,iBAA8D;IAAzBD,EAAA,CAAAE,UAAA,mBAAAa,iEAAA;MAAAf,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAU,YAAA,EAAc;IAAA,EAAC;IAC3DhB,EAAA,CAAAU,SAAA,oBAA+C;IAC/CV,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAW,MAAA,aAAK;IACjCX,EADiC,CAAAY,YAAA,EAAO,EAC/B;IAETZ,EAAA,CAAAC,cAAA,kBAE0C;IAAlCD,EAAA,CAAAE,UAAA,mBAAAe,kEAAA;MAAAjB,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAY,qBAAA,EAAuB;IAAA,EAAC;IACvClB,EAAA,CAAAU,SAAA,oBAAoF;IACtFV,EAAA,CAAAY,YAAA,EAAS;IAETZ,EAAA,CAAAC,cAAA,kBAAiE;IAA5BD,EAAA,CAAAE,UAAA,mBAAAiB,kEAAA;MAAAnB,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAc,eAAA,EAAiB;IAAA,EAAC;IAC9DpB,EAAA,CAAAU,SAAA,oBAA0C;IAC1CV,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAW,MAAA,aAAK;IAEnCX,EAFmC,CAAAY,YAAA,EAAO,EAC/B,EACL;;;;IA1BIZ,EAAA,CAAAqB,SAAA,EAA+B;IAA/BrB,EAAA,CAAAsB,WAAA,WAAAhB,MAAA,CAAAiB,cAAA,CAA+B;IAE3BvB,EAAA,CAAAqB,SAAA,EAAmD;IAAnDrB,EAAA,CAAAwB,UAAA,SAAAlB,MAAA,CAAAiB,cAAA,6BAAmD;IAClCvB,EAAA,CAAAqB,SAAA,GAA+B;IAA/BrB,EAAA,CAAAyB,iBAAA,CAAAnB,MAAA,CAAAoB,WAAA,CAAApB,MAAA,CAAAqB,YAAA,EAA+B;IAK/B3B,EAAA,CAAAqB,SAAA,GAAkC;IAAlCrB,EAAA,CAAAyB,iBAAA,CAAAnB,MAAA,CAAAoB,WAAA,CAAApB,MAAA,CAAAsB,eAAA,EAAkC;IASvD5B,EAAA,CAAAqB,SAAA,GAAoC;IAApCrB,EAAA,CAAAsB,WAAA,WAAAhB,MAAA,CAAAuB,mBAAA,CAAoC;IAEhC7B,EAAA,CAAAqB,SAAA,EAA8D;IAA9DrB,EAAA,CAAAwB,UAAA,SAAAlB,MAAA,CAAAuB,mBAAA,mCAA8D;;;;;IA2BxE7B,EAAA,CAAAC,cAAA,cAAiE;IAC/DD,EAAA,CAAAU,SAAA,cAAiC;IACjCV,EAAA,CAAAC,cAAA,cAA6B;IAG3BD,EAFA,CAAAU,SAAA,cAAsC,cACC,cACF;IAEzCV,EADE,CAAAY,YAAA,EAAM,EACF;;;;;IARRZ,EADF,CAAAC,cAAA,cAAiD,cACrB;IACxBD,EAAA,CAAA8B,UAAA,IAAAC,+CAAA,kBAAiE;IASrE/B,EADE,CAAAY,YAAA,EAAM,EACF;;;IAToBZ,EAAA,CAAAqB,SAAA,GAAoB;IAApBrB,EAAA,CAAAwB,UAAA,YAAAxB,EAAA,CAAAgC,eAAA,IAAAC,GAAA,EAAoB;;;;;;IAY9CjC,EAAA,CAAAC,cAAA,cAAyD;IACvDD,EAAA,CAAAU,SAAA,mBAA4D;IAC5DV,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAW,MAAA,GAAW;IAAAX,EAAA,CAAAY,YAAA,EAAI;IACxCZ,EAAA,CAAAC,cAAA,iBAA8C;IAApBD,EAAA,CAAAE,UAAA,mBAAAgC,kEAAA;MAAAlC,EAAA,CAAAI,aAAA,CAAA+B,GAAA;MAAA,MAAA7B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA8B,OAAA,EAAS;IAAA,EAAC;IAC3CpC,EAAA,CAAAU,SAAA,mBAAoC;IACpCV,EAAA,CAAAW,MAAA,kBACF;IACFX,EADE,CAAAY,YAAA,EAAS,EACL;;;;IALqBZ,EAAA,CAAAqB,SAAA,GAAW;IAAXrB,EAAA,CAAAyB,iBAAA,CAAAnB,MAAA,CAAA+B,KAAA,CAAW;;;;;IAyChCrC,EAAA,CAAAC,cAAA,cAAuE;IACrED,EAAA,CAAAW,MAAA,GACF;IAAAX,EAAA,CAAAY,YAAA,EAAM;;;;;IADJZ,EAAA,CAAAqB,SAAA,EACF;IADErB,EAAA,CAAAsC,kBAAA,MAAAhC,MAAA,CAAAiC,qBAAA,CAAAC,UAAA,YACF;;;;;IA8BExC,EAAA,CAAAC,cAAA,eAC6B;IAAAD,EAAA,CAAAW,MAAA,GAAwC;IAAAX,EAAA,CAAAY,YAAA,EAAO;;;;;IAA/CZ,EAAA,CAAAqB,SAAA,EAAwC;IAAxCrB,EAAA,CAAAyB,iBAAA,CAAAnB,MAAA,CAAAmC,WAAA,CAAAD,UAAA,CAAAE,aAAA,EAAwC;;;;;IAMnE1C,EAAA,CAAAU,SAAA,mBAIY;;;;;IADVV,EAAA,CAAAsB,WAAA,WAAAqB,OAAA,IAAAH,UAAA,CAAAI,MAAA,CAAAC,OAAA,CAA+C;IAD/C7C,EAAA,CAAAwB,UAAA,SAAAmB,OAAA,IAAAH,UAAA,CAAAI,MAAA,CAAAC,OAAA,2BAAiE;;;;;;IA9DvE7C,EAAA,CAAAC,cAAA,cAIC;IADCD,EAAA,CAAAE,UAAA,mBAAA4C,qEAAA;MAAA,MAAAN,UAAA,GAAAxC,EAAA,CAAAI,aAAA,CAAA2C,GAAA,EAAAC,SAAA;MAAA,MAAA1C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA2C,cAAA,CAAAT,UAAA,CAAuB;IAAA,EAAC;IAGrCxC,EAAA,CAAAC,cAAA,cAAqC;IACnCD,EAAA,CAAAU,SAAA,cAKE;IAGFV,EAAA,CAAAC,cAAA,cAA4B;IAC1BD,EAAA,CAAAU,SAAA,mBAAwC;IACxCV,EAAA,CAAAW,MAAA,iBACF;IAAAX,EAAA,CAAAY,YAAA,EAAM;IAGNZ,EAAA,CAAA8B,UAAA,IAAAoB,qDAAA,kBAAuE;IAMrElD,EADF,CAAAC,cAAA,cAA4B,iBAMzB;IAFCD,EAAA,CAAAE,UAAA,mBAAAiD,wEAAAC,MAAA;MAAA,MAAAZ,UAAA,GAAAxC,EAAA,CAAAI,aAAA,CAAA2C,GAAA,EAAAC,SAAA;MAAA,MAAA1C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA+C,aAAA,CAAAb,UAAA,EAAAY,MAAA,CAA8B;IAAA,EAAC;IAGxCpD,EAAA,CAAAU,SAAA,mBAAsF;IACxFV,EAAA,CAAAY,YAAA,EAAS;IACTZ,EAAA,CAAAC,cAAA,kBAIC;IAFCD,EAAA,CAAAE,UAAA,mBAAAoD,yEAAAF,MAAA;MAAA,MAAAZ,UAAA,GAAAxC,EAAA,CAAAI,aAAA,CAAA2C,GAAA,EAAAC,SAAA;MAAA,MAAA1C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAiD,cAAA,CAAAf,UAAA,EAAAY,MAAA,CAA+B;IAAA,EAAC;IAGzCpD,EAAA,CAAAU,SAAA,oBAA0C;IAGhDV,EAFI,CAAAY,YAAA,EAAS,EACL,EACF;IAIJZ,EADF,CAAAC,cAAA,eAA0B,eACG;IAAAD,EAAA,CAAAW,MAAA,IAAmB;IAAAX,EAAA,CAAAY,YAAA,EAAM;IACpDZ,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAW,MAAA,IAAkB;IAAAX,EAAA,CAAAY,YAAA,EAAK;IAI9CZ,EADF,CAAAC,cAAA,eAA2B,gBACG;IAAAD,EAAA,CAAAW,MAAA,IAAgC;IAAAX,EAAA,CAAAY,YAAA,EAAO;IACnEZ,EAAA,CAAA8B,UAAA,KAAA0B,uDAAA,mBAC6B;IAC/BxD,EAAA,CAAAY,YAAA,EAAM;IAIJZ,EADF,CAAAC,cAAA,eAA4B,eACP;IACjBD,EAAA,CAAA8B,UAAA,KAAA2B,2DAAA,uBAIC;IACHzD,EAAA,CAAAY,YAAA,EAAM;IACNZ,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAW,MAAA,IAA4B;IACxDX,EADwD,CAAAY,YAAA,EAAO,EACzD;IAIJZ,EADF,CAAAC,cAAA,eAA6B,kBAI1B;IADCD,EAAA,CAAAE,UAAA,mBAAAwD,yEAAAN,MAAA;MAAA,MAAAZ,UAAA,GAAAxC,EAAA,CAAAI,aAAA,CAAA2C,GAAA,EAAAC,SAAA;MAAA,MAAA1C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAqD,WAAA,CAAAnB,UAAA,EAAAY,MAAA,CAA4B;IAAA,EAAC;IAEtCpD,EAAA,CAAAU,SAAA,oBAA4C;IAC5CV,EAAA,CAAAW,MAAA,qBACF;IAAAX,EAAA,CAAAY,YAAA,EAAS;IACTZ,EAAA,CAAAC,cAAA,kBAGC;IADCD,EAAA,CAAAE,UAAA,mBAAA0D,yEAAAR,MAAA;MAAA,MAAAZ,UAAA,GAAAxC,EAAA,CAAAI,aAAA,CAAA2C,GAAA,EAAAC,SAAA;MAAA,MAAA1C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAuD,eAAA,CAAArB,UAAA,EAAAY,MAAA,CAAgC;IAAA,EAAC;IAE1CpD,EAAA,CAAAU,SAAA,oBAA0C;IAIlDV,EAHM,CAAAY,YAAA,EAAS,EACL,EACF,EACF;;;;;IA9EAZ,EAAA,CAAAqB,SAAA,GAA6B;IAC7BrB,EADA,CAAAwB,UAAA,QAAAgB,UAAA,CAAAsB,MAAA,IAAAC,GAAA,EAAA/D,EAAA,CAAAgE,aAAA,CAA6B,QAAAxB,UAAA,CAAAsB,MAAA,IAAAG,GAAA,IAAAzB,UAAA,CAAA0B,IAAA,CACgB;IAYzClE,EAAA,CAAAqB,SAAA,GAAwC;IAAxCrB,EAAA,CAAAwB,UAAA,SAAAlB,MAAA,CAAAiC,qBAAA,CAAAC,UAAA,MAAwC;IAQ1CxC,EAAA,CAAAqB,SAAA,GAA2C;IAA3CrB,EAAA,CAAAsB,WAAA,UAAAhB,MAAA,CAAA6D,cAAA,CAAA3B,UAAA,CAAA4B,GAAA,EAA2C;;IAIjCpE,EAAA,CAAAqB,SAAA,EAAgE;IAAhErB,EAAA,CAAAwB,UAAA,SAAAlB,MAAA,CAAA6D,cAAA,CAAA3B,UAAA,CAAA4B,GAAA,8BAAgE;IAK1EpE,EAAA,CAAAqB,SAAA,EAA2C;;IASpBrB,EAAA,CAAAqB,SAAA,GAAmB;IAAnBrB,EAAA,CAAAyB,iBAAA,CAAAe,UAAA,CAAA6B,KAAA,CAAmB;IACrBrE,EAAA,CAAAqB,SAAA,GAAkB;IAAlBrB,EAAA,CAAAyB,iBAAA,CAAAe,UAAA,CAAA0B,IAAA,CAAkB;IAIblE,EAAA,CAAAqB,SAAA,GAAgC;IAAhCrB,EAAA,CAAAyB,iBAAA,CAAAnB,MAAA,CAAAmC,WAAA,CAAAD,UAAA,CAAA8B,KAAA,EAAgC;IACrDtE,EAAA,CAAAqB,SAAA,EAAoE;IAApErB,EAAA,CAAAwB,UAAA,SAAAgB,UAAA,CAAAE,aAAA,IAAAF,UAAA,CAAAE,aAAA,GAAAF,UAAA,CAAA8B,KAAA,CAAoE;IAQtDtE,EAAA,CAAAqB,SAAA,GAAc;IAAdrB,EAAA,CAAAwB,UAAA,YAAAxB,EAAA,CAAAgC,eAAA,KAAAuC,GAAA,EAAc;IAKTvE,EAAA,CAAAqB,SAAA,GAA4B;IAA5BrB,EAAA,CAAAsC,kBAAA,MAAAE,UAAA,CAAAI,MAAA,CAAA4B,KAAA,MAA4B;;;;;;IA5E5DxE,EAFF,CAAAC,cAAA,cAAmG,iBAEP;IAAtDD,EAAA,CAAAE,UAAA,mBAAAuE,kEAAA;MAAAzE,EAAA,CAAAI,aAAA,CAAAsE,GAAA;MAAA,MAAApE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAqE,SAAA,EAAW;IAAA,EAAC;IACvD3E,EAAA,CAAAU,SAAA,mBAAyC;IAC3CV,EAAA,CAAAY,YAAA,EAAS;IACTZ,EAAA,CAAAC,cAAA,iBAAgG;IAA5DD,EAAA,CAAAE,UAAA,mBAAA0E,kEAAA;MAAA5E,EAAA,CAAAI,aAAA,CAAAsE,GAAA;MAAA,MAAApE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAuE,SAAA,EAAW;IAAA,EAAC;IACvD7E,EAAA,CAAAU,SAAA,kBAA4C;IAC9CV,EAAA,CAAAY,YAAA,EAAS;IAGTZ,EAAA,CAAAC,cAAA,cAAsG;IAAjCD,EAAhC,CAAAE,UAAA,wBAAA4E,oEAAA;MAAA9E,EAAA,CAAAI,aAAA,CAAAsE,GAAA;MAAA,MAAApE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAcF,MAAA,CAAAyE,cAAA,EAAgB;IAAA,EAAC,wBAAAC,oEAAA;MAAAhF,EAAA,CAAAI,aAAA,CAAAsE,GAAA;MAAA,MAAApE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAeF,MAAA,CAAA2E,eAAA,EAAiB;IAAA,EAAC;IACnGjF,EAAA,CAAAC,cAAA,cAAqF;IACnFD,EAAA,CAAA8B,UAAA,IAAAoD,+CAAA,oBAIC;IAqFPlF,EAFE,CAAAY,YAAA,EAAM,EACA,EACF;;;;IAnGsDZ,EAAA,CAAAqB,SAAA,EAA+B;IAA/BrB,EAAA,CAAAwB,UAAA,aAAAlB,MAAA,CAAA6E,YAAA,OAA+B;IAG/BnF,EAAA,CAAAqB,SAAA,GAAqC;IAArCrB,EAAA,CAAAwB,UAAA,aAAAlB,MAAA,CAAA6E,YAAA,IAAA7E,MAAA,CAAA8E,QAAA,CAAqC;IAMhEpF,EAAA,CAAAqB,SAAA,GAAuD;IAAvDrB,EAAA,CAAAqF,WAAA,8BAAA/E,MAAA,CAAAgF,WAAA,SAAuD;IAE5DtF,EAAA,CAAAqB,SAAA,EAAqB;IAAArB,EAArB,CAAAwB,UAAA,YAAAlB,MAAA,CAAAiF,gBAAA,CAAqB,iBAAAjF,MAAA,CAAAkF,gBAAA,CAAyB;;;;;IA2F1ExF,EAAA,CAAAC,cAAA,cAA2F;IACzFD,EAAA,CAAAU,SAAA,mBAAmE;IACnEV,EAAA,CAAAC,cAAA,aAAwB;IAAAD,EAAA,CAAAW,MAAA,2BAAoB;IAAAX,EAAA,CAAAY,YAAA,EAAK;IACjDZ,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAW,MAAA,0CAAmC;IAC9DX,EAD8D,CAAAY,YAAA,EAAI,EAC5D;;;ADrKR,OAAM,MAAO6E,yBAAyB;EA+EpCC,YACUC,eAAgC,EAChCC,aAAwC,EACxCC,WAAwB,EACxBC,eAAgC,EAChCC,MAAc;IAJd,KAAAJ,eAAe,GAAfA,eAAe;IACf,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,MAAM,GAANA,MAAM;IAnFhB,KAAAR,gBAAgB,GAAc,EAAE;IAChC,KAAAS,SAAS,GAAG,IAAI;IAChB,KAAA3D,KAAK,GAAkB,IAAI;IAC3B,KAAA4D,aAAa,GAAG,IAAIC,GAAG,EAAU;IACzB,KAAAC,YAAY,GAAiB,IAAItG,YAAY,EAAE;IAEvD;IACA,KAAAsF,YAAY,GAAG,CAAC;IAChB,KAAAG,WAAW,GAAG,CAAC;IACf,KAAAc,SAAS,GAAG,GAAG,CAAC,CAAC;IACjB,KAAAC,YAAY,GAAG,CAAC,CAAC,CAAC;IAClB,KAAAjB,QAAQ,GAAG,CAAC;IAIZ,KAAAkB,cAAc,GAAG,IAAI,CAAC,CAAC;IACvB,KAAAC,aAAa,GAAG,IAAI;IACpB,KAAAC,QAAQ,GAAG,KAAK;IAEhB;IACA,KAAAjF,cAAc,GAAG,KAAK;IACtB,KAAAM,mBAAmB,GAAG,KAAK;IAC3B,KAAAF,YAAY,GAAG,GAAG;IAClB,KAAAC,eAAe,GAAG,GAAG;IACrB,KAAA6E,QAAQ,GAAG,KAAK;IAEhB;IACA,KAAAC,eAAe,GAAe;MAC5BC,IAAI,EAAE,IAAI;MACVC,SAAS,EAAE,IAAI;MACfC,SAAS,EAAE,IAAI;MACfC,QAAQ,EAAE,KAAK;MACfC,IAAI,EAAE,IAAI;MACVC,QAAQ,EAAE,GAAG;MACbC,OAAO,EAAE,CACP,2CAA2C,EAC3C,8CAA8C,CAC/C;MACDC,QAAQ,EAAE,IAAI;MACdC,eAAe,EAAE,IAAI;MACrBC,kBAAkB,EAAE,IAAI;MACxBC,aAAa,EAAE,IAAI;MACnBC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE;QACV,CAAC,EAAE;UACDC,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE,EAAE;UACVC,GAAG,EAAE,KAAK;UACVZ,IAAI,EAAE;SACP;QACD,GAAG,EAAE;UACHU,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE,EAAE;UACVC,GAAG,EAAE,IAAI;UACTZ,IAAI,EAAE;SACP;QACD,GAAG,EAAE;UACHU,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE,EAAE;UACVC,GAAG,EAAE,IAAI;UACTZ,IAAI,EAAE;SACP;QACD,GAAG,EAAE;UACHU,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE,EAAE;UACVC,GAAG,EAAE,IAAI;UACTZ,IAAI,EAAE,KAAK,CAAe;;OAE7B;MACDY,GAAG,EAAE,IAAI;MACTD,MAAM,EAAE,EAAE;MACVE,YAAY,EAAE,CAAC;MACfC,MAAM,EAAE,KAAK;MACbC,UAAU,EAAE,KAAK;MACjBC,SAAS,EAAE;KACZ;EAQE;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,yBAAyB,EAAE;IAChC,IAAI,CAACC,sBAAsB,EAAE;IAC7B,IAAI,CAACC,wBAAwB,EAAE;IAC/B,IAAI,CAACC,mBAAmB,EAAE;IAC1B,IAAI,CAACC,iBAAiB,EAAE;EAC1B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACpC,YAAY,CAACqC,WAAW,EAAE;IAC/B,IAAI,CAACC,aAAa,EAAE;EACtB;EAEQP,yBAAyBA,CAAA;IAC/B,IAAI,CAAC/B,YAAY,CAACuC,GAAG,CACnB,IAAI,CAAC/C,eAAe,CAACgD,iBAAiB,CAACC,SAAS,CAACC,QAAQ,IAAG;MAC1D,IAAI,CAACtD,gBAAgB,GAAGsD,QAAQ;MAChC,IAAI,CAAC7C,SAAS,GAAG,KAAK;MACtB,IAAI,CAAC8C,0BAA0B,EAAE;IACnC,CAAC,CAAC,CACH;EACH;EAEQX,sBAAsBA,CAAA;IAC5B,IAAI,CAAChC,YAAY,CAACuC,GAAG,CACnB,IAAI,CAAC9C,aAAa,CAACmD,cAAc,CAACH,SAAS,CAAC3C,aAAa,IAAG;MAC1D,IAAI,CAACA,aAAa,GAAGA,aAAa;IACpC,CAAC,CAAC,CACH;EACH;EAEcgC,oBAAoBA,CAAA;IAAA,IAAAe,KAAA;IAAA,OAAAC,iBAAA;MAChC,IAAI;QACFD,KAAI,CAAChD,SAAS,GAAG,IAAI;QACrBgD,KAAI,CAAC3G,KAAK,GAAG,IAAI;QACjB,MAAM2G,KAAI,CAACrD,eAAe,CAACsC,oBAAoB,CAAC,CAAC,EAAE,CAAC,CAAC;OACtD,CAAC,OAAO5F,KAAK,EAAE;QACd6G,OAAO,CAAC7G,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxD2G,KAAI,CAAC3G,KAAK,GAAG,kCAAkC;QAC/C2G,KAAI,CAAChD,SAAS,GAAG,KAAK;;IACvB;EACH;EAEA/C,cAAcA,CAACkG,OAAgB;IAC7B,IAAI,CAACpD,MAAM,CAACqD,QAAQ,CAAC,CAAC,UAAU,EAAED,OAAO,CAAC/E,GAAG,CAAC,CAAC;EACjD;EAEMf,aAAaA,CAAC8F,OAAgB,EAAEE,KAAY;IAAA,IAAAC,MAAA;IAAA,OAAAL,iBAAA;MAChDI,KAAK,CAACE,eAAe,EAAE;MACvB,IAAI;QACF,MAAMC,MAAM,SAASF,MAAI,CAAC1D,aAAa,CAAC6D,WAAW,CAACN,OAAO,CAAC/E,GAAG,CAAC;QAChE,IAAIoF,MAAM,CAACE,OAAO,EAAE;UAClBR,OAAO,CAACS,GAAG,CAACH,MAAM,CAACI,OAAO,CAAC;SAC5B,MAAM;UACLV,OAAO,CAAC7G,KAAK,CAAC,yBAAyB,EAAEmH,MAAM,CAACI,OAAO,CAAC;;OAE3D,CAAC,OAAOvH,KAAK,EAAE;QACd6G,OAAO,CAAC7G,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;;IAC9C;EACH;EAEMkB,cAAcA,CAAC4F,OAAgB,EAAEE,KAAY;IAAA,IAAAQ,MAAA;IAAA,OAAAZ,iBAAA;MACjDI,KAAK,CAACE,eAAe,EAAE;MACvB,IAAI;QACF;QACA,MAAMO,UAAU,GAAG,GAAGC,MAAM,CAACC,QAAQ,CAACC,MAAM,YAAYd,OAAO,CAAC/E,GAAG,EAAE;QACrE,MAAM8F,SAAS,CAACC,SAAS,CAACC,SAAS,CAACN,UAAU,CAAC;QAE/C;QACA,MAAMD,MAAI,CAACjE,aAAa,CAACyE,YAAY,CAAClB,OAAO,CAAC/E,GAAG,EAAE;UACjDkG,QAAQ,EAAE,WAAW;UACrBV,OAAO,EAAE,0BAA0BT,OAAO,CAACjF,IAAI,SAASiF,OAAO,CAAC9E,KAAK;SACtE,CAAC;QAEF6E,OAAO,CAACS,GAAG,CAAC,mCAAmC,CAAC;OACjD,CAAC,OAAOtH,KAAK,EAAE;QACd6G,OAAO,CAAC7G,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;;IAC/C;EACH;EAEMsB,WAAWA,CAACwF,OAAgB,EAAEE,KAAY;IAAA,IAAAkB,MAAA;IAAA,OAAAtB,iBAAA;MAC9CI,KAAK,CAACE,eAAe,EAAE;MACvB,IAAI;QACF,MAAMgB,MAAI,CAAC1E,WAAW,CAAC2E,SAAS,CAACrB,OAAO,CAAC/E,GAAG,EAAE,CAAC,CAAC;QAChD8E,OAAO,CAACS,GAAG,CAAC,wBAAwB,CAAC;OACtC,CAAC,OAAOtH,KAAK,EAAE;QACd6G,OAAO,CAAC7G,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;;IAC9C;EACH;EAEMwB,eAAeA,CAACsF,OAAgB,EAAEE,KAAY;IAAA,IAAAoB,MAAA;IAAA,OAAAxB,iBAAA;MAClDI,KAAK,CAACE,eAAe,EAAE;MACvB,IAAI;QACF,MAAMkB,MAAI,CAAC3E,eAAe,CAAC4E,aAAa,CAACvB,OAAO,CAAC/E,GAAG,CAAC;QACrD8E,OAAO,CAACS,GAAG,CAAC,4BAA4B,CAAC;OAC1C,CAAC,OAAOtH,KAAK,EAAE;QACd6G,OAAO,CAAC7G,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;;IAClD;EACH;EAEAE,qBAAqBA,CAAC4G,OAAgB;IACpC,IAAIA,OAAO,CAACzG,aAAa,IAAIyG,OAAO,CAACzG,aAAa,GAAGyG,OAAO,CAAC7E,KAAK,EAAE;MAClE,OAAOqG,IAAI,CAACC,KAAK,CAAE,CAACzB,OAAO,CAACzG,aAAa,GAAGyG,OAAO,CAAC7E,KAAK,IAAI6E,OAAO,CAACzG,aAAa,GAAI,GAAG,CAAC;;IAE5F,OAAO,CAAC;EACV;EAEAD,WAAWA,CAAC6B,KAAa;IACvB,OAAO,IAAIuG,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE;KACxB,CAAC,CAACC,MAAM,CAAC5G,KAAK,CAAC;EAClB;EAEAlC,OAAOA,CAAA;IACL,IAAI,CAAC6F,oBAAoB,EAAE;EAC7B;EAEAkD,SAASA,CAAA;IACP,IAAI,CAACpF,MAAM,CAACqD,QAAQ,CAAC,CAAC,WAAW,CAAC,EAAE;MAClCgC,WAAW,EAAE;QAAEC,MAAM,EAAE;MAAU;KAClC,CAAC;EACJ;EAEAlH,cAAcA,CAACmH,SAAiB;IAC9B,OAAO,IAAI,CAACrF,aAAa,CAACsF,GAAG,CAACD,SAAS,CAAC;EAC1C;EAEA9F,gBAAgBA,CAACgG,KAAa,EAAErC,OAAgB;IAC9C,OAAOA,OAAO,CAAC/E,GAAG;EACpB;EAEA;EACQqH,cAAcA,CAAA;IACpB,IAAI,CAAC,IAAI,CAAClF,aAAa,IAAI,IAAI,CAACC,QAAQ,EAAE;IAE1C,IAAI,CAACiC,aAAa,EAAE;IACpB,IAAI,CAACiD,iBAAiB,GAAGC,WAAW,CAAC,MAAK;MACxC,IAAI,CAAC,IAAI,CAACnF,QAAQ,IAAI,IAAI,CAACjB,gBAAgB,CAACqG,MAAM,GAAG,IAAI,CAACvF,YAAY,EAAE;QACtE,IAAI,CAACwF,aAAa,EAAE;;IAExB,CAAC,EAAE,IAAI,CAACvF,cAAc,CAAC;EACzB;EAEQmC,aAAaA,CAAA;IACnB,IAAI,IAAI,CAACiD,iBAAiB,EAAE;MAC1BI,aAAa,CAAC,IAAI,CAACJ,iBAAiB,CAAC;MACrC,IAAI,CAACA,iBAAiB,GAAG,IAAI;;EAEjC;EAEQG,aAAaA,CAAA;IACnB,IAAI,IAAI,CAAC1G,YAAY,IAAI,IAAI,CAACC,QAAQ,EAAE;MACtC;MACA,IAAI,CAACD,YAAY,GAAG,CAAC;KACtB,MAAM;MACL,IAAI,CAACA,YAAY,EAAE;;IAErB,IAAI,CAAC4G,iBAAiB,EAAE;EAC1B;EAEAhH,cAAcA,CAAA;IACZ,IAAI,CAACyB,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACiC,aAAa,EAAE;EACtB;EAEAxD,eAAeA,CAAA;IACb,IAAI,CAACuB,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACiF,cAAc,EAAE;EACvB;EAEA;EACQrD,wBAAwBA,CAAA;IAC9B,MAAM4D,KAAK,GAAGjC,MAAM,CAACkC,UAAU;IAC/B,IAAID,KAAK,IAAI,GAAG,EAAE;MAChB,IAAI,CAAC5F,SAAS,GAAG,GAAG,CAAC,CAAC;MACtB,IAAI,CAACC,YAAY,GAAG,CAAC;KACtB,MAAM,IAAI2F,KAAK,IAAI,GAAG,EAAE;MACvB,IAAI,CAAC5F,SAAS,GAAG,GAAG,CAAC,CAAC;MACtB,IAAI,CAACC,YAAY,GAAG,CAAC;KACtB,MAAM,IAAI2F,KAAK,IAAI,IAAI,EAAE;MACxB,IAAI,CAAC5F,SAAS,GAAG,GAAG,CAAC,CAAC;MACtB,IAAI,CAACC,YAAY,GAAG,CAAC;KACtB,MAAM;MACL,IAAI,CAACD,SAAS,GAAG,GAAG,CAAC,CAAC;MACtB,IAAI,CAACC,YAAY,GAAG,CAAC;;IAEvB,IAAI,CAAC6F,kBAAkB,EAAE;IACzB,IAAI,CAACH,iBAAiB,EAAE;EAC1B;EAEQ1D,mBAAmBA,CAAA;IACzB0B,MAAM,CAACoC,gBAAgB,CAAC,QAAQ,EAAE,MAAK;MACrC,IAAI,CAAC/D,wBAAwB,EAAE;IACjC,CAAC,CAAC;EACJ;EAEA;EACA8D,kBAAkBA,CAAA;IAChB,IAAI,CAAC9G,QAAQ,GAAGuF,IAAI,CAACyB,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC7G,gBAAgB,CAACqG,MAAM,GAAG,IAAI,CAACvF,YAAY,CAAC;EAC/E;EAEA1B,SAASA,CAAA;IACP,IAAI,IAAI,CAACQ,YAAY,GAAG,CAAC,EAAE;MACzB,IAAI,CAACA,YAAY,EAAE;MACnB,IAAI,CAAC4G,iBAAiB,EAAE;MACxB,IAAI,CAACM,gCAAgC,EAAE;;EAE3C;EAEAxH,SAASA,CAAA;IACP,IAAI,IAAI,CAACM,YAAY,GAAG,IAAI,CAACC,QAAQ,EAAE;MACrC,IAAI,CAACD,YAAY,EAAE;MACnB,IAAI,CAAC4G,iBAAiB,EAAE;MACxB,IAAI,CAACM,gCAAgC,EAAE;;EAE3C;EAEQA,gCAAgCA,CAAA;IACtC,IAAI,CAAC5D,aAAa,EAAE;IACpB6D,UAAU,CAAC,MAAK;MACd,IAAI,CAACb,cAAc,EAAE;IACvB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;EACZ;EAEQM,iBAAiBA,CAAA;IACvB,IAAI,CAACzG,WAAW,GAAG,CAAC,IAAI,CAACH,YAAY,GAAG,IAAI,CAACiB,SAAS;EACxD;EAEA;EACQ0C,0BAA0BA,CAAA;IAChCwD,UAAU,CAAC,MAAK;MACd,IAAI,CAACJ,kBAAkB,EAAE;MACzB,IAAI,CAAC/G,YAAY,GAAG,CAAC;MACrB,IAAI,CAACG,WAAW,GAAG,CAAC;MACpB,IAAI,CAACmG,cAAc,EAAE;IACvB,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;EACAhL,iBAAiBA,CAAA;IACf,IAAI,CAACc,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAC1C,IAAI,IAAI,CAACA,cAAc,EAAE;MACvB,IAAI,CAACI,YAAY,EAAE;KACpB,MAAM;MACL,IAAI,CAACA,YAAY,EAAE;;EAEvB;EAEAT,qBAAqBA,CAAA;IACnB,IAAI,CAACW,mBAAmB,GAAG,CAAC,IAAI,CAACA,mBAAmB;EACtD;EAEAf,YAAYA,CAAA;IACV;IACAoI,OAAO,CAACS,GAAG,CAAC,gDAAgD,CAAC;EAC/D;EAEA3I,YAAYA,CAAA;IACV;IACA,IAAIkJ,SAAS,CAACqC,KAAK,EAAE;MACnBrC,SAAS,CAACqC,KAAK,CAAC;QACdC,KAAK,EAAE,mBAAmB;QAC1BC,IAAI,EAAE,4CAA4C;QAClD1I,GAAG,EAAEgG,MAAM,CAACC,QAAQ,CAAC0C;OACtB,CAAC;KACH,MAAM;MACL;MACAxC,SAAS,CAACC,SAAS,CAACC,SAAS,CAACL,MAAM,CAACC,QAAQ,CAAC0C,IAAI,CAAC;MACnDxD,OAAO,CAACS,GAAG,CAAC,0BAA0B,CAAC;;EAE3C;EAEAvI,eAAeA,CAAA;IACb;IACA8H,OAAO,CAACS,GAAG,CAAC,4CAA4C,CAAC;EAC3D;EAEAjI,WAAWA,CAAC8C,KAAa;IACvB,IAAIA,KAAK,IAAI,OAAO,EAAE;MACpB,OAAO,CAACA,KAAK,GAAG,OAAO,EAAEmI,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;KAC1C,MAAM,IAAInI,KAAK,IAAI,IAAI,EAAE;MACxB,OAAO,CAACA,KAAK,GAAG,IAAI,EAAEmI,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAExC,OAAOnI,KAAK,CAACoI,QAAQ,EAAE;EACzB;EAEQtE,iBAAiBA,CAAA;IACvB,IAAI,CAAC7B,QAAQ,GAAGsD,MAAM,CAACkC,UAAU,IAAI,GAAG;EAC1C;;;uBA1XWxG,yBAAyB,EAAAzF,EAAA,CAAA6M,iBAAA,CAAAC,EAAA,CAAAC,eAAA,GAAA/M,EAAA,CAAA6M,iBAAA,CAAAG,EAAA,CAAAC,yBAAA,GAAAjN,EAAA,CAAA6M,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAAnN,EAAA,CAAA6M,iBAAA,CAAAO,EAAA,CAAAC,eAAA,GAAArN,EAAA,CAAA6M,iBAAA,CAAAS,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAzB9H,yBAAyB;MAAA+H,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA1N,EAAA,CAAA2N,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCjBpCjO,EAFF,CAAAC,cAAA,aAAyC,aAEwD;UAC7FD,EAAA,CAAAW,MAAA,+CACF;UAAAX,EAAA,CAAAY,YAAA,EAAM;UAENZ,EAAA,CAAA8B,UAAA,IAAAqM,wCAAA,kBAAoD;UAiChDnO,EAFJ,CAAAC,cAAA,aAA4B,aACE,YACA;UACxBD,EAAA,CAAAU,SAAA,kBAA2D;UAC3DV,EAAA,CAAAW,MAAA,qBACF;UAAAX,EAAA,CAAAY,YAAA,EAAK;UACLZ,EAAA,CAAAC,cAAA,WAA4B;UAAAD,EAAA,CAAAW,MAAA,uCAA+B;UAC7DX,EAD6D,CAAAY,YAAA,EAAI,EAC3D;UACNZ,EAAA,CAAAC,cAAA,iBAAmD;UAAtBD,EAAA,CAAAE,UAAA,mBAAAkO,4DAAA;YAAA,OAASF,GAAA,CAAA/C,SAAA,EAAW;UAAA,EAAC;UAChDnL,EAAA,CAAAW,MAAA,kBACA;UAAAX,EAAA,CAAAU,SAAA,mBAA4C;UAEhDV,EADE,CAAAY,YAAA,EAAS,EACL;UAmINZ,EAhIA,CAAA8B,UAAA,KAAAuM,yCAAA,kBAAiD,KAAAC,yCAAA,kBAcQ,KAAAC,yCAAA,kBAU0C,KAAAC,yCAAA,kBAwGR;UAK7FxO,EAAA,CAAAY,YAAA,EAAM;;;UAnLgCZ,EAAA,CAAAqB,SAAA,GAAc;UAAdrB,EAAA,CAAAwB,UAAA,SAAA0M,GAAA,CAAAzH,QAAA,CAAc;UA8C5CzG,EAAA,CAAAqB,SAAA,IAAe;UAAfrB,EAAA,CAAAwB,UAAA,SAAA0M,GAAA,CAAAlI,SAAA,CAAe;UAcfhG,EAAA,CAAAqB,SAAA,EAAyB;UAAzBrB,EAAA,CAAAwB,UAAA,SAAA0M,GAAA,CAAA7L,KAAA,KAAA6L,GAAA,CAAAlI,SAAA,CAAyB;UAUzBhG,EAAA,CAAAqB,SAAA,EAAyD;UAAzDrB,EAAA,CAAAwB,UAAA,UAAA0M,GAAA,CAAAlI,SAAA,KAAAkI,GAAA,CAAA7L,KAAA,IAAA6L,GAAA,CAAA3I,gBAAA,CAAAqG,MAAA,KAAyD;UAwGzD5L,EAAA,CAAAqB,SAAA,EAA2D;UAA3DrB,EAAA,CAAAwB,UAAA,UAAA0M,GAAA,CAAAlI,SAAA,KAAAkI,GAAA,CAAA7L,KAAA,IAAA6L,GAAA,CAAA3I,gBAAA,CAAAqG,MAAA,OAA2D;;;qBDrKvDhM,YAAY,EAAA6O,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAE7O,WAAW,EAAA8O,EAAA,CAAAC,OAAA,EAAE9O,cAAc;MAAA+O,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}