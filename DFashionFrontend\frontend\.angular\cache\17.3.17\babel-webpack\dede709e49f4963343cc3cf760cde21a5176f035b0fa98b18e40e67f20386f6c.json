{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashionFrontend/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CommonModule } from '@angular/common';\nimport { Subscription } from 'rxjs';\nimport { IonicModule } from '@ionic/angular';\nimport { CarouselModule } from 'ngx-owl-carousel-o';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@ionic/angular\";\nconst _c0 = () => [1, 2, 3, 4];\nfunction SuggestedForYouComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function SuggestedForYouComponent_div_1_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleSectionLike());\n    });\n    i0.ɵɵelement(2, \"ion-icon\", 13);\n    i0.ɵɵelementStart(3, \"span\", 14);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function SuggestedForYouComponent_div_1_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openComments());\n    });\n    i0.ɵɵelement(6, \"ion-icon\", 16);\n    i0.ɵɵelementStart(7, \"span\", 14);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function SuggestedForYouComponent_div_1_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.shareSection());\n    });\n    i0.ɵɵelement(10, \"ion-icon\", 18);\n    i0.ɵɵelementStart(11, \"span\", 19);\n    i0.ɵɵtext(12, \"Share\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function SuggestedForYouComponent_div_1_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleSectionBookmark());\n    });\n    i0.ɵɵelement(14, \"ion-icon\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function SuggestedForYouComponent_div_1_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openMusicPlayer());\n    });\n    i0.ɵɵelement(16, \"ion-icon\", 22);\n    i0.ɵɵelementStart(17, \"span\", 19);\n    i0.ɵɵtext(18, \"Music\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"active\", ctx_r1.isSectionLiked);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", ctx_r1.isSectionLiked ? \"heart\" : \"heart-outline\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.formatCount(ctx_r1.sectionLikes));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.formatCount(ctx_r1.sectionComments));\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"active\", ctx_r1.isSectionBookmarked);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", ctx_r1.isSectionBookmarked ? \"bookmark\" : \"bookmark-outline\");\n  }\n}\nfunction SuggestedForYouComponent_div_9_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵelement(1, \"div\", 27);\n    i0.ɵɵelementStart(2, \"div\", 28);\n    i0.ɵɵelement(3, \"div\", 29)(4, \"div\", 30)(5, \"div\", 31);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SuggestedForYouComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"div\", 24);\n    i0.ɵɵtemplate(2, SuggestedForYouComponent_div_9_div_2_Template, 6, 0, \"div\", 25);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c0));\n  }\n}\nfunction SuggestedForYouComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵelement(1, \"ion-icon\", 33);\n    i0.ɵɵelementStart(2, \"p\", 34);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function SuggestedForYouComponent_div_10_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onRetry());\n    });\n    i0.ɵɵelement(5, \"ion-icon\", 36);\n    i0.ɵɵtext(6, \" Try Again \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction SuggestedForYouComponent_div_11_div_7_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 56);\n    i0.ɵɵelement(1, \"ion-icon\", 57);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SuggestedForYouComponent_div_11_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵlistener(\"click\", function SuggestedForYouComponent_div_11_div_7_Template_div_click_0_listener() {\n      const user_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onUserClick(user_r6));\n    });\n    i0.ɵɵelementStart(1, \"div\", 46);\n    i0.ɵɵelement(2, \"img\", 47);\n    i0.ɵɵtemplate(3, SuggestedForYouComponent_div_11_div_7_div_3_Template, 2, 0, \"div\", 48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 49)(5, \"h3\", 50);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 51);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\", 52);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"p\", 53);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"p\", 54);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"button\", 55);\n    i0.ɵɵlistener(\"click\", function SuggestedForYouComponent_div_11_div_7_Template_button_click_15_listener($event) {\n      const user_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onFollowUser(user_r6, $event));\n    });\n    i0.ɵɵelementStart(16, \"span\");\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(18, \"ion-icon\", 13);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const user_r6 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", user_r6.avatar, i0.ɵɵsanitizeUrl)(\"alt\", user_r6.fullName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", user_r6.isInfluencer);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(user_r6.fullName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"@\", user_r6.username, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.formatFollowerCount(user_r6.followerCount), \" followers\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(user_r6.category);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(user_r6.followedBy);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"following\", user_r6.isFollowing);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(user_r6.isFollowing ? \"Following\" : \"Follow\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", user_r6.isFollowing ? \"checkmark\" : \"add\");\n  }\n}\nfunction SuggestedForYouComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function SuggestedForYouComponent_div_11_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.slidePrev());\n    });\n    i0.ɵɵelement(2, \"ion-icon\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function SuggestedForYouComponent_div_11_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.slideNext());\n    });\n    i0.ɵɵelement(4, \"ion-icon\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 42);\n    i0.ɵɵlistener(\"mouseenter\", function SuggestedForYouComponent_div_11_Template_div_mouseenter_5_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.pauseAutoSlide());\n    })(\"mouseleave\", function SuggestedForYouComponent_div_11_Template_div_mouseleave_5_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.resumeAutoSlide());\n    });\n    i0.ɵɵelementStart(6, \"div\", 43);\n    i0.ɵɵtemplate(7, SuggestedForYouComponent_div_11_div_7_Template, 19, 12, \"div\", 44);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentSlide === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentSlide >= ctx_r1.maxSlide);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"transform\", \"translateX(\" + ctx_r1.slideOffset + \"px)\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.suggestedUsers)(\"ngForTrackBy\", ctx_r1.trackByUserId);\n  }\n}\nfunction SuggestedForYouComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58);\n    i0.ɵɵelement(1, \"ion-icon\", 59);\n    i0.ɵɵelementStart(2, \"h3\", 60);\n    i0.ɵɵtext(3, \"No Suggestions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 61);\n    i0.ɵɵtext(5, \"Check back later for user suggestions\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let SuggestedForYouComponent = /*#__PURE__*/(() => {\n  class SuggestedForYouComponent {\n    constructor(router) {\n      this.router = router;\n      this.suggestedUsers = [];\n      this.isLoading = true;\n      this.error = null;\n      this.subscription = new Subscription();\n      // Slider properties\n      this.currentSlide = 0;\n      this.slideOffset = 0;\n      this.cardWidth = 200; // Width of each user card including margin\n      this.visibleCards = 4; // Number of cards visible at once\n      this.maxSlide = 0;\n      this.autoSlideDelay = 5000; // 5 seconds for users\n      this.isAutoSliding = true;\n      this.isPaused = false;\n      // Section interaction properties\n      this.isSectionLiked = false;\n      this.isSectionBookmarked = false;\n      this.sectionLikes = 198;\n      this.sectionComments = 67;\n      this.isMobile = false;\n    }\n    ngOnInit() {\n      this.loadSuggestedUsers();\n      this.updateResponsiveSettings();\n      this.setupResizeListener();\n      this.checkMobileDevice();\n    }\n    ngOnDestroy() {\n      this.subscription.unsubscribe();\n      this.stopAutoSlide();\n    }\n    loadSuggestedUsers() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        try {\n          _this.isLoading = true;\n          _this.error = null;\n          // Mock data for suggested users\n          _this.suggestedUsers = [{\n            id: '1',\n            username: 'fashionista_maya',\n            fullName: 'Maya Patel',\n            avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=150&h=150&fit=crop&crop=face',\n            followedBy: 'Followed by john_doe and 12 others',\n            isFollowing: false,\n            isInfluencer: true,\n            followerCount: 45000,\n            category: 'Fashion'\n          }, {\n            id: '2',\n            username: 'style_guru_raj',\n            fullName: 'Raj Kumar',\n            avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',\n            followedBy: 'Followed by sarah_k and 8 others',\n            isFollowing: false,\n            isInfluencer: true,\n            followerCount: 32000,\n            category: 'Menswear'\n          }, {\n            id: '3',\n            username: 'trendy_sara',\n            fullName: 'Sara Johnson',\n            avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',\n            followedBy: 'Followed by alex_m and 15 others',\n            isFollowing: false,\n            isInfluencer: false,\n            followerCount: 8500,\n            category: 'Casual'\n          }, {\n            id: '4',\n            username: 'luxury_lover',\n            fullName: 'Emma Wilson',\n            avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face',\n            followedBy: 'Followed by mike_t and 20 others',\n            isFollowing: false,\n            isInfluencer: true,\n            followerCount: 67000,\n            category: 'Luxury'\n          }, {\n            id: '5',\n            username: 'street_style_alex',\n            fullName: 'Alex Chen',\n            avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',\n            followedBy: 'Followed by lisa_p and 5 others',\n            isFollowing: false,\n            isInfluencer: false,\n            followerCount: 12000,\n            category: 'Streetwear'\n          }, {\n            id: '6',\n            username: 'boho_bella',\n            fullName: 'Isabella Rodriguez',\n            avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face',\n            followedBy: 'Followed by tom_h and 18 others',\n            isFollowing: false,\n            isInfluencer: true,\n            followerCount: 28000,\n            category: 'Boho'\n          }];\n          _this.isLoading = false;\n          _this.updateSliderOnUsersLoad();\n        } catch (error) {\n          console.error('Error loading suggested users:', error);\n          _this.error = 'Failed to load suggested users';\n          _this.isLoading = false;\n        }\n      })();\n    }\n    onUserClick(user) {\n      this.router.navigate(['/profile', user.username]);\n    }\n    onFollowUser(user, event) {\n      event.stopPropagation();\n      user.isFollowing = !user.isFollowing;\n      if (user.isFollowing) {\n        user.followerCount++;\n      } else {\n        user.followerCount--;\n      }\n    }\n    formatFollowerCount(count) {\n      if (count >= 1000000) {\n        return (count / 1000000).toFixed(1) + 'M';\n      } else if (count >= 1000) {\n        return (count / 1000).toFixed(1) + 'K';\n      }\n      return count.toString();\n    }\n    onRetry() {\n      this.loadSuggestedUsers();\n    }\n    trackByUserId(index, user) {\n      return user.id;\n    }\n    // Auto-sliding methods\n    startAutoSlide() {\n      if (!this.isAutoSliding || this.isPaused) return;\n      this.stopAutoSlide();\n      this.autoSlideInterval = setInterval(() => {\n        if (!this.isPaused && this.suggestedUsers.length > this.visibleCards) {\n          this.autoSlideNext();\n        }\n      }, this.autoSlideDelay);\n    }\n    stopAutoSlide() {\n      if (this.autoSlideInterval) {\n        clearInterval(this.autoSlideInterval);\n        this.autoSlideInterval = null;\n      }\n    }\n    autoSlideNext() {\n      if (this.currentSlide >= this.maxSlide) {\n        this.currentSlide = 0;\n      } else {\n        this.currentSlide++;\n      }\n      this.updateSlideOffset();\n    }\n    pauseAutoSlide() {\n      this.isPaused = true;\n      this.stopAutoSlide();\n    }\n    resumeAutoSlide() {\n      this.isPaused = false;\n      this.startAutoSlide();\n    }\n    // Responsive methods\n    updateResponsiveSettings() {\n      const width = window.innerWidth;\n      if (width <= 480) {\n        this.cardWidth = 180;\n        this.visibleCards = 1;\n      } else if (width <= 768) {\n        this.cardWidth = 200;\n        this.visibleCards = 2;\n      } else if (width <= 1200) {\n        this.cardWidth = 220;\n        this.visibleCards = 3;\n      } else {\n        this.cardWidth = 220;\n        this.visibleCards = 4;\n      }\n      this.updateSliderLimits();\n      this.updateSlideOffset();\n    }\n    setupResizeListener() {\n      window.addEventListener('resize', () => {\n        this.updateResponsiveSettings();\n      });\n    }\n    // Slider methods\n    updateSliderLimits() {\n      this.maxSlide = Math.max(0, this.suggestedUsers.length - this.visibleCards);\n    }\n    slidePrev() {\n      if (this.currentSlide > 0) {\n        this.currentSlide--;\n        this.updateSlideOffset();\n        this.restartAutoSlideAfterInteraction();\n      }\n    }\n    slideNext() {\n      if (this.currentSlide < this.maxSlide) {\n        this.currentSlide++;\n        this.updateSlideOffset();\n        this.restartAutoSlideAfterInteraction();\n      }\n    }\n    updateSlideOffset() {\n      this.slideOffset = -this.currentSlide * this.cardWidth;\n    }\n    restartAutoSlideAfterInteraction() {\n      this.stopAutoSlide();\n      setTimeout(() => {\n        this.startAutoSlide();\n      }, 2000);\n    }\n    // Update slider when users load\n    updateSliderOnUsersLoad() {\n      setTimeout(() => {\n        this.updateSliderLimits();\n        this.currentSlide = 0;\n        this.slideOffset = 0;\n        this.startAutoSlide();\n      }, 100);\n    }\n    // Section interaction methods\n    toggleSectionLike() {\n      this.isSectionLiked = !this.isSectionLiked;\n      if (this.isSectionLiked) {\n        this.sectionLikes++;\n      } else {\n        this.sectionLikes--;\n      }\n    }\n    toggleSectionBookmark() {\n      this.isSectionBookmarked = !this.isSectionBookmarked;\n    }\n    openComments() {\n      console.log('Opening comments for suggested users section');\n    }\n    shareSection() {\n      if (navigator.share) {\n        navigator.share({\n          title: 'Suggested for You',\n          text: 'Discover amazing fashion creators!',\n          url: window.location.href\n        });\n      } else {\n        navigator.clipboard.writeText(window.location.href);\n        console.log('Link copied to clipboard');\n      }\n    }\n    openMusicPlayer() {\n      console.log('Opening music player for suggested users');\n    }\n    formatCount(count) {\n      if (count >= 1000000) {\n        return (count / 1000000).toFixed(1) + 'M';\n      } else if (count >= 1000) {\n        return (count / 1000).toFixed(1) + 'K';\n      }\n      return count.toString();\n    }\n    checkMobileDevice() {\n      this.isMobile = window.innerWidth <= 768;\n    }\n    static {\n      this.ɵfac = function SuggestedForYouComponent_Factory(t) {\n        return new (t || SuggestedForYouComponent)(i0.ɵɵdirectiveInject(i1.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: SuggestedForYouComponent,\n        selectors: [[\"app-suggested-for-you\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 13,\n        vars: 5,\n        consts: [[1, \"suggested-users-container\"], [\"class\", \"mobile-action-buttons\", 4, \"ngIf\"], [1, \"section-header\"], [1, \"header-content\"], [1, \"section-title\"], [\"name\", \"people\", 1, \"title-icon\"], [1, \"section-subtitle\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"users-slider-container\", 4, \"ngIf\"], [\"class\", \"empty-container\", 4, \"ngIf\"], [1, \"mobile-action-buttons\"], [1, \"action-btn\", \"like-btn\", 3, \"click\"], [3, \"name\"], [1, \"action-count\"], [1, \"action-btn\", \"comment-btn\", 3, \"click\"], [\"name\", \"chatbubble-outline\"], [1, \"action-btn\", \"share-btn\", 3, \"click\"], [\"name\", \"arrow-redo-outline\"], [1, \"action-text\"], [1, \"action-btn\", \"bookmark-btn\", 3, \"click\"], [1, \"action-btn\", \"music-btn\", 3, \"click\"], [\"name\", \"musical-notes\"], [1, \"loading-container\"], [1, \"loading-grid\"], [\"class\", \"loading-user-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"loading-user-card\"], [1, \"loading-avatar\"], [1, \"loading-content\"], [1, \"loading-line\", \"short\"], [1, \"loading-line\", \"medium\"], [1, \"loading-line\", \"long\"], [1, \"error-container\"], [\"name\", \"alert-circle\", 1, \"error-icon\"], [1, \"error-message\"], [1, \"retry-btn\", 3, \"click\"], [\"name\", \"refresh\"], [1, \"users-slider-container\"], [1, \"slider-nav\", \"prev-btn\", 3, \"click\", \"disabled\"], [\"name\", \"chevron-back\"], [1, \"slider-nav\", \"next-btn\", 3, \"click\", \"disabled\"], [\"name\", \"chevron-forward\"], [1, \"users-slider-wrapper\", 3, \"mouseenter\", \"mouseleave\"], [1, \"users-slider\"], [\"class\", \"user-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"user-card\", 3, \"click\"], [1, \"user-avatar-container\"], [\"loading\", \"lazy\", 1, \"user-avatar\", 3, \"src\", \"alt\"], [\"class\", \"influencer-badge\", 4, \"ngIf\"], [1, \"user-info\"], [1, \"user-name\"], [1, \"username\"], [1, \"follower-count\"], [1, \"category-tag\"], [1, \"followed-by\"], [1, \"follow-btn\", 3, \"click\"], [1, \"influencer-badge\"], [\"name\", \"star\"], [1, \"empty-container\"], [\"name\", \"people-outline\", 1, \"empty-icon\"], [1, \"empty-title\"], [1, \"empty-message\"]],\n        template: function SuggestedForYouComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0);\n            i0.ɵɵtemplate(1, SuggestedForYouComponent_div_1_Template, 19, 8, \"div\", 1);\n            i0.ɵɵelementStart(2, \"div\", 2)(3, \"div\", 3)(4, \"h2\", 4);\n            i0.ɵɵelement(5, \"ion-icon\", 5);\n            i0.ɵɵtext(6, \" Suggested for you \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(7, \"p\", 6);\n            i0.ɵɵtext(8, \"Discover amazing fashion creators\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(9, SuggestedForYouComponent_div_9_Template, 3, 2, \"div\", 7)(10, SuggestedForYouComponent_div_10_Template, 7, 1, \"div\", 8)(11, SuggestedForYouComponent_div_11_Template, 8, 6, \"div\", 9)(12, SuggestedForYouComponent_div_12_Template, 6, 0, \"div\", 10);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isMobile);\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.error && !ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.suggestedUsers.length > 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.suggestedUsers.length === 0);\n          }\n        },\n        dependencies: [CommonModule, i2.NgForOf, i2.NgIf, IonicModule, i3.IonIcon, CarouselModule],\n        styles: [\".suggested-users-container[_ngcontent-%COMP%]{padding:20px;background:linear-gradient(135deg,#f8f9fa,#e9ecef);border-radius:16px;margin-bottom:24px;position:relative;max-width:675px}.mobile-action-buttons[_ngcontent-%COMP%]{position:absolute;right:15px;top:50%;transform:translateY(-50%);display:flex;flex-direction:column;gap:20px;z-index:10}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]{width:48px;height:48px;border-radius:50%;border:none;background:#6c5ce733;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);color:#6c5ce7;display:flex;flex-direction:column;align-items:center;justify-content:center;cursor:pointer;transition:all .3s ease;position:relative}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:24px;margin-bottom:2px}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   .action-count[_ngcontent-%COMP%], .mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   .action-text[_ngcontent-%COMP%]{font-size:10px;font-weight:600;line-height:1}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:hover{transform:scale(1.1);background:#6c5ce74d}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active[_ngcontent-%COMP%]{background:#6c5ce7e6;color:#fff}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active.like-btn[_ngcontent-%COMP%]{background:#ff3040e6;color:#fff}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active.like-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_heartBeat .6s ease-in-out}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active.bookmark-btn[_ngcontent-%COMP%]{background:#ffd700e6;color:#fff}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.like-btn.active[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:#fff}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.music-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,#ff3040,#6c5ce7);color:#fff}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.music-btn[_ngcontent-%COMP%]:hover{transform:scale(1.1) rotate(15deg)}@keyframes _ngcontent-%COMP%_heartBeat{0%{transform:scale(1)}25%{transform:scale(1.3)}50%{transform:scale(1.1)}75%{transform:scale(1.25)}to{transform:scale(1)}}@media (min-width: 769px){.mobile-action-buttons[_ngcontent-%COMP%]{display:none}}.section-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:24px}.section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]{flex:1}.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{font-size:24px;font-weight:700;color:#1a1a1a;margin:0 0 8px;display:flex;align-items:center;gap:12px}.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   .title-icon[_ngcontent-%COMP%]{font-size:28px;color:#6c5ce7}.section-header[_ngcontent-%COMP%]   .section-subtitle[_ngcontent-%COMP%]{font-size:14px;color:#666;margin:0}.loading-container[_ngcontent-%COMP%]   .loading-grid[_ngcontent-%COMP%]{display:flex;gap:20px;overflow-x:auto;padding-bottom:8px}.loading-container[_ngcontent-%COMP%]   .loading-user-card[_ngcontent-%COMP%]{flex:0 0 180px;background:#ffffffb3;border-radius:16px;padding:20px}.loading-container[_ngcontent-%COMP%]   .loading-user-card[_ngcontent-%COMP%]   .loading-avatar[_ngcontent-%COMP%]{width:80px;height:80px;border-radius:50%;background:linear-gradient(90deg,#f0f0f0 25%,#e0e0e0,#f0f0f0 75%);background-size:200% 100%;animation:_ngcontent-%COMP%_loading 1.5s infinite;margin:0 auto 16px}.loading-container[_ngcontent-%COMP%]   .loading-user-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line[_ngcontent-%COMP%]{height:12px;border-radius:6px;background:linear-gradient(90deg,#f0f0f0 25%,#e0e0e0,#f0f0f0 75%);background-size:200% 100%;animation:_ngcontent-%COMP%_loading 1.5s infinite;margin-bottom:8px}.loading-container[_ngcontent-%COMP%]   .loading-user-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.short[_ngcontent-%COMP%]{width:60%}.loading-container[_ngcontent-%COMP%]   .loading-user-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.medium[_ngcontent-%COMP%]{width:80%}.loading-container[_ngcontent-%COMP%]   .loading-user-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.long[_ngcontent-%COMP%]{width:100%}@keyframes _ngcontent-%COMP%_loading{0%{background-position:200% 0}to{background-position:-200% 0}}.error-container[_ngcontent-%COMP%]{text-align:center;padding:40px 20px}.error-container[_ngcontent-%COMP%]   .error-icon[_ngcontent-%COMP%]{font-size:48px;color:#e74c3c;margin-bottom:16px}.error-container[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%]{color:#666;margin-bottom:20px;font-size:16px}.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,#e74c3c,#c0392b);color:#fff;border:none;padding:12px 24px;border-radius:25px;font-weight:600;display:flex;align-items:center;gap:8px;margin:0 auto;cursor:pointer;transition:all .3s ease}.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 8px 25px #e74c3c4d}.users-slider-container[_ngcontent-%COMP%]{position:relative;margin:0 -20px}.users-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]{position:absolute;top:50%;transform:translateY(-50%);z-index:10;background:#000000b3;color:#fff;border:none;width:40px;height:40px;border-radius:50%;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all .3s ease}.users-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]:hover:not(:disabled){background:#000000e6;transform:translateY(-50%) scale(1.1)}.users-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]:disabled{opacity:.3;cursor:not-allowed}.users-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px}.users-slider-container[_ngcontent-%COMP%]   .slider-nav.prev-btn[_ngcontent-%COMP%]{left:-20px}.users-slider-container[_ngcontent-%COMP%]   .slider-nav.next-btn[_ngcontent-%COMP%]{right:-20px}.users-slider-wrapper[_ngcontent-%COMP%]{overflow:hidden;padding:0 20px}.users-slider[_ngcontent-%COMP%]{display:flex;transition:transform .4s cubic-bezier(.25,.46,.45,.94);gap:20px}.users-slider[_ngcontent-%COMP%]   .user-card[_ngcontent-%COMP%]{flex:0 0 180px;width:180px}.user-card[_ngcontent-%COMP%]{background:#fff;border-radius:16px;padding:20px;text-align:center;box-shadow:0 4px 20px #00000014;transition:all .3s ease;cursor:pointer}.user-card[_ngcontent-%COMP%]:hover{transform:translateY(-8px);box-shadow:0 12px 40px #00000026}.user-avatar-container[_ngcontent-%COMP%]{position:relative;margin-bottom:16px}.user-avatar-container[_ngcontent-%COMP%]   .user-avatar[_ngcontent-%COMP%]{width:80px;height:80px;border-radius:50%;object-fit:cover;border:3px solid #6c5ce7;margin:0 auto;display:block}.user-avatar-container[_ngcontent-%COMP%]   .influencer-badge[_ngcontent-%COMP%]{position:absolute;top:-5px;right:calc(50% - 45px);background:linear-gradient(135deg,gold,#ffb347);color:#fff;width:24px;height:24px;border-radius:50%;display:flex;align-items:center;justify-content:center;font-size:12px;border:2px solid white}.user-avatar-container[_ngcontent-%COMP%]   .influencer-badge[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:12px}.user-info[_ngcontent-%COMP%]{margin-bottom:16px}.user-info[_ngcontent-%COMP%]   .user-name[_ngcontent-%COMP%]{font-size:16px;font-weight:600;color:#1a1a1a;margin:0 0 4px}.user-info[_ngcontent-%COMP%]   .username[_ngcontent-%COMP%]{font-size:14px;color:#6c5ce7;margin:0 0 8px;font-weight:500}.user-info[_ngcontent-%COMP%]   .follower-count[_ngcontent-%COMP%]{font-size:12px;color:#666;margin:0 0 4px;font-weight:600}.user-info[_ngcontent-%COMP%]   .category-tag[_ngcontent-%COMP%]{font-size:11px;color:#6c5ce7;background:#6c5ce71a;padding:2px 8px;border-radius:12px;display:inline-block;margin:0 0 8px}.user-info[_ngcontent-%COMP%]   .followed-by[_ngcontent-%COMP%]{font-size:11px;color:#999;margin:0;line-height:1.3}.follow-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,#6c5ce7,#5a4fcf);color:#fff;border:none;padding:8px 16px;border-radius:20px;font-size:12px;font-weight:600;display:flex;align-items:center;gap:4px;margin:0 auto;cursor:pointer;transition:all .3s ease}.follow-btn[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 6px 20px #6c5ce74d}.follow-btn.following[_ngcontent-%COMP%]{background:linear-gradient(135deg,#00b894,#00a085)}.follow-btn.following[_ngcontent-%COMP%]:hover{box-shadow:0 6px 20px #00b8944d}.follow-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:14px}.empty-container[_ngcontent-%COMP%]{text-align:center;padding:60px 20px}.empty-container[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%]{font-size:64px;color:#ddd;margin-bottom:20px}.empty-container[_ngcontent-%COMP%]   .empty-title[_ngcontent-%COMP%]{font-size:20px;font-weight:600;color:#666;margin:0 0 8px}.empty-container[_ngcontent-%COMP%]   .empty-message[_ngcontent-%COMP%]{color:#999;margin:0}@media (max-width: 1200px){.users-slider[_ngcontent-%COMP%]   .user-card[_ngcontent-%COMP%]{flex:0 0 170px;width:170px}}@media (max-width: 768px){.users-slider-container[_ngcontent-%COMP%]{margin:0 -10px}.users-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]{width:35px;height:35px}.users-slider-container[_ngcontent-%COMP%]   .slider-nav.prev-btn[_ngcontent-%COMP%]{left:-15px}.users-slider-container[_ngcontent-%COMP%]   .slider-nav.next-btn[_ngcontent-%COMP%]{right:-15px}.users-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:16px}.users-slider-wrapper[_ngcontent-%COMP%]{padding:0 10px}.users-slider[_ngcontent-%COMP%]{gap:15px}.users-slider[_ngcontent-%COMP%]   .user-card[_ngcontent-%COMP%]{flex:0 0 160px;width:160px;padding:16px}.user-avatar-container[_ngcontent-%COMP%]   .user-avatar[_ngcontent-%COMP%]{width:70px;height:70px}}@media (max-width: 480px){.users-slider[_ngcontent-%COMP%]   .user-card[_ngcontent-%COMP%]{flex:0 0 150px;width:150px}}\"]\n      });\n    }\n  }\n  return SuggestedForYouComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}