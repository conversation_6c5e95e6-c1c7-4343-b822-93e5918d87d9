.suggested-users-container {
  padding: 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 16px;
  margin-bottom: 24px;
  position: relative;
    max-width: 675px;
}

// Mobile Action Buttons (TikTok/Instagram Style)
.mobile-action-buttons {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  gap: 20px;
  z-index: 10;

  .action-btn {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    border: none;
    background: rgba(108, 92, 231, 0.2);
    backdrop-filter: blur(10px);
    color: #6c5ce7;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;

    ion-icon {
      font-size: 24px;
      margin-bottom: 2px;
    }

    .action-count, .action-text {
      font-size: 10px;
      font-weight: 600;
      line-height: 1;
    }

    &:hover {
      transform: scale(1.1);
      background: rgba(108, 92, 231, 0.3);
    }

    &.active {
      background: rgba(108, 92, 231, 0.9);
      color: white;

      &.like-btn {
        background: rgba(255, 48, 64, 0.9);
        color: white;

        ion-icon {
          animation: heartBeat 0.6s ease-in-out;
        }
      }

      &.bookmark-btn {
        background: rgba(255, 215, 0, 0.9);
        color: white;
      }
    }

    &.like-btn.active ion-icon {
      color: white;
    }

    &.music-btn {
      background: linear-gradient(135deg, #ff3040 0%, #6c5ce7 100%);
      color: white;

      &:hover {
        transform: scale(1.1) rotate(15deg);
      }
    }
  }
}

@keyframes heartBeat {
  0% { transform: scale(1); }
  25% { transform: scale(1.3); }
  50% { transform: scale(1.1); }
  75% { transform: scale(1.25); }
  100% { transform: scale(1); }
}

// Hide action buttons on desktop
@media (min-width: 769px) {
  .mobile-action-buttons {
    display: none;
  }
}

// Header Section
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  
  .header-content {
    flex: 1;
  }
  
  .section-title {
    font-size: 24px;
    font-weight: 700;
    color: #1a1a1a;
    margin: 0 0 8px 0;
    display: flex;
    align-items: center;
    gap: 12px;
    
    .title-icon {
      font-size: 28px;
      color: #6c5ce7;
    }
  }
  
  .section-subtitle {
    font-size: 14px;
    color: #666;
    margin: 0;
  }
}

// Loading States
.loading-container {
  .loading-grid {
    display: flex;
    gap: 20px;
    overflow-x: auto;
    padding-bottom: 8px;
  }
  
  .loading-user-card {
    flex: 0 0 180px;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 16px;
    padding: 20px;
    
    .loading-avatar {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200% 100%;
      animation: loading 1.5s infinite;
      margin: 0 auto 16px;
    }
    
    .loading-content {
      .loading-line {
        height: 12px;
        border-radius: 6px;
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
        margin-bottom: 8px;
        
        &.short { width: 60%; }
        &.medium { width: 80%; }
        &.long { width: 100%; }
      }
    }
  }
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

// Error State
.error-container {
  text-align: center;
  padding: 40px 20px;
  
  .error-icon {
    font-size: 48px;
    color: #e74c3c;
    margin-bottom: 16px;
  }
  
  .error-message {
    color: #666;
    margin-bottom: 20px;
    font-size: 16px;
  }
  
  .retry-btn {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0 auto;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(231, 76, 60, 0.3);
    }
  }
}

// Users Slider Styles
.users-slider-container {
  position: relative;
  margin: 0 -20px;
  
  .slider-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 10;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover:not(:disabled) {
      background: rgba(0, 0, 0, 0.9);
      transform: translateY(-50%) scale(1.1);
    }
    
    &:disabled {
      opacity: 0.3;
      cursor: not-allowed;
    }
    
    ion-icon {
      font-size: 18px;
    }
    
    &.prev-btn {
      left: -20px;
    }
    
    &.next-btn {
      right: -20px;
    }
  }
}

.users-slider-wrapper {
  overflow: hidden;
  padding: 0 20px;
}

.users-slider {
  display: flex;
  transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  gap: 20px;
  
  .user-card {
    flex: 0 0 180px;
    width: 180px;
  }
}

// User Card Styles
.user-card {
  background: white;
  border-radius: 16px;
  padding: 20px;
  text-align: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  cursor: pointer;
  
  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  }
}

.user-avatar-container {
  position: relative;
  margin-bottom: 16px;
  
  .user-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid #6c5ce7;
    margin: 0 auto;
    display: block;
  }
  
  .influencer-badge {
    position: absolute;
    top: -5px;
    right: calc(50% - 45px);
    background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%);
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    border: 2px solid white;
    
    ion-icon {
      font-size: 12px;
    }
  }
}

.user-info {
  margin-bottom: 16px;
  
  .user-name {
    font-size: 16px;
    font-weight: 600;
    color: #1a1a1a;
    margin: 0 0 4px 0;
  }
  
  .username {
    font-size: 14px;
    color: #6c5ce7;
    margin: 0 0 8px 0;
    font-weight: 500;
  }
  
  .follower-count {
    font-size: 12px;
    color: #666;
    margin: 0 0 4px 0;
    font-weight: 600;
  }
  
  .category-tag {
    font-size: 11px;
    color: #6c5ce7;
    background: rgba(108, 92, 231, 0.1);
    padding: 2px 8px;
    border-radius: 12px;
    display: inline-block;
    margin: 0 0 8px 0;
  }
  
  .followed-by {
    font-size: 11px;
    color: #999;
    margin: 0;
    line-height: 1.3;
  }
}

.follow-btn {
  background: linear-gradient(135deg, #6c5ce7 0%, #5a4fcf 100%);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 4px;
  margin: 0 auto;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(108, 92, 231, 0.3);
  }
  
  &.following {
    background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
    
    &:hover {
      box-shadow: 0 6px 20px rgba(0, 184, 148, 0.3);
    }
  }
  
  ion-icon {
    font-size: 14px;
  }
}

// Empty State
.empty-container {
  text-align: center;
  padding: 60px 20px;
  
  .empty-icon {
    font-size: 64px;
    color: #ddd;
    margin-bottom: 20px;
  }
  
  .empty-title {
    font-size: 20px;
    font-weight: 600;
    color: #666;
    margin: 0 0 8px 0;
  }
  
  .empty-message {
    color: #999;
    margin: 0;
  }
}

// Responsive Design
@media (max-width: 1200px) {
  .users-slider {
    .user-card {
      flex: 0 0 170px;
      width: 170px;
    }
  }
}

@media (max-width: 768px) {
  .users-slider-container {
    margin: 0 -10px;
    
    .slider-nav {
      width: 35px;
      height: 35px;
      
      &.prev-btn {
        left: -15px;
      }
      
      &.next-btn {
        right: -15px;
      }
      
      ion-icon {
        font-size: 16px;
      }
    }
  }
  
  .users-slider-wrapper {
    padding: 0 10px;
  }
  
  .users-slider {
    gap: 15px;
    
    .user-card {
      flex: 0 0 160px;
      width: 160px;
      padding: 16px;
    }
  }
  
  .user-avatar-container .user-avatar {
    width: 70px;
    height: 70px;
  }
}

@media (max-width: 480px) {
  .users-slider {
    .user-card {
      flex: 0 0 150px;
      width: 150px;
    }
  }
}
