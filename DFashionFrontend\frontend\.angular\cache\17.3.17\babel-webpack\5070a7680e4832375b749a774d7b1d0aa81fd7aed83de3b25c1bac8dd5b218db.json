{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashionFrontend/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CommonModule } from '@angular/common';\nimport { Subscription } from 'rxjs';\nimport { IonicModule } from '@ionic/angular';\nimport { CarouselModule } from 'ngx-owl-carousel-o';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/trending.service\";\nimport * as i2 from \"../../../../core/services/social-interactions.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@ionic/angular\";\nconst _c0 = () => [1, 2, 3, 4];\nconst _c1 = () => [1, 2, 3];\nconst _c2 = () => [1, 2, 3, 4, 5];\nfunction FeaturedBrandsComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_1_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleSectionLike());\n    });\n    i0.ɵɵelement(2, \"ion-icon\", 13);\n    i0.ɵɵelementStart(3, \"span\", 14);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_1_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openComments());\n    });\n    i0.ɵɵelement(6, \"ion-icon\", 16);\n    i0.ɵɵelementStart(7, \"span\", 14);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_1_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.shareSection());\n    });\n    i0.ɵɵelement(10, \"ion-icon\", 18);\n    i0.ɵɵelementStart(11, \"span\", 19);\n    i0.ɵɵtext(12, \"Share\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_1_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleSectionBookmark());\n    });\n    i0.ɵɵelement(14, \"ion-icon\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_1_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openMusicPlayer());\n    });\n    i0.ɵɵelement(16, \"ion-icon\", 22);\n    i0.ɵɵelementStart(17, \"span\", 19);\n    i0.ɵɵtext(18, \"Music\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"active\", ctx_r1.isSectionLiked);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", ctx_r1.isSectionLiked ? \"heart\" : \"heart-outline\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.formatCount(ctx_r1.sectionLikes));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.formatCount(ctx_r1.sectionComments));\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"active\", ctx_r1.isSectionBookmarked);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", ctx_r1.isSectionBookmarked ? \"bookmark\" : \"bookmark-outline\");\n  }\n}\nfunction FeaturedBrandsComponent_div_9_div_2_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 32);\n  }\n}\nfunction FeaturedBrandsComponent_div_9_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"div\", 27);\n    i0.ɵɵelement(2, \"div\", 28)(3, \"div\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 30);\n    i0.ɵɵtemplate(5, FeaturedBrandsComponent_div_9_div_2_div_5_Template, 1, 0, \"div\", 31);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c1));\n  }\n}\nfunction FeaturedBrandsComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"div\", 24);\n    i0.ɵɵtemplate(2, FeaturedBrandsComponent_div_9_div_2_Template, 6, 2, \"div\", 25);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c0));\n  }\n}\nfunction FeaturedBrandsComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵelement(1, \"ion-icon\", 34);\n    i0.ɵɵelementStart(2, \"p\", 35);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_10_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onRetry());\n    });\n    i0.ɵɵelement(5, \"ion-icon\", 37);\n    i0.ɵɵtext(6, \" Try Again \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction FeaturedBrandsComponent_div_11_div_7_div_25_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 77);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r8 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.formatPrice(product_r8.originalPrice));\n  }\n}\nfunction FeaturedBrandsComponent_div_11_div_7_div_25_ion_icon_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ion-icon\", 13);\n  }\n  if (rf & 2) {\n    const star_r9 = ctx.$implicit;\n    const product_r8 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵclassProp(\"filled\", star_r9 <= product_r8.rating.average);\n    i0.ɵɵproperty(\"name\", star_r9 <= product_r8.rating.average ? \"star\" : \"star-outline\");\n  }\n}\nfunction FeaturedBrandsComponent_div_11_div_7_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_11_div_7_div_25_Template_div_click_0_listener($event) {\n      const product_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onProductClick(product_r8, $event));\n    });\n    i0.ɵɵelementStart(1, \"div\", 64);\n    i0.ɵɵelement(2, \"img\", 65);\n    i0.ɵɵelementStart(3, \"div\", 66)(4, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_11_div_7_div_25_Template_button_click_4_listener($event) {\n      const product_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onLikeProduct(product_r8, $event));\n    });\n    i0.ɵɵelement(5, \"ion-icon\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_11_div_7_div_25_Template_button_click_6_listener($event) {\n      const product_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onShareProduct(product_r8, $event));\n    });\n    i0.ɵɵelement(7, \"ion-icon\", 67);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(8, \"div\", 68)(9, \"h5\", 69);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 70)(12, \"span\", 71);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, FeaturedBrandsComponent_div_11_div_7_div_25_span_14_Template, 2, 1, \"span\", 72);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 73)(16, \"div\", 74);\n    i0.ɵɵtemplate(17, FeaturedBrandsComponent_div_11_div_7_div_25_ion_icon_17_Template, 1, 3, \"ion-icon\", 75);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"span\", 76);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const product_r8 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", product_r8.images[0].url, i0.ɵɵsanitizeUrl)(\"alt\", product_r8.images[0].alt || product_r8.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"liked\", ctx_r1.isProductLiked(product_r8._id));\n    i0.ɵɵattribute(\"aria-label\", \"Like \" + product_r8.name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", ctx_r1.isProductLiked(product_r8._id) ? \"heart\" : \"heart-outline\");\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"aria-label\", \"Share \" + product_r8.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(product_r8.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.formatPrice(product_r8.price));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r8.originalPrice && product_r8.originalPrice > product_r8.price);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(12, _c2));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", product_r8.rating.count, \")\");\n  }\n}\nfunction FeaturedBrandsComponent_div_11_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 46);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_11_div_7_Template_div_click_0_listener() {\n      const brand_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onBrandClick(brand_r6));\n    });\n    i0.ɵɵelementStart(1, \"div\", 47)(2, \"div\", 48)(3, \"h3\", 49);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 50)(6, \"div\", 51);\n    i0.ɵɵelement(7, \"ion-icon\", 52);\n    i0.ɵɵelementStart(8, \"span\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 51);\n    i0.ɵɵelement(11, \"ion-icon\", 53);\n    i0.ɵɵelementStart(12, \"span\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 51);\n    i0.ɵɵelement(15, \"ion-icon\", 54);\n    i0.ɵɵelementStart(16, \"span\");\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(18, \"div\", 55);\n    i0.ɵɵelement(19, \"ion-icon\", 56);\n    i0.ɵɵtext(20, \" Featured \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 57)(22, \"h4\", 58);\n    i0.ɵɵtext(23, \"Top Products\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 59);\n    i0.ɵɵtemplate(25, FeaturedBrandsComponent_div_11_div_7_div_25_Template, 20, 13, \"div\", 60);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\", 61)(27, \"button\", 62)(28, \"span\");\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(30, \"ion-icon\", 42);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const brand_r6 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(brand_r6.brand);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", brand_r6.productCount, \" Products\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", brand_r6.avgRating, \"/5\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.formatNumber(brand_r6.totalViews), \" Views\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", brand_r6.topProducts)(\"ngForTrackBy\", ctx_r1.trackByProductId);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"View All \", brand_r6.brand, \" Products\");\n  }\n}\nfunction FeaturedBrandsComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_11_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.slidePrev());\n    });\n    i0.ɵɵelement(2, \"ion-icon\", 40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_11_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.slideNext());\n    });\n    i0.ɵɵelement(4, \"ion-icon\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 43);\n    i0.ɵɵlistener(\"mouseenter\", function FeaturedBrandsComponent_div_11_Template_div_mouseenter_5_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.pauseAutoSlide());\n    })(\"mouseleave\", function FeaturedBrandsComponent_div_11_Template_div_mouseleave_5_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.resumeAutoSlide());\n    });\n    i0.ɵɵelementStart(6, \"div\", 44);\n    i0.ɵɵtemplate(7, FeaturedBrandsComponent_div_11_div_7_Template, 31, 7, \"div\", 45);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentSlide === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentSlide >= ctx_r1.maxSlide);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"transform\", \"translateX(\" + ctx_r1.slideOffset + \"px)\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.featuredBrands)(\"ngForTrackBy\", ctx_r1.trackByBrandName);\n  }\n}\nfunction FeaturedBrandsComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 78);\n    i0.ɵɵelement(1, \"ion-icon\", 79);\n    i0.ɵɵelementStart(2, \"h3\", 80);\n    i0.ɵɵtext(3, \"No Featured Brands\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 81);\n    i0.ɵɵtext(5, \"Check back later for featured brand collections\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let FeaturedBrandsComponent = /*#__PURE__*/(() => {\n  class FeaturedBrandsComponent {\n    constructor(trendingService, socialService, router) {\n      this.trendingService = trendingService;\n      this.socialService = socialService;\n      this.router = router;\n      this.featuredBrands = [];\n      this.isLoading = true;\n      this.error = null;\n      this.likedProducts = new Set();\n      this.subscription = new Subscription();\n      // Slider properties\n      this.currentSlide = 0;\n      this.slideOffset = 0;\n      this.cardWidth = 320; // Width of each brand card including margin\n      this.visibleCards = 3; // Number of cards visible at once\n      this.maxSlide = 0;\n      this.autoSlideDelay = 4000; // 4 seconds for brands\n      this.isAutoSliding = true;\n      this.isPaused = false;\n      // Section interaction properties\n      this.isSectionLiked = false;\n      this.isSectionBookmarked = false;\n      this.sectionLikes = 287;\n      this.sectionComments = 89;\n      this.isMobile = false;\n    }\n    ngOnInit() {\n      this.loadFeaturedBrands();\n      this.subscribeFeaturedBrands();\n      this.subscribeLikedProducts();\n      this.updateResponsiveSettings();\n      this.setupResizeListener();\n      this.checkMobileDevice();\n    }\n    ngOnDestroy() {\n      this.subscription.unsubscribe();\n      this.stopAutoSlide();\n    }\n    subscribeFeaturedBrands() {\n      this.subscription.add(this.trendingService.featuredBrands$.subscribe(brands => {\n        this.featuredBrands = brands;\n        this.isLoading = false;\n        this.updateSliderOnBrandsLoad();\n      }));\n    }\n    subscribeLikedProducts() {\n      this.subscription.add(this.socialService.likedProducts$.subscribe(likedProducts => {\n        this.likedProducts = likedProducts;\n      }));\n    }\n    loadFeaturedBrands() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        try {\n          _this.isLoading = true;\n          _this.error = null;\n          yield _this.trendingService.loadFeaturedBrands();\n        } catch (error) {\n          console.error('Error loading featured brands:', error);\n          _this.error = 'Failed to load featured brands';\n          _this.isLoading = false;\n        }\n      })();\n    }\n    onBrandClick(brand) {\n      this.router.navigate(['/products'], {\n        queryParams: {\n          brand: brand.brand\n        }\n      });\n    }\n    onProductClick(product, event) {\n      event.stopPropagation();\n      this.router.navigate(['/product', product._id]);\n    }\n    onLikeProduct(product, event) {\n      var _this2 = this;\n      return _asyncToGenerator(function* () {\n        event.stopPropagation();\n        try {\n          const result = yield _this2.socialService.likeProduct(product._id);\n          if (result.success) {\n            console.log(result.message);\n          } else {\n            console.error('Failed to like product:', result.message);\n          }\n        } catch (error) {\n          console.error('Error liking product:', error);\n        }\n      })();\n    }\n    onShareProduct(product, event) {\n      var _this3 = this;\n      return _asyncToGenerator(function* () {\n        event.stopPropagation();\n        try {\n          const productUrl = `${window.location.origin}/product/${product._id}`;\n          yield navigator.clipboard.writeText(productUrl);\n          yield _this3.socialService.shareProduct(product._id, {\n            platform: 'copy_link',\n            message: `Check out this amazing ${product.name} from ${product.brand}!`\n          });\n          console.log('Product link copied to clipboard!');\n        } catch (error) {\n          console.error('Error sharing product:', error);\n        }\n      })();\n    }\n    formatPrice(price) {\n      return new Intl.NumberFormat('en-IN', {\n        style: 'currency',\n        currency: 'INR',\n        minimumFractionDigits: 0\n      }).format(price);\n    }\n    formatNumber(num) {\n      if (num >= 1000000) {\n        return (num / 1000000).toFixed(1) + 'M';\n      } else if (num >= 1000) {\n        return (num / 1000).toFixed(1) + 'K';\n      }\n      return num.toString();\n    }\n    onRetry() {\n      this.loadFeaturedBrands();\n    }\n    trackByBrandName(index, brand) {\n      return brand.brand;\n    }\n    isProductLiked(productId) {\n      return this.likedProducts.has(productId);\n    }\n    trackByProductId(index, product) {\n      return product._id;\n    }\n    // Auto-sliding methods\n    startAutoSlide() {\n      if (!this.isAutoSliding || this.isPaused) return;\n      this.stopAutoSlide();\n      this.autoSlideInterval = setInterval(() => {\n        if (!this.isPaused && this.featuredBrands.length > this.visibleCards) {\n          this.autoSlideNext();\n        }\n      }, this.autoSlideDelay);\n    }\n    stopAutoSlide() {\n      if (this.autoSlideInterval) {\n        clearInterval(this.autoSlideInterval);\n        this.autoSlideInterval = null;\n      }\n    }\n    autoSlideNext() {\n      if (this.currentSlide >= this.maxSlide) {\n        this.currentSlide = 0;\n      } else {\n        this.currentSlide++;\n      }\n      this.updateSlideOffset();\n    }\n    pauseAutoSlide() {\n      this.isPaused = true;\n      this.stopAutoSlide();\n    }\n    resumeAutoSlide() {\n      this.isPaused = false;\n      this.startAutoSlide();\n    }\n    // Responsive methods\n    updateResponsiveSettings() {\n      const width = window.innerWidth;\n      if (width <= 768) {\n        this.cardWidth = 280;\n        this.visibleCards = 1;\n      } else if (width <= 1200) {\n        this.cardWidth = 320;\n        this.visibleCards = 2;\n      } else {\n        this.cardWidth = 340;\n        this.visibleCards = 3;\n      }\n      this.updateSliderLimits();\n      this.updateSlideOffset();\n    }\n    setupResizeListener() {\n      window.addEventListener('resize', () => {\n        this.updateResponsiveSettings();\n      });\n    }\n    // Slider methods\n    updateSliderLimits() {\n      this.maxSlide = Math.max(0, this.featuredBrands.length - this.visibleCards);\n    }\n    slidePrev() {\n      if (this.currentSlide > 0) {\n        this.currentSlide--;\n        this.updateSlideOffset();\n        this.restartAutoSlideAfterInteraction();\n      }\n    }\n    slideNext() {\n      if (this.currentSlide < this.maxSlide) {\n        this.currentSlide++;\n        this.updateSlideOffset();\n        this.restartAutoSlideAfterInteraction();\n      }\n    }\n    updateSlideOffset() {\n      this.slideOffset = -this.currentSlide * this.cardWidth;\n    }\n    restartAutoSlideAfterInteraction() {\n      this.stopAutoSlide();\n      setTimeout(() => {\n        this.startAutoSlide();\n      }, 2000);\n    }\n    // Update slider when brands load\n    updateSliderOnBrandsLoad() {\n      setTimeout(() => {\n        this.updateSliderLimits();\n        this.currentSlide = 0;\n        this.slideOffset = 0;\n        this.startAutoSlide();\n      }, 100);\n    }\n    // Section interaction methods\n    toggleSectionLike() {\n      this.isSectionLiked = !this.isSectionLiked;\n      if (this.isSectionLiked) {\n        this.sectionLikes++;\n      } else {\n        this.sectionLikes--;\n      }\n    }\n    toggleSectionBookmark() {\n      this.isSectionBookmarked = !this.isSectionBookmarked;\n    }\n    openComments() {\n      console.log('Opening comments for featured brands section');\n    }\n    shareSection() {\n      if (navigator.share) {\n        navigator.share({\n          title: 'Featured Brands',\n          text: 'Check out these amazing featured fashion brands!',\n          url: window.location.href\n        });\n      } else {\n        navigator.clipboard.writeText(window.location.href);\n        console.log('Link copied to clipboard');\n      }\n    }\n    openMusicPlayer() {\n      console.log('Opening music player for featured brands');\n    }\n    formatCount(count) {\n      if (count >= 1000000) {\n        return (count / 1000000).toFixed(1) + 'M';\n      } else if (count >= 1000) {\n        return (count / 1000).toFixed(1) + 'K';\n      }\n      return count.toString();\n    }\n    checkMobileDevice() {\n      this.isMobile = window.innerWidth <= 768;\n    }\n    static {\n      this.ɵfac = function FeaturedBrandsComponent_Factory(t) {\n        return new (t || FeaturedBrandsComponent)(i0.ɵɵdirectiveInject(i1.TrendingService), i0.ɵɵdirectiveInject(i2.SocialInteractionsService), i0.ɵɵdirectiveInject(i3.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: FeaturedBrandsComponent,\n        selectors: [[\"app-featured-brands\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 13,\n        vars: 5,\n        consts: [[1, \"featured-brands-container\"], [\"class\", \"mobile-action-buttons\", 4, \"ngIf\"], [1, \"section-header\"], [1, \"header-content\"], [1, \"section-title\"], [\"name\", \"diamond\", 1, \"title-icon\"], [1, \"section-subtitle\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"brands-slider-container\", 4, \"ngIf\"], [\"class\", \"empty-container\", 4, \"ngIf\"], [1, \"mobile-action-buttons\"], [1, \"action-btn\", \"like-btn\", 3, \"click\"], [3, \"name\"], [1, \"action-count\"], [1, \"action-btn\", \"comment-btn\", 3, \"click\"], [\"name\", \"chatbubble-outline\"], [1, \"action-btn\", \"share-btn\", 3, \"click\"], [\"name\", \"arrow-redo-outline\"], [1, \"action-text\"], [1, \"action-btn\", \"bookmark-btn\", 3, \"click\"], [1, \"action-btn\", \"music-btn\", 3, \"click\"], [\"name\", \"musical-notes\"], [1, \"loading-container\"], [1, \"loading-grid\"], [\"class\", \"loading-brand-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"loading-brand-card\"], [1, \"loading-header\"], [1, \"loading-brand-name\"], [1, \"loading-stats\"], [1, \"loading-products\"], [\"class\", \"loading-product\", 4, \"ngFor\", \"ngForOf\"], [1, \"loading-product\"], [1, \"error-container\"], [\"name\", \"alert-circle\", 1, \"error-icon\"], [1, \"error-message\"], [1, \"retry-btn\", 3, \"click\"], [\"name\", \"refresh\"], [1, \"brands-slider-container\"], [1, \"slider-nav\", \"prev-btn\", 3, \"click\", \"disabled\"], [\"name\", \"chevron-back\"], [1, \"slider-nav\", \"next-btn\", 3, \"click\", \"disabled\"], [\"name\", \"chevron-forward\"], [1, \"brands-slider-wrapper\", 3, \"mouseenter\", \"mouseleave\"], [1, \"brands-slider\"], [\"class\", \"brand-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"brand-card\", 3, \"click\"], [1, \"brand-header\"], [1, \"brand-info\"], [1, \"brand-name\"], [1, \"brand-stats\"], [1, \"stat-item\"], [\"name\", \"bag-outline\"], [\"name\", \"star\"], [\"name\", \"eye-outline\"], [1, \"brand-badge\"], [\"name\", \"diamond\"], [1, \"top-products\"], [1, \"products-title\"], [1, \"products-list\"], [\"class\", \"product-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"view-more-section\"], [1, \"view-more-btn\"], [1, \"product-item\", 3, \"click\"], [1, \"product-image-container\"], [\"loading\", \"lazy\", 1, \"product-image\", 3, \"src\", \"alt\"], [1, \"product-actions\"], [\"name\", \"share-outline\"], [1, \"product-details\"], [1, \"product-name\"], [1, \"product-price\"], [1, \"current-price\"], [\"class\", \"original-price\", 4, \"ngIf\"], [1, \"product-rating\"], [1, \"stars\"], [3, \"name\", \"filled\", 4, \"ngFor\", \"ngForOf\"], [1, \"rating-count\"], [1, \"original-price\"], [1, \"empty-container\"], [\"name\", \"diamond-outline\", 1, \"empty-icon\"], [1, \"empty-title\"], [1, \"empty-message\"]],\n        template: function FeaturedBrandsComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0);\n            i0.ɵɵtemplate(1, FeaturedBrandsComponent_div_1_Template, 19, 8, \"div\", 1);\n            i0.ɵɵelementStart(2, \"div\", 2)(3, \"div\", 3)(4, \"h2\", 4);\n            i0.ɵɵelement(5, \"ion-icon\", 5);\n            i0.ɵɵtext(6, \" Featured Brands \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(7, \"p\", 6);\n            i0.ɵɵtext(8, \"Top brands with amazing collections\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(9, FeaturedBrandsComponent_div_9_Template, 3, 2, \"div\", 7)(10, FeaturedBrandsComponent_div_10_Template, 7, 1, \"div\", 8)(11, FeaturedBrandsComponent_div_11_Template, 8, 6, \"div\", 9)(12, FeaturedBrandsComponent_div_12_Template, 6, 0, \"div\", 10);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isMobile);\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.error && !ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.featuredBrands.length > 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.featuredBrands.length === 0);\n          }\n        },\n        dependencies: [CommonModule, i4.NgForOf, i4.NgIf, IonicModule, i5.IonIcon, CarouselModule],\n        styles: [\".featured-brands-container[_ngcontent-%COMP%]{padding:20px;background:linear-gradient(135deg,#667eea,#764ba2);border-radius:16px;margin-bottom:24px;color:#fff;position:relative}.mobile-action-buttons[_ngcontent-%COMP%]{position:absolute;right:15px;top:50%;transform:translateY(-50%);display:flex;flex-direction:column;gap:20px;z-index:10}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]{width:48px;height:48px;border-radius:50%;border:none;background:#fff3;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);color:#fff;display:flex;flex-direction:column;align-items:center;justify-content:center;cursor:pointer;transition:all .3s ease;position:relative}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:24px;margin-bottom:2px}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   .action-count[_ngcontent-%COMP%], .mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   .action-text[_ngcontent-%COMP%]{font-size:10px;font-weight:600;line-height:1}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:hover{transform:scale(1.1);background:#ffffff4d}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active[_ngcontent-%COMP%]{background:#ffffffe6;color:#667eea}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active.like-btn[_ngcontent-%COMP%]{color:#ff3040}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active.like-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_heartBeat .6s ease-in-out}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active.bookmark-btn[_ngcontent-%COMP%]{color:gold}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.like-btn.active[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:#ff3040}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.music-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,#ff3040,#667eea)}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.music-btn[_ngcontent-%COMP%]:hover{transform:scale(1.1) rotate(15deg)}@keyframes _ngcontent-%COMP%_heartBeat{0%{transform:scale(1)}25%{transform:scale(1.3)}50%{transform:scale(1.1)}75%{transform:scale(1.25)}to{transform:scale(1)}}@media (min-width: 769px){.mobile-action-buttons[_ngcontent-%COMP%]{display:none}}.section-header[_ngcontent-%COMP%]{margin-bottom:24px}.section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]{text-align:center}.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{font-size:24px;font-weight:700;color:#fff;margin:0 0 8px;display:flex;align-items:center;justify-content:center;gap:12px}.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   .title-icon[_ngcontent-%COMP%]{font-size:28px;color:gold}.section-header[_ngcontent-%COMP%]   .section-subtitle[_ngcontent-%COMP%]{font-size:14px;color:#fffc;margin:0}.loading-container[_ngcontent-%COMP%]   .loading-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(350px,1fr));gap:20px}.loading-container[_ngcontent-%COMP%]   .loading-brand-card[_ngcontent-%COMP%]{background:#ffffff1a;border-radius:16px;padding:20px;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.loading-container[_ngcontent-%COMP%]   .loading-brand-card[_ngcontent-%COMP%]   .loading-header[_ngcontent-%COMP%]{margin-bottom:16px}.loading-container[_ngcontent-%COMP%]   .loading-brand-card[_ngcontent-%COMP%]   .loading-header[_ngcontent-%COMP%]   .loading-brand-name[_ngcontent-%COMP%]{height:24px;background:#fff3;border-radius:8px;margin-bottom:8px;animation:_ngcontent-%COMP%_loading 1.5s infinite}.loading-container[_ngcontent-%COMP%]   .loading-brand-card[_ngcontent-%COMP%]   .loading-header[_ngcontent-%COMP%]   .loading-stats[_ngcontent-%COMP%]{height:16px;background:#fff3;border-radius:8px;width:70%;animation:_ngcontent-%COMP%_loading 1.5s infinite}.loading-container[_ngcontent-%COMP%]   .loading-brand-card[_ngcontent-%COMP%]   .loading-products[_ngcontent-%COMP%]{display:flex;gap:12px}.loading-container[_ngcontent-%COMP%]   .loading-brand-card[_ngcontent-%COMP%]   .loading-products[_ngcontent-%COMP%]   .loading-product[_ngcontent-%COMP%]{flex:1;height:120px;background:#fff3;border-radius:12px;animation:_ngcontent-%COMP%_loading 1.5s infinite}@keyframes _ngcontent-%COMP%_loading{0%,to{opacity:.6}50%{opacity:1}}.error-container[_ngcontent-%COMP%]{text-align:center;padding:40px 20px}.error-container[_ngcontent-%COMP%]   .error-icon[_ngcontent-%COMP%]{font-size:48px;color:#ff6b6b;margin-bottom:16px}.error-container[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%]{font-size:16px;color:#fffc;margin-bottom:20px}.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]{background:#fff3;color:#fff;border:2px solid rgba(255,255,255,.3);padding:12px 24px;border-radius:8px;font-weight:600;cursor:pointer;display:flex;align-items:center;gap:8px;margin:0 auto;transition:all .3s ease;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]:hover{background:#ffffff4d;border-color:#ffffff80}.brands-slider-container[_ngcontent-%COMP%]{position:relative;margin:0 -20px}.brands-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]{position:absolute;top:50%;transform:translateY(-50%);z-index:10;background:#000000b3;color:#fff;border:none;width:40px;height:40px;border-radius:50%;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all .3s ease}.brands-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]:hover:not(:disabled){background:#000000e6;transform:translateY(-50%) scale(1.1)}.brands-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]:disabled{opacity:.3;cursor:not-allowed}.brands-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px}.brands-slider-container[_ngcontent-%COMP%]   .slider-nav.prev-btn[_ngcontent-%COMP%]{left:-20px}.brands-slider-container[_ngcontent-%COMP%]   .slider-nav.next-btn[_ngcontent-%COMP%]{right:-20px}.brands-slider-wrapper[_ngcontent-%COMP%]{overflow:hidden;padding:0 20px}.brands-slider[_ngcontent-%COMP%]{display:flex;transition:transform .4s cubic-bezier(.25,.46,.45,.94);gap:20px}.brands-slider[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]{flex:0 0 300px;width:300px}@media (max-width: 1200px){.brands-slider[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]{flex:0 0 280px;width:280px}}@media (max-width: 768px){.brands-slider-container[_ngcontent-%COMP%]{margin:0 -10px}.brands-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]{width:35px;height:35px}.brands-slider-container[_ngcontent-%COMP%]   .slider-nav.prev-btn[_ngcontent-%COMP%]{left:-15px}.brands-slider-container[_ngcontent-%COMP%]   .slider-nav.next-btn[_ngcontent-%COMP%]{right:-15px}.brands-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:16px}.brands-slider-wrapper[_ngcontent-%COMP%]{padding:0 10px}.brands-slider[_ngcontent-%COMP%]{gap:15px}.brands-slider[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]{flex:0 0 260px;width:260px}}.brands-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(350px,1fr));gap:20px}.brand-card[_ngcontent-%COMP%]{background:#ffffff1a;border-radius:16px;padding:20px;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border:1px solid rgba(255,255,255,.2);transition:all .3s ease;cursor:pointer}.brand-card[_ngcontent-%COMP%]:hover{transform:translateY(-8px);background:#ffffff26;box-shadow:0 12px 40px #0003}.brand-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:20px}.brand-header[_ngcontent-%COMP%]   .brand-info[_ngcontent-%COMP%]{flex:1}.brand-header[_ngcontent-%COMP%]   .brand-name[_ngcontent-%COMP%]{font-size:20px;font-weight:700;color:#fff;margin:0 0 12px}.brand-header[_ngcontent-%COMP%]   .brand-stats[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:6px}.brand-header[_ngcontent-%COMP%]   .brand-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-size:12px;color:#fffc}.brand-header[_ngcontent-%COMP%]   .brand-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:14px;color:gold}.brand-header[_ngcontent-%COMP%]   .brand-badge[_ngcontent-%COMP%]{background:linear-gradient(135deg,gold,#ffed4e);color:#333;padding:8px 12px;border-radius:20px;font-size:12px;font-weight:600;display:flex;align-items:center;gap:6px}.brand-header[_ngcontent-%COMP%]   .brand-badge[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:14px}.top-products[_ngcontent-%COMP%]{margin-bottom:20px}.top-products[_ngcontent-%COMP%]   .products-title[_ngcontent-%COMP%]{font-size:16px;font-weight:600;color:#fff;margin:0 0 16px}.top-products[_ngcontent-%COMP%]   .products-list[_ngcontent-%COMP%]{display:flex;gap:12px;overflow-x:auto;padding-bottom:8px}.top-products[_ngcontent-%COMP%]   .products-list[_ngcontent-%COMP%]::-webkit-scrollbar{height:4px}.top-products[_ngcontent-%COMP%]   .products-list[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:#ffffff1a;border-radius:2px}.top-products[_ngcontent-%COMP%]   .products-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#ffffff4d;border-radius:2px}.product-item[_ngcontent-%COMP%]{flex:0 0 140px;background:#ffffff1a;border-radius:12px;overflow:hidden;transition:all .3s ease;cursor:pointer}.product-item[_ngcontent-%COMP%]:hover{transform:translateY(-4px);background:#ffffff26}.product-item[_ngcontent-%COMP%]:hover   .product-actions[_ngcontent-%COMP%]{opacity:1}.product-image-container[_ngcontent-%COMP%]{position:relative}.product-image-container[_ngcontent-%COMP%]   .product-image[_ngcontent-%COMP%]{width:100%;height:100px;object-fit:cover}.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]{position:absolute;top:8px;right:8px;display:flex;flex-direction:column;gap:4px;opacity:0;transition:opacity .3s ease}.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]{width:28px;height:28px;border-radius:50%;border:none;background:#ffffffe6;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all .3s ease}.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:14px;color:#333}.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:hover{background:#fff;transform:scale(1.1)}.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn.like-btn[_ngcontent-%COMP%]:hover   ion-icon[_ngcontent-%COMP%]{color:#dc3545}.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn.like-btn.liked[_ngcontent-%COMP%]{background:#dc354533}.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn.like-btn.liked[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:#dc3545}.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn.share-btn[_ngcontent-%COMP%]:hover   ion-icon[_ngcontent-%COMP%]{color:#007bff}.product-details[_ngcontent-%COMP%]{padding:12px}.product-details[_ngcontent-%COMP%]   .product-name[_ngcontent-%COMP%]{font-size:12px;font-weight:600;color:#fff;margin:0 0 8px;line-height:1.3;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;overflow:hidden}.product-details[_ngcontent-%COMP%]   .product-price[_ngcontent-%COMP%]{display:flex;align-items:center;gap:6px;margin-bottom:8px}.product-details[_ngcontent-%COMP%]   .product-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%]{font-size:14px;font-weight:700;color:gold}.product-details[_ngcontent-%COMP%]   .product-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%]{font-size:10px;color:#fff9;text-decoration:line-through}.product-details[_ngcontent-%COMP%]   .product-rating[_ngcontent-%COMP%]{display:flex;align-items:center;gap:6px}.product-details[_ngcontent-%COMP%]   .product-rating[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]{display:flex;gap:1px}.product-details[_ngcontent-%COMP%]   .product-rating[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:10px;color:#ffffff4d}.product-details[_ngcontent-%COMP%]   .product-rating[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   ion-icon.filled[_ngcontent-%COMP%]{color:gold}.product-details[_ngcontent-%COMP%]   .product-rating[_ngcontent-%COMP%]   .rating-count[_ngcontent-%COMP%]{font-size:10px;color:#fff9}.view-more-section[_ngcontent-%COMP%]   .view-more-btn[_ngcontent-%COMP%]{width:100%;background:#ffffff1a;border:2px solid rgba(255,255,255,.2);color:#fff;padding:12px 16px;border-radius:8px;font-weight:600;font-size:14px;display:flex;align-items:center;justify-content:center;gap:8px;cursor:pointer;transition:all .3s ease}.view-more-section[_ngcontent-%COMP%]   .view-more-btn[_ngcontent-%COMP%]:hover{background:#fff3;border-color:#fff6}.view-more-section[_ngcontent-%COMP%]   .view-more-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:16px}.empty-container[_ngcontent-%COMP%]{text-align:center;padding:60px 20px}.empty-container[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%]{font-size:64px;color:#fff6;margin-bottom:20px}.empty-container[_ngcontent-%COMP%]   .empty-title[_ngcontent-%COMP%]{font-size:20px;font-weight:600;color:#fff;margin-bottom:8px}.empty-container[_ngcontent-%COMP%]   .empty-message[_ngcontent-%COMP%]{font-size:14px;color:#ffffffb3}@media (max-width: 768px){.featured-brands-container[_ngcontent-%COMP%]{padding:16px}.brands-grid[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:16px}.brand-header[_ngcontent-%COMP%]{flex-direction:column;gap:12px}.brand-header[_ngcontent-%COMP%]   .brand-badge[_ngcontent-%COMP%]{align-self:flex-start}.brand-stats[_ngcontent-%COMP%]{flex-direction:row!important;flex-wrap:wrap;gap:12px!important}.section-title[_ngcontent-%COMP%]{font-size:20px}}\"]\n      });\n    }\n  }\n  return FeaturedBrandsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}