{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashionFrontend/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CommonModule } from '@angular/common';\nimport { Subscription } from 'rxjs';\nimport { IonicModule } from '@ionic/angular';\nimport { CarouselModule } from 'ngx-owl-carousel-o';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/trending.service\";\nimport * as i2 from \"../../../../core/services/social-interactions.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@ionic/angular\";\nconst _c0 = () => [1, 2, 3, 4];\nconst _c1 = () => [1, 2, 3];\nconst _c2 = () => [1, 2, 3, 4, 5];\nfunction FeaturedBrandsComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_1_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleSectionLike());\n    });\n    i0.ɵɵelement(2, \"ion-icon\", 14);\n    i0.ɵɵelementStart(3, \"span\", 15);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_1_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openComments());\n    });\n    i0.ɵɵelement(6, \"ion-icon\", 17);\n    i0.ɵɵelementStart(7, \"span\", 15);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_1_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.shareSection());\n    });\n    i0.ɵɵelement(10, \"ion-icon\", 19);\n    i0.ɵɵelementStart(11, \"span\", 20);\n    i0.ɵɵtext(12, \"Share\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_1_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleSectionBookmark());\n    });\n    i0.ɵɵelement(14, \"ion-icon\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_1_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openMusicPlayer());\n    });\n    i0.ɵɵelement(16, \"ion-icon\", 23);\n    i0.ɵɵelementStart(17, \"span\", 20);\n    i0.ɵɵtext(18, \"Music\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"active\", ctx_r1.isSectionLiked);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", ctx_r1.isSectionLiked ? \"heart\" : \"heart-outline\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.formatCount(ctx_r1.sectionLikes));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.formatCount(ctx_r1.sectionComments));\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"active\", ctx_r1.isSectionBookmarked);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", ctx_r1.isSectionBookmarked ? \"bookmark\" : \"bookmark-outline\");\n  }\n}\nfunction FeaturedBrandsComponent_div_9_div_2_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 33);\n  }\n}\nfunction FeaturedBrandsComponent_div_9_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"div\", 28);\n    i0.ɵɵelement(2, \"div\", 29)(3, \"div\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 31);\n    i0.ɵɵtemplate(5, FeaturedBrandsComponent_div_9_div_2_div_5_Template, 1, 0, \"div\", 32);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c1));\n  }\n}\nfunction FeaturedBrandsComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"div\", 25);\n    i0.ɵɵtemplate(2, FeaturedBrandsComponent_div_9_div_2_Template, 6, 2, \"div\", 26);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c0));\n  }\n}\nfunction FeaturedBrandsComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵelement(1, \"ion-icon\", 35);\n    i0.ɵɵelementStart(2, \"p\", 36);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_10_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onRetry());\n    });\n    i0.ɵɵelement(5, \"ion-icon\", 38);\n    i0.ɵɵtext(6, \" Try Again \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction FeaturedBrandsComponent_div_14_div_7_div_25_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 78);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r8 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.formatPrice(product_r8.originalPrice));\n  }\n}\nfunction FeaturedBrandsComponent_div_14_div_7_div_25_ion_icon_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ion-icon\", 14);\n  }\n  if (rf & 2) {\n    const star_r9 = ctx.$implicit;\n    const product_r8 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵclassProp(\"filled\", star_r9 <= product_r8.rating.average);\n    i0.ɵɵproperty(\"name\", star_r9 <= product_r8.rating.average ? \"star\" : \"star-outline\");\n  }\n}\nfunction FeaturedBrandsComponent_div_14_div_7_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 64);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_14_div_7_div_25_Template_div_click_0_listener($event) {\n      const product_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onProductClick(product_r8, $event));\n    });\n    i0.ɵɵelementStart(1, \"div\", 65);\n    i0.ɵɵelement(2, \"img\", 66);\n    i0.ɵɵelementStart(3, \"div\", 67)(4, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_14_div_7_div_25_Template_button_click_4_listener($event) {\n      const product_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onLikeProduct(product_r8, $event));\n    });\n    i0.ɵɵelement(5, \"ion-icon\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_14_div_7_div_25_Template_button_click_6_listener($event) {\n      const product_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onShareProduct(product_r8, $event));\n    });\n    i0.ɵɵelement(7, \"ion-icon\", 68);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(8, \"div\", 69)(9, \"h5\", 70);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 71)(12, \"span\", 72);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, FeaturedBrandsComponent_div_14_div_7_div_25_span_14_Template, 2, 1, \"span\", 73);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 74)(16, \"div\", 75);\n    i0.ɵɵtemplate(17, FeaturedBrandsComponent_div_14_div_7_div_25_ion_icon_17_Template, 1, 3, \"ion-icon\", 76);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"span\", 77);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const product_r8 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", product_r8.images[0].url, i0.ɵɵsanitizeUrl)(\"alt\", product_r8.images[0].alt || product_r8.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"liked\", ctx_r1.isProductLiked(product_r8._id));\n    i0.ɵɵattribute(\"aria-label\", \"Like \" + product_r8.name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", ctx_r1.isProductLiked(product_r8._id) ? \"heart\" : \"heart-outline\");\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"aria-label\", \"Share \" + product_r8.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(product_r8.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.formatPrice(product_r8.price));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r8.originalPrice && product_r8.originalPrice > product_r8.price);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(12, _c2));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", product_r8.rating.count, \")\");\n  }\n}\nfunction FeaturedBrandsComponent_div_14_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_14_div_7_Template_div_click_0_listener() {\n      const brand_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onBrandClick(brand_r6));\n    });\n    i0.ɵɵelementStart(1, \"div\", 48)(2, \"div\", 49)(3, \"h3\", 50);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 51)(6, \"div\", 52);\n    i0.ɵɵelement(7, \"ion-icon\", 53);\n    i0.ɵɵelementStart(8, \"span\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 52);\n    i0.ɵɵelement(11, \"ion-icon\", 54);\n    i0.ɵɵelementStart(12, \"span\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 52);\n    i0.ɵɵelement(15, \"ion-icon\", 55);\n    i0.ɵɵelementStart(16, \"span\");\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(18, \"div\", 56);\n    i0.ɵɵelement(19, \"ion-icon\", 57);\n    i0.ɵɵtext(20, \" Featured \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 58)(22, \"h4\", 59);\n    i0.ɵɵtext(23, \"Top Products\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 60);\n    i0.ɵɵtemplate(25, FeaturedBrandsComponent_div_14_div_7_div_25_Template, 20, 13, \"div\", 61);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\", 62)(27, \"button\", 63)(28, \"span\");\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(30, \"ion-icon\", 43);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const brand_r6 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(brand_r6.brand);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", brand_r6.productCount, \" Products\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", brand_r6.avgRating, \"/5\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.formatNumber(brand_r6.totalViews), \" Views\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", brand_r6.topProducts)(\"ngForTrackBy\", ctx_r1.trackByProductId);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"View All \", brand_r6.brand, \" Products\");\n  }\n}\nfunction FeaturedBrandsComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_14_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.slidePrev());\n    });\n    i0.ɵɵelement(2, \"ion-icon\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function FeaturedBrandsComponent_div_14_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.slideNext());\n    });\n    i0.ɵɵelement(4, \"ion-icon\", 43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 44);\n    i0.ɵɵlistener(\"mouseenter\", function FeaturedBrandsComponent_div_14_Template_div_mouseenter_5_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.pauseAutoSlide());\n    })(\"mouseleave\", function FeaturedBrandsComponent_div_14_Template_div_mouseleave_5_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.resumeAutoSlide());\n    });\n    i0.ɵɵelementStart(6, \"div\", 45);\n    i0.ɵɵtemplate(7, FeaturedBrandsComponent_div_14_div_7_Template, 31, 7, \"div\", 46);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentSlide === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentSlide >= ctx_r1.maxSlide);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"transform\", \"translateX(\" + ctx_r1.slideOffset + \"px)\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.featuredBrands)(\"ngForTrackBy\", ctx_r1.trackByBrandName);\n  }\n}\nfunction FeaturedBrandsComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 79);\n    i0.ɵɵelement(1, \"ion-icon\", 80);\n    i0.ɵɵelementStart(2, \"h3\", 81);\n    i0.ɵɵtext(3, \"No Featured Brands\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 82);\n    i0.ɵɵtext(5, \"Check back later for featured brand collections\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class FeaturedBrandsComponent {\n  constructor(trendingService, socialService, router) {\n    this.trendingService = trendingService;\n    this.socialService = socialService;\n    this.router = router;\n    this.featuredBrands = [];\n    this.isLoading = true;\n    this.error = null;\n    this.likedProducts = new Set();\n    this.subscription = new Subscription();\n    // Slider properties\n    this.currentSlide = 0;\n    this.slideOffset = 0;\n    this.cardWidth = 320; // Width of each brand card including margin\n    this.visibleCards = 3; // Number of cards visible at once\n    this.maxSlide = 0;\n    this.autoSlideDelay = 4000; // 4 seconds for brands\n    this.isAutoSliding = true;\n    this.isPaused = false;\n    // Section interaction properties\n    this.isSectionLiked = false;\n    this.isSectionBookmarked = false;\n    this.sectionLikes = 287;\n    this.sectionComments = 89;\n    this.isMobile = false;\n  }\n  ngOnInit() {\n    console.log('FeaturedBrandsComponent initialized');\n    this.loadFeaturedBrands();\n    this.subscribeFeaturedBrands();\n    this.subscribeLikedProducts();\n    this.updateResponsiveSettings();\n    this.setupResizeListener();\n    this.checkMobileDevice();\n  }\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n    this.stopAutoSlide();\n  }\n  subscribeFeaturedBrands() {\n    this.subscription.add(this.trendingService.featuredBrands$.subscribe(brands => {\n      console.log('FeaturedBrandsComponent received brands:', brands);\n      this.featuredBrands = brands;\n      this.isLoading = false;\n      this.updateSliderOnBrandsLoad();\n    }));\n  }\n  subscribeLikedProducts() {\n    this.subscription.add(this.socialService.likedProducts$.subscribe(likedProducts => {\n      this.likedProducts = likedProducts;\n    }));\n  }\n  loadFeaturedBrands() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this.isLoading = true;\n        _this.error = null;\n        yield _this.trendingService.loadFeaturedBrands();\n      } catch (error) {\n        console.error('Error loading featured brands:', error);\n        _this.error = 'Failed to load featured brands';\n        _this.isLoading = false;\n      }\n    })();\n  }\n  onBrandClick(brand) {\n    this.router.navigate(['/products'], {\n      queryParams: {\n        brand: brand.brand\n      }\n    });\n  }\n  onProductClick(product, event) {\n    event.stopPropagation();\n    this.router.navigate(['/product', product._id]);\n  }\n  onLikeProduct(product, event) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      event.stopPropagation();\n      try {\n        const result = yield _this2.socialService.likeProduct(product._id);\n        if (result.success) {\n          console.log(result.message);\n        } else {\n          console.error('Failed to like product:', result.message);\n        }\n      } catch (error) {\n        console.error('Error liking product:', error);\n      }\n    })();\n  }\n  onShareProduct(product, event) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      event.stopPropagation();\n      try {\n        const productUrl = `${window.location.origin}/product/${product._id}`;\n        yield navigator.clipboard.writeText(productUrl);\n        yield _this3.socialService.shareProduct(product._id, {\n          platform: 'copy_link',\n          message: `Check out this amazing ${product.name} from ${product.brand}!`\n        });\n        console.log('Product link copied to clipboard!');\n      } catch (error) {\n        console.error('Error sharing product:', error);\n      }\n    })();\n  }\n  formatPrice(price) {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0\n    }).format(price);\n  }\n  formatNumber(num) {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n  onRetry() {\n    this.loadFeaturedBrands();\n  }\n  trackByBrandName(index, brand) {\n    return brand.brand;\n  }\n  isProductLiked(productId) {\n    return this.likedProducts.has(productId);\n  }\n  trackByProductId(index, product) {\n    return product._id;\n  }\n  // Auto-sliding methods\n  startAutoSlide() {\n    if (!this.isAutoSliding || this.isPaused) return;\n    this.stopAutoSlide();\n    this.autoSlideInterval = setInterval(() => {\n      if (!this.isPaused && this.featuredBrands.length > this.visibleCards) {\n        this.autoSlideNext();\n      }\n    }, this.autoSlideDelay);\n  }\n  stopAutoSlide() {\n    if (this.autoSlideInterval) {\n      clearInterval(this.autoSlideInterval);\n      this.autoSlideInterval = null;\n    }\n  }\n  autoSlideNext() {\n    if (this.currentSlide >= this.maxSlide) {\n      this.currentSlide = 0;\n    } else {\n      this.currentSlide++;\n    }\n    this.updateSlideOffset();\n  }\n  pauseAutoSlide() {\n    this.isPaused = true;\n    this.stopAutoSlide();\n  }\n  resumeAutoSlide() {\n    this.isPaused = false;\n    this.startAutoSlide();\n  }\n  // Responsive methods\n  updateResponsiveSettings() {\n    const width = window.innerWidth;\n    if (width <= 768) {\n      this.cardWidth = 280;\n      this.visibleCards = 1;\n    } else if (width <= 1200) {\n      this.cardWidth = 320;\n      this.visibleCards = 2;\n    } else {\n      this.cardWidth = 340;\n      this.visibleCards = 3;\n    }\n    this.updateSliderLimits();\n    this.updateSlideOffset();\n  }\n  setupResizeListener() {\n    window.addEventListener('resize', () => {\n      this.updateResponsiveSettings();\n    });\n  }\n  // Slider methods\n  updateSliderLimits() {\n    this.maxSlide = Math.max(0, this.featuredBrands.length - this.visibleCards);\n  }\n  slidePrev() {\n    if (this.currentSlide > 0) {\n      this.currentSlide--;\n      this.updateSlideOffset();\n      this.restartAutoSlideAfterInteraction();\n    }\n  }\n  slideNext() {\n    if (this.currentSlide < this.maxSlide) {\n      this.currentSlide++;\n      this.updateSlideOffset();\n      this.restartAutoSlideAfterInteraction();\n    }\n  }\n  updateSlideOffset() {\n    this.slideOffset = -this.currentSlide * this.cardWidth;\n  }\n  restartAutoSlideAfterInteraction() {\n    this.stopAutoSlide();\n    setTimeout(() => {\n      this.startAutoSlide();\n    }, 2000);\n  }\n  // Update slider when brands load\n  updateSliderOnBrandsLoad() {\n    setTimeout(() => {\n      this.updateSliderLimits();\n      this.currentSlide = 0;\n      this.slideOffset = 0;\n      this.startAutoSlide();\n    }, 100);\n  }\n  // Section interaction methods\n  toggleSectionLike() {\n    this.isSectionLiked = !this.isSectionLiked;\n    if (this.isSectionLiked) {\n      this.sectionLikes++;\n    } else {\n      this.sectionLikes--;\n    }\n  }\n  toggleSectionBookmark() {\n    this.isSectionBookmarked = !this.isSectionBookmarked;\n  }\n  openComments() {\n    console.log('Opening comments for featured brands section');\n  }\n  shareSection() {\n    if (navigator.share) {\n      navigator.share({\n        title: 'Featured Brands',\n        text: 'Check out these amazing featured fashion brands!',\n        url: window.location.href\n      });\n    } else {\n      navigator.clipboard.writeText(window.location.href);\n      console.log('Link copied to clipboard');\n    }\n  }\n  openMusicPlayer() {\n    console.log('Opening music player for featured brands');\n  }\n  formatCount(count) {\n    if (count >= 1000000) {\n      return (count / 1000000).toFixed(1) + 'M';\n    } else if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'K';\n    }\n    return count.toString();\n  }\n  checkMobileDevice() {\n    this.isMobile = window.innerWidth <= 768;\n  }\n  static {\n    this.ɵfac = function FeaturedBrandsComponent_Factory(t) {\n      return new (t || FeaturedBrandsComponent)(i0.ɵɵdirectiveInject(i1.TrendingService), i0.ɵɵdirectiveInject(i2.SocialInteractionsService), i0.ɵɵdirectiveInject(i3.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: FeaturedBrandsComponent,\n      selectors: [[\"app-featured-brands\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 16,\n      vars: 8,\n      consts: [[1, \"featured-brands-container\"], [\"class\", \"mobile-action-buttons\", 4, \"ngIf\"], [1, \"section-header\"], [1, \"header-content\"], [1, \"section-title\"], [\"name\", \"diamond\", 1, \"title-icon\"], [1, \"section-subtitle\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [1, \"debug-info\", 2, \"background\", \"yellow\", \"padding\", \"10px\", \"margin\", \"10px 0\"], [\"class\", \"brands-slider-container\", 4, \"ngIf\"], [\"class\", \"empty-container\", 4, \"ngIf\"], [1, \"mobile-action-buttons\"], [1, \"action-btn\", \"like-btn\", 3, \"click\"], [3, \"name\"], [1, \"action-count\"], [1, \"action-btn\", \"comment-btn\", 3, \"click\"], [\"name\", \"chatbubble-outline\"], [1, \"action-btn\", \"share-btn\", 3, \"click\"], [\"name\", \"arrow-redo-outline\"], [1, \"action-text\"], [1, \"action-btn\", \"bookmark-btn\", 3, \"click\"], [1, \"action-btn\", \"music-btn\", 3, \"click\"], [\"name\", \"musical-notes\"], [1, \"loading-container\"], [1, \"loading-grid\"], [\"class\", \"loading-brand-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"loading-brand-card\"], [1, \"loading-header\"], [1, \"loading-brand-name\"], [1, \"loading-stats\"], [1, \"loading-products\"], [\"class\", \"loading-product\", 4, \"ngFor\", \"ngForOf\"], [1, \"loading-product\"], [1, \"error-container\"], [\"name\", \"alert-circle\", 1, \"error-icon\"], [1, \"error-message\"], [1, \"retry-btn\", 3, \"click\"], [\"name\", \"refresh\"], [1, \"brands-slider-container\"], [1, \"slider-nav\", \"prev-btn\", 3, \"click\", \"disabled\"], [\"name\", \"chevron-back\"], [1, \"slider-nav\", \"next-btn\", 3, \"click\", \"disabled\"], [\"name\", \"chevron-forward\"], [1, \"brands-slider-wrapper\", 3, \"mouseenter\", \"mouseleave\"], [1, \"brands-slider\"], [\"class\", \"brand-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"brand-card\", 3, \"click\"], [1, \"brand-header\"], [1, \"brand-info\"], [1, \"brand-name\"], [1, \"brand-stats\"], [1, \"stat-item\"], [\"name\", \"bag-outline\"], [\"name\", \"star\"], [\"name\", \"eye-outline\"], [1, \"brand-badge\"], [\"name\", \"diamond\"], [1, \"top-products\"], [1, \"products-title\"], [1, \"products-list\"], [\"class\", \"product-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"view-more-section\"], [1, \"view-more-btn\"], [1, \"product-item\", 3, \"click\"], [1, \"product-image-container\"], [\"loading\", \"lazy\", 1, \"product-image\", 3, \"src\", \"alt\"], [1, \"product-actions\"], [\"name\", \"share-outline\"], [1, \"product-details\"], [1, \"product-name\"], [1, \"product-price\"], [1, \"current-price\"], [\"class\", \"original-price\", 4, \"ngIf\"], [1, \"product-rating\"], [1, \"stars\"], [3, \"name\", \"filled\", 4, \"ngFor\", \"ngForOf\"], [1, \"rating-count\"], [1, \"original-price\"], [1, \"empty-container\"], [\"name\", \"diamond-outline\", 1, \"empty-icon\"], [1, \"empty-title\"], [1, \"empty-message\"]],\n      template: function FeaturedBrandsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, FeaturedBrandsComponent_div_1_Template, 19, 8, \"div\", 1);\n          i0.ɵɵelementStart(2, \"div\", 2)(3, \"div\", 3)(4, \"h2\", 4);\n          i0.ɵɵelement(5, \"ion-icon\", 5);\n          i0.ɵɵtext(6, \" Featured Brands \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"p\", 6);\n          i0.ɵɵtext(8, \"Top brands with amazing collections\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(9, FeaturedBrandsComponent_div_9_Template, 3, 2, \"div\", 7)(10, FeaturedBrandsComponent_div_10_Template, 7, 1, \"div\", 8);\n          i0.ɵɵelementStart(11, \"div\", 9)(12, \"p\");\n          i0.ɵɵtext(13);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(14, FeaturedBrandsComponent_div_14_Template, 8, 6, \"div\", 10)(15, FeaturedBrandsComponent_div_15_Template, 6, 0, \"div\", 11);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isMobile);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.error && !ctx.isLoading);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate3(\"Debug: isLoading = \", ctx.isLoading, \", error = \", ctx.error, \", featuredBrands.length = \", ctx.featuredBrands.length, \"\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.featuredBrands.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.featuredBrands.length === 0);\n        }\n      },\n      dependencies: [CommonModule, i4.NgForOf, i4.NgIf, IonicModule, i5.IonIcon, CarouselModule],\n      styles: [\".featured-brands-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  border-radius: 16px;\\n  margin-bottom: 24px;\\n  color: white;\\n  position: relative;\\n}\\n\\n.mobile-action-buttons[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 15px;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  display: flex;\\n  flex-direction: column;\\n  gap: 20px;\\n  z-index: 10;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%] {\\n  width: 48px;\\n  height: 48px;\\n  border-radius: 50%;\\n  border: none;\\n  background: rgba(255, 255, 255, 0.2);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  color: white;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  position: relative;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  margin-bottom: 2px;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   .action-count[_ngcontent-%COMP%], .mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   .action-text[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n  font-weight: 600;\\n  line-height: 1;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1);\\n  background: rgba(255, 255, 255, 0.3);\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.9);\\n  color: #667eea;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active.like-btn[_ngcontent-%COMP%] {\\n  color: #ff3040;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active.like-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_heartBeat 0.6s ease-in-out;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active.bookmark-btn[_ngcontent-%COMP%] {\\n  color: #ffd700;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.like-btn.active[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  color: #ff3040;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.music-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ff3040 0%, #667eea 100%);\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.music-btn[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1) rotate(15deg);\\n}\\n\\n@keyframes _ngcontent-%COMP%_heartBeat {\\n  0% {\\n    transform: scale(1);\\n  }\\n  25% {\\n    transform: scale(1.3);\\n  }\\n  50% {\\n    transform: scale(1.1);\\n  }\\n  75% {\\n    transform: scale(1.25);\\n  }\\n  100% {\\n    transform: scale(1);\\n  }\\n}\\n@media (min-width: 769px) {\\n  .mobile-action-buttons[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n.section-header[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n.section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 700;\\n  color: white;\\n  margin: 0 0 8px 0;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 12px;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   .title-icon[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  color: #ffd700;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-subtitle[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: rgba(255, 255, 255, 0.8);\\n  margin: 0;\\n}\\n\\n.loading-container[_ngcontent-%COMP%]   .loading-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\\n  gap: 20px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-brand-card[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 16px;\\n  padding: 20px;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-brand-card[_ngcontent-%COMP%]   .loading-header[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-brand-card[_ngcontent-%COMP%]   .loading-header[_ngcontent-%COMP%]   .loading-brand-name[_ngcontent-%COMP%] {\\n  height: 24px;\\n  background: rgba(255, 255, 255, 0.2);\\n  border-radius: 8px;\\n  margin-bottom: 8px;\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-brand-card[_ngcontent-%COMP%]   .loading-header[_ngcontent-%COMP%]   .loading-stats[_ngcontent-%COMP%] {\\n  height: 16px;\\n  background: rgba(255, 255, 255, 0.2);\\n  border-radius: 8px;\\n  width: 70%;\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-brand-card[_ngcontent-%COMP%]   .loading-products[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-brand-card[_ngcontent-%COMP%]   .loading-products[_ngcontent-%COMP%]   .loading-product[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 120px;\\n  background: rgba(255, 255, 255, 0.2);\\n  border-radius: 12px;\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_loading {\\n  0%, 100% {\\n    opacity: 0.6;\\n  }\\n  50% {\\n    opacity: 1;\\n  }\\n}\\n.error-container[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 40px 20px;\\n}\\n.error-container[_ngcontent-%COMP%]   .error-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  color: #ff6b6b;\\n  margin-bottom: 16px;\\n}\\n.error-container[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: rgba(255, 255, 255, 0.8);\\n  margin-bottom: 20px;\\n}\\n.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  color: white;\\n  border: 2px solid rgba(255, 255, 255, 0.3);\\n  padding: 12px 24px;\\n  border-radius: 8px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin: 0 auto;\\n  transition: all 0.3s ease;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3);\\n  border-color: rgba(255, 255, 255, 0.5);\\n}\\n\\n.brands-slider-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  margin: 0 -20px;\\n}\\n.brands-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  z-index: 10;\\n  background: rgba(0, 0, 0, 0.7);\\n  color: white;\\n  border: none;\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.brands-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: rgba(0, 0, 0, 0.9);\\n  transform: translateY(-50%) scale(1.1);\\n}\\n.brands-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.3;\\n  cursor: not-allowed;\\n}\\n.brands-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n}\\n.brands-slider-container[_ngcontent-%COMP%]   .slider-nav.prev-btn[_ngcontent-%COMP%] {\\n  left: -20px;\\n}\\n.brands-slider-container[_ngcontent-%COMP%]   .slider-nav.next-btn[_ngcontent-%COMP%] {\\n  right: -20px;\\n}\\n\\n.brands-slider-wrapper[_ngcontent-%COMP%] {\\n  overflow: hidden;\\n  padding: 0 20px;\\n}\\n\\n.brands-slider[_ngcontent-%COMP%] {\\n  display: flex;\\n  transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);\\n  gap: 20px;\\n}\\n.brands-slider[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%] {\\n  flex: 0 0 300px;\\n  width: 300px;\\n}\\n\\n@media (max-width: 1200px) {\\n  .brands-slider[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%] {\\n    flex: 0 0 280px;\\n    width: 280px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .brands-slider-container[_ngcontent-%COMP%] {\\n    margin: 0 -10px;\\n  }\\n  .brands-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%] {\\n    width: 35px;\\n    height: 35px;\\n  }\\n  .brands-slider-container[_ngcontent-%COMP%]   .slider-nav.prev-btn[_ngcontent-%COMP%] {\\n    left: -15px;\\n  }\\n  .brands-slider-container[_ngcontent-%COMP%]   .slider-nav.next-btn[_ngcontent-%COMP%] {\\n    right: -15px;\\n  }\\n  .brands-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n  .brands-slider-wrapper[_ngcontent-%COMP%] {\\n    padding: 0 10px;\\n  }\\n  .brands-slider[_ngcontent-%COMP%] {\\n    gap: 15px;\\n  }\\n  .brands-slider[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%] {\\n    flex: 0 0 260px;\\n    width: 260px;\\n  }\\n}\\n.brands-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\\n  gap: 20px;\\n}\\n\\n.brand-card[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 16px;\\n  padding: 20px;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n}\\n.brand-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-8px);\\n  background: rgba(255, 255, 255, 0.15);\\n  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);\\n}\\n\\n.brand-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: flex-start;\\n  margin-bottom: 20px;\\n}\\n.brand-header[_ngcontent-%COMP%]   .brand-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.brand-header[_ngcontent-%COMP%]   .brand-name[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 700;\\n  color: white;\\n  margin: 0 0 12px 0;\\n}\\n.brand-header[_ngcontent-%COMP%]   .brand-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 6px;\\n}\\n.brand-header[_ngcontent-%COMP%]   .brand-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-size: 12px;\\n  color: rgba(255, 255, 255, 0.8);\\n}\\n.brand-header[_ngcontent-%COMP%]   .brand-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #ffd700;\\n}\\n.brand-header[_ngcontent-%COMP%]   .brand-badge[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);\\n  color: #333;\\n  padding: 8px 12px;\\n  border-radius: 20px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n}\\n.brand-header[_ngcontent-%COMP%]   .brand-badge[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n}\\n\\n.top-products[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n.top-products[_ngcontent-%COMP%]   .products-title[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: white;\\n  margin: 0 0 16px 0;\\n}\\n.top-products[_ngcontent-%COMP%]   .products-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n  overflow-x: auto;\\n  padding-bottom: 8px;\\n}\\n.top-products[_ngcontent-%COMP%]   .products-list[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  height: 4px;\\n}\\n.top-products[_ngcontent-%COMP%]   .products-list[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 2px;\\n}\\n.top-products[_ngcontent-%COMP%]   .products-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: rgba(255, 255, 255, 0.3);\\n  border-radius: 2px;\\n}\\n\\n.product-item[_ngcontent-%COMP%] {\\n  flex: 0 0 140px;\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 12px;\\n  overflow: hidden;\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n}\\n.product-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  background: rgba(255, 255, 255, 0.15);\\n}\\n.product-item[_ngcontent-%COMP%]:hover   .product-actions[_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n\\n.product-image-container[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100px;\\n  object-fit: cover;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 8px;\\n  right: 8px;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 4px;\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%] {\\n  width: 28px;\\n  height: 28px;\\n  border-radius: 50%;\\n  border: none;\\n  background: rgba(255, 255, 255, 0.9);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #333;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:hover {\\n  background: white;\\n  transform: scale(1.1);\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn.like-btn[_ngcontent-%COMP%]:hover   ion-icon[_ngcontent-%COMP%] {\\n  color: #dc3545;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn.like-btn.liked[_ngcontent-%COMP%] {\\n  background: rgba(220, 53, 69, 0.2);\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn.like-btn.liked[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  color: #dc3545;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn.share-btn[_ngcontent-%COMP%]:hover   ion-icon[_ngcontent-%COMP%] {\\n  color: #007bff;\\n}\\n\\n.product-details[_ngcontent-%COMP%] {\\n  padding: 12px;\\n}\\n.product-details[_ngcontent-%COMP%]   .product-name[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  font-weight: 600;\\n  color: white;\\n  margin: 0 0 8px 0;\\n  line-height: 1.3;\\n  display: -webkit-box;\\n  -webkit-line-clamp: 2;\\n  -webkit-box-orient: vertical;\\n  overflow: hidden;\\n}\\n.product-details[_ngcontent-%COMP%]   .product-price[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  margin-bottom: 8px;\\n}\\n.product-details[_ngcontent-%COMP%]   .product-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 700;\\n  color: #ffd700;\\n}\\n.product-details[_ngcontent-%COMP%]   .product-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n  color: rgba(255, 255, 255, 0.6);\\n  text-decoration: line-through;\\n}\\n.product-details[_ngcontent-%COMP%]   .product-rating[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n}\\n.product-details[_ngcontent-%COMP%]   .product-rating[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1px;\\n}\\n.product-details[_ngcontent-%COMP%]   .product-rating[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n  color: rgba(255, 255, 255, 0.3);\\n}\\n.product-details[_ngcontent-%COMP%]   .product-rating[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   ion-icon.filled[_ngcontent-%COMP%] {\\n  color: #ffd700;\\n}\\n.product-details[_ngcontent-%COMP%]   .product-rating[_ngcontent-%COMP%]   .rating-count[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n  color: rgba(255, 255, 255, 0.6);\\n}\\n\\n.view-more-section[_ngcontent-%COMP%]   .view-more-btn[_ngcontent-%COMP%] {\\n  width: 100%;\\n  background: rgba(255, 255, 255, 0.1);\\n  border: 2px solid rgba(255, 255, 255, 0.2);\\n  color: white;\\n  padding: 12px 16px;\\n  border-radius: 8px;\\n  font-weight: 600;\\n  font-size: 14px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 8px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.view-more-section[_ngcontent-%COMP%]   .view-more-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.2);\\n  border-color: rgba(255, 255, 255, 0.4);\\n}\\n.view-more-section[_ngcontent-%COMP%]   .view-more-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n\\n.empty-container[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 60px 20px;\\n}\\n.empty-container[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%] {\\n  font-size: 64px;\\n  color: rgba(255, 255, 255, 0.4);\\n  margin-bottom: 20px;\\n}\\n.empty-container[_ngcontent-%COMP%]   .empty-title[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: white;\\n  margin-bottom: 8px;\\n}\\n.empty-container[_ngcontent-%COMP%]   .empty-message[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: rgba(255, 255, 255, 0.7);\\n}\\n\\n@media (max-width: 768px) {\\n  .featured-brands-container[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .brands-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 16px;\\n  }\\n  .brand-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 12px;\\n  }\\n  .brand-header[_ngcontent-%COMP%]   .brand-badge[_ngcontent-%COMP%] {\\n    align-self: flex-start;\\n  }\\n  .brand-stats[_ngcontent-%COMP%] {\\n    flex-direction: row !important;\\n    flex-wrap: wrap;\\n    gap: 12px !important;\\n  }\\n  .section-title[_ngcontent-%COMP%] {\\n    font-size: 20px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "Subscription", "IonicModule", "CarouselModule", "i0", "ɵɵelementStart", "ɵɵlistener", "FeaturedBrandsComponent_div_1_Template_button_click_1_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "toggleSectionLike", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "FeaturedBrandsComponent_div_1_Template_button_click_5_listener", "openComments", "FeaturedBrandsComponent_div_1_Template_button_click_9_listener", "shareSection", "FeaturedBrandsComponent_div_1_Template_button_click_13_listener", "toggleSectionBookmark", "FeaturedBrandsComponent_div_1_Template_button_click_15_listener", "openMusicPlayer", "ɵɵadvance", "ɵɵclassProp", "isSectionLiked", "ɵɵproperty", "ɵɵtextInterpolate", "formatCount", "sectionLikes", "sectionComments", "isSectionBookmarked", "ɵɵtemplate", "FeaturedBrandsComponent_div_9_div_2_div_5_Template", "ɵɵpureFunction0", "_c1", "FeaturedBrandsComponent_div_9_div_2_Template", "_c0", "FeaturedBrandsComponent_div_10_Template_button_click_4_listener", "_r3", "onRetry", "error", "formatPrice", "product_r8", "originalPrice", "star_r9", "rating", "average", "FeaturedBrandsComponent_div_14_div_7_div_25_Template_div_click_0_listener", "$event", "_r7", "$implicit", "onProductClick", "FeaturedBrandsComponent_div_14_div_7_div_25_Template_button_click_4_listener", "onLikeProduct", "FeaturedBrandsComponent_div_14_div_7_div_25_Template_button_click_6_listener", "onShareProduct", "FeaturedBrandsComponent_div_14_div_7_div_25_span_14_Template", "FeaturedBrandsComponent_div_14_div_7_div_25_ion_icon_17_Template", "images", "url", "ɵɵsanitizeUrl", "alt", "name", "isProductLiked", "_id", "price", "_c2", "ɵɵtextInterpolate1", "count", "FeaturedBrandsComponent_div_14_div_7_Template_div_click_0_listener", "brand_r6", "_r5", "onBrandClick", "FeaturedBrandsComponent_div_14_div_7_div_25_Template", "brand", "productCount", "avgRating", "formatNumber", "totalViews", "topProducts", "trackByProductId", "FeaturedBrandsComponent_div_14_Template_button_click_1_listener", "_r4", "slidePrev", "FeaturedBrandsComponent_div_14_Template_button_click_3_listener", "slideNext", "FeaturedBrandsComponent_div_14_Template_div_mouseenter_5_listener", "pauseAutoSlide", "FeaturedBrandsComponent_div_14_Template_div_mouseleave_5_listener", "resumeAutoSlide", "FeaturedBrandsComponent_div_14_div_7_Template", "currentSlide", "maxSlide", "ɵɵstyleProp", "slideOffset", "featuredB<PERSON>s", "trackByBrandName", "FeaturedBrandsComponent", "constructor", "trendingService", "socialService", "router", "isLoading", "likedProducts", "Set", "subscription", "<PERSON><PERSON><PERSON><PERSON>", "visibleCards", "autoSlideDelay", "isAutoSliding", "isPaused", "isMobile", "ngOnInit", "console", "log", "loadFeaturedBrands", "subscribeFeaturedBrands", "subscribeLikedProducts", "updateResponsiveSettings", "setupResizeListener", "checkMobileDevice", "ngOnDestroy", "unsubscribe", "stopAutoSlide", "add", "featuredBrands$", "subscribe", "brands", "updateSliderOnBrandsLoad", "likedProducts$", "_this", "_asyncToGenerator", "navigate", "queryParams", "product", "event", "stopPropagation", "_this2", "result", "likeProduct", "success", "message", "_this3", "productUrl", "window", "location", "origin", "navigator", "clipboard", "writeText", "shareProduct", "platform", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "format", "num", "toFixed", "toString", "index", "productId", "has", "startAutoSlide", "autoSlideInterval", "setInterval", "length", "autoSlideNext", "clearInterval", "updateSlideOffset", "width", "innerWidth", "updateSliderLimits", "addEventListener", "Math", "max", "restartAutoSlideAfterInteraction", "setTimeout", "share", "title", "text", "href", "ɵɵdirectiveInject", "i1", "TrendingService", "i2", "SocialInteractionsService", "i3", "Router", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "FeaturedBrandsComponent_Template", "rf", "ctx", "FeaturedBrandsComponent_div_1_Template", "FeaturedBrandsComponent_div_9_Template", "FeaturedBrandsComponent_div_10_Template", "FeaturedBrandsComponent_div_14_Template", "FeaturedBrandsComponent_div_15_Template", "ɵɵtextInterpolate3", "i4", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i5", "IonIcon", "styles"], "sources": ["E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\features\\home\\components\\featured-brands\\featured-brands.component.ts", "E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\features\\home\\components\\featured-brands\\featured-brands.component.html"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { Router } from '@angular/router';\r\nimport { Subscription } from 'rxjs';\r\nimport { TrendingService, FeaturedBrand } from '../../../../core/services/trending.service';\r\nimport { Product } from '../../../../core/models/product.model';\r\nimport { SocialInteractionsService } from '../../../../core/services/social-interactions.service';\r\nimport { IonicModule } from '@ionic/angular';\r\nimport { CarouselModule } from 'ngx-owl-carousel-o';\r\n\r\n@Component({\r\n  selector: 'app-featured-brands',\r\n  standalone: true,\r\n  imports: [CommonModule, IonicModule, CarouselModule],\r\n  templateUrl: './featured-brands.component.html',\r\n  styleUrls: ['./featured-brands.component.scss']\r\n})\r\nexport class FeaturedBrandsComponent implements OnInit, OnDestroy {\r\n  featuredBrands: FeaturedBrand[] = [];\r\n  isLoading = true;\r\n  error: string | null = null;\r\n  likedProducts = new Set<string>();\r\n  private subscription: Subscription = new Subscription();\r\n\r\n  // Slider properties\r\n  currentSlide = 0;\r\n  slideOffset = 0;\r\n  cardWidth = 320; // Width of each brand card including margin\r\n  visibleCards = 3; // Number of cards visible at once\r\n  maxSlide = 0;\r\n\r\n  // Auto-sliding properties\r\n  autoSlideInterval: any;\r\n  autoSlideDelay = 4000; // 4 seconds for brands\r\n  isAutoSliding = true;\r\n  isPaused = false;\r\n\r\n  // Section interaction properties\r\n  isSectionLiked = false;\r\n  isSectionBookmarked = false;\r\n  sectionLikes = 287;\r\n  sectionComments = 89;\r\n  isMobile = false;\r\n\r\n  constructor(\r\n    private trendingService: TrendingService,\r\n    private socialService: SocialInteractionsService,\r\n    private router: Router\r\n  ) {}\r\n\r\n  ngOnInit() {\r\n    console.log('FeaturedBrandsComponent initialized');\r\n    this.loadFeaturedBrands();\r\n    this.subscribeFeaturedBrands();\r\n    this.subscribeLikedProducts();\r\n    this.updateResponsiveSettings();\r\n    this.setupResizeListener();\r\n    this.checkMobileDevice();\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.subscription.unsubscribe();\r\n    this.stopAutoSlide();\r\n  }\r\n\r\n  private subscribeFeaturedBrands() {\r\n    this.subscription.add(\r\n      this.trendingService.featuredBrands$.subscribe(brands => {\r\n        console.log('FeaturedBrandsComponent received brands:', brands);\r\n        this.featuredBrands = brands;\r\n        this.isLoading = false;\r\n        this.updateSliderOnBrandsLoad();\r\n      })\r\n    );\r\n  }\r\n\r\n  private subscribeLikedProducts() {\r\n    this.subscription.add(\r\n      this.socialService.likedProducts$.subscribe(likedProducts => {\r\n        this.likedProducts = likedProducts;\r\n      })\r\n    );\r\n  }\r\n\r\n  private async loadFeaturedBrands() {\r\n    try {\r\n      this.isLoading = true;\r\n      this.error = null;\r\n      await this.trendingService.loadFeaturedBrands();\r\n    } catch (error) {\r\n      console.error('Error loading featured brands:', error);\r\n      this.error = 'Failed to load featured brands';\r\n      this.isLoading = false;\r\n    }\r\n  }\r\n\r\n  onBrandClick(brand: FeaturedBrand) {\r\n    this.router.navigate(['/products'], { \r\n      queryParams: { brand: brand.brand } \r\n    });\r\n  }\r\n\r\n  onProductClick(product: Product, event: Event) {\r\n    event.stopPropagation();\r\n    this.router.navigate(['/product', product._id]);\r\n  }\r\n\r\n  async onLikeProduct(product: Product, event: Event) {\r\n    event.stopPropagation();\r\n    try {\r\n      const result = await this.socialService.likeProduct(product._id);\r\n      if (result.success) {\r\n        console.log(result.message);\r\n      } else {\r\n        console.error('Failed to like product:', result.message);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error liking product:', error);\r\n    }\r\n  }\r\n\r\n  async onShareProduct(product: Product, event: Event) {\r\n    event.stopPropagation();\r\n    try {\r\n      const productUrl = `${window.location.origin}/product/${product._id}`;\r\n      await navigator.clipboard.writeText(productUrl);\r\n\r\n      await this.socialService.shareProduct(product._id, {\r\n        platform: 'copy_link',\r\n        message: `Check out this amazing ${product.name} from ${product.brand}!`\r\n      });\r\n\r\n      console.log('Product link copied to clipboard!');\r\n    } catch (error) {\r\n      console.error('Error sharing product:', error);\r\n    }\r\n  }\r\n\r\n  formatPrice(price: number): string {\r\n    return new Intl.NumberFormat('en-IN', {\r\n      style: 'currency',\r\n      currency: 'INR',\r\n      minimumFractionDigits: 0\r\n    }).format(price);\r\n  }\r\n\r\n  formatNumber(num: number): string {\r\n    if (num >= 1000000) {\r\n      return (num / 1000000).toFixed(1) + 'M';\r\n    } else if (num >= 1000) {\r\n      return (num / 1000).toFixed(1) + 'K';\r\n    }\r\n    return num.toString();\r\n  }\r\n\r\n  onRetry() {\r\n    this.loadFeaturedBrands();\r\n  }\r\n\r\n  trackByBrandName(index: number, brand: FeaturedBrand): string {\r\n    return brand.brand;\r\n  }\r\n\r\n  isProductLiked(productId: string): boolean {\r\n    return this.likedProducts.has(productId);\r\n  }\r\n\r\n  trackByProductId(index: number, product: Product): string {\r\n    return product._id;\r\n  }\r\n\r\n  // Auto-sliding methods\r\n  private startAutoSlide() {\r\n    if (!this.isAutoSliding || this.isPaused) return;\r\n\r\n    this.stopAutoSlide();\r\n    this.autoSlideInterval = setInterval(() => {\r\n      if (!this.isPaused && this.featuredBrands.length > this.visibleCards) {\r\n        this.autoSlideNext();\r\n      }\r\n    }, this.autoSlideDelay);\r\n  }\r\n\r\n  private stopAutoSlide() {\r\n    if (this.autoSlideInterval) {\r\n      clearInterval(this.autoSlideInterval);\r\n      this.autoSlideInterval = null;\r\n    }\r\n  }\r\n\r\n  private autoSlideNext() {\r\n    if (this.currentSlide >= this.maxSlide) {\r\n      this.currentSlide = 0;\r\n    } else {\r\n      this.currentSlide++;\r\n    }\r\n    this.updateSlideOffset();\r\n  }\r\n\r\n  pauseAutoSlide() {\r\n    this.isPaused = true;\r\n    this.stopAutoSlide();\r\n  }\r\n\r\n  resumeAutoSlide() {\r\n    this.isPaused = false;\r\n    this.startAutoSlide();\r\n  }\r\n\r\n  // Responsive methods\r\n  private updateResponsiveSettings() {\r\n    const width = window.innerWidth;\r\n    if (width <= 768) {\r\n      this.cardWidth = 280;\r\n      this.visibleCards = 1;\r\n    } else if (width <= 1200) {\r\n      this.cardWidth = 320;\r\n      this.visibleCards = 2;\r\n    } else {\r\n      this.cardWidth = 340;\r\n      this.visibleCards = 3;\r\n    }\r\n    this.updateSliderLimits();\r\n    this.updateSlideOffset();\r\n  }\r\n\r\n  private setupResizeListener() {\r\n    window.addEventListener('resize', () => {\r\n      this.updateResponsiveSettings();\r\n    });\r\n  }\r\n\r\n  // Slider methods\r\n  updateSliderLimits() {\r\n    this.maxSlide = Math.max(0, this.featuredBrands.length - this.visibleCards);\r\n  }\r\n\r\n  slidePrev() {\r\n    if (this.currentSlide > 0) {\r\n      this.currentSlide--;\r\n      this.updateSlideOffset();\r\n      this.restartAutoSlideAfterInteraction();\r\n    }\r\n  }\r\n\r\n  slideNext() {\r\n    if (this.currentSlide < this.maxSlide) {\r\n      this.currentSlide++;\r\n      this.updateSlideOffset();\r\n      this.restartAutoSlideAfterInteraction();\r\n    }\r\n  }\r\n\r\n  private updateSlideOffset() {\r\n    this.slideOffset = -this.currentSlide * this.cardWidth;\r\n  }\r\n\r\n  private restartAutoSlideAfterInteraction() {\r\n    this.stopAutoSlide();\r\n    setTimeout(() => {\r\n      this.startAutoSlide();\r\n    }, 2000);\r\n  }\r\n\r\n  // Update slider when brands load\r\n  private updateSliderOnBrandsLoad() {\r\n    setTimeout(() => {\r\n      this.updateSliderLimits();\r\n      this.currentSlide = 0;\r\n      this.slideOffset = 0;\r\n      this.startAutoSlide();\r\n    }, 100);\r\n  }\r\n\r\n  // Section interaction methods\r\n  toggleSectionLike() {\r\n    this.isSectionLiked = !this.isSectionLiked;\r\n    if (this.isSectionLiked) {\r\n      this.sectionLikes++;\r\n    } else {\r\n      this.sectionLikes--;\r\n    }\r\n  }\r\n\r\n  toggleSectionBookmark() {\r\n    this.isSectionBookmarked = !this.isSectionBookmarked;\r\n  }\r\n\r\n  openComments() {\r\n    console.log('Opening comments for featured brands section');\r\n  }\r\n\r\n  shareSection() {\r\n    if (navigator.share) {\r\n      navigator.share({\r\n        title: 'Featured Brands',\r\n        text: 'Check out these amazing featured fashion brands!',\r\n        url: window.location.href\r\n      });\r\n    } else {\r\n      navigator.clipboard.writeText(window.location.href);\r\n      console.log('Link copied to clipboard');\r\n    }\r\n  }\r\n\r\n  openMusicPlayer() {\r\n    console.log('Opening music player for featured brands');\r\n  }\r\n\r\n  formatCount(count: number): string {\r\n    if (count >= 1000000) {\r\n      return (count / 1000000).toFixed(1) + 'M';\r\n    } else if (count >= 1000) {\r\n      return (count / 1000).toFixed(1) + 'K';\r\n    }\r\n    return count.toString();\r\n  }\r\n\r\n  private checkMobileDevice() {\r\n    this.isMobile = window.innerWidth <= 768;\r\n  }\r\n}\r\n", "<div class=\"featured-brands-container\">\r\n  <!-- Mobile Action Buttons (TikTok/Instagram Style) -->\r\n  <div class=\"mobile-action-buttons\" *ngIf=\"isMobile\">\r\n    <button class=\"action-btn like-btn\"\r\n            [class.active]=\"isSectionLiked\"\r\n            (click)=\"toggleSectionLike()\">\r\n      <ion-icon [name]=\"isSectionLiked ? 'heart' : 'heart-outline'\"></ion-icon>\r\n      <span class=\"action-count\">{{ formatCount(sectionLikes) }}</span>\r\n    </button>\r\n\r\n    <button class=\"action-btn comment-btn\" (click)=\"openComments()\">\r\n      <ion-icon name=\"chatbubble-outline\"></ion-icon>\r\n      <span class=\"action-count\">{{ formatCount(sectionComments) }}</span>\r\n    </button>\r\n\r\n    <button class=\"action-btn share-btn\" (click)=\"shareSection()\">\r\n      <ion-icon name=\"arrow-redo-outline\"></ion-icon>\r\n      <span class=\"action-text\">Share</span>\r\n    </button>\r\n\r\n    <button class=\"action-btn bookmark-btn\"\r\n            [class.active]=\"isSectionBookmarked\"\r\n            (click)=\"toggleSectionBookmark()\">\r\n      <ion-icon [name]=\"isSectionBookmarked ? 'bookmark' : 'bookmark-outline'\"></ion-icon>\r\n    </button>\r\n\r\n    <button class=\"action-btn music-btn\" (click)=\"openMusicPlayer()\">\r\n      <ion-icon name=\"musical-notes\"></ion-icon>\r\n      <span class=\"action-text\">Music</span>\r\n    </button>\r\n  </div>\r\n\r\n  <!-- Header -->\r\n  <div class=\"section-header\">\r\n    <div class=\"header-content\">\r\n      <h2 class=\"section-title\">\r\n        <ion-icon name=\"diamond\" class=\"title-icon\"></ion-icon>\r\n        Featured Brands\r\n      </h2>\r\n      <p class=\"section-subtitle\">Top brands with amazing collections</p>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Loading State -->\r\n  <div *ngIf=\"isLoading\" class=\"loading-container\">\r\n    <div class=\"loading-grid\">\r\n      <div *ngFor=\"let item of [1,2,3,4]\" class=\"loading-brand-card\">\r\n        <div class=\"loading-header\">\r\n          <div class=\"loading-brand-name\"></div>\r\n          <div class=\"loading-stats\"></div>\r\n        </div>\r\n        <div class=\"loading-products\">\r\n          <div *ngFor=\"let prod of [1,2,3]\" class=\"loading-product\"></div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Error State -->\r\n  <div *ngIf=\"error && !isLoading\" class=\"error-container\">\r\n    <ion-icon name=\"alert-circle\" class=\"error-icon\"></ion-icon>\r\n    <p class=\"error-message\">{{ error }}</p>\r\n    <button class=\"retry-btn\" (click)=\"onRetry()\">\r\n      <ion-icon name=\"refresh\"></ion-icon>\r\n      Try Again\r\n    </button>\r\n  </div>\r\n\r\n  <!-- Debug Info -->\r\n  <div class=\"debug-info\" style=\"background: yellow; padding: 10px; margin: 10px 0;\">\r\n    <p>Debug: isLoading = {{isLoading}}, error = {{error}}, featuredBrands.length = {{featuredBrands.length}}</p>\r\n  </div>\r\n\r\n  <!-- Brands Slider -->\r\n  <div *ngIf=\"!isLoading && !error && featuredBrands.length > 0\" class=\"brands-slider-container\">\r\n    <!-- Navigation Buttons -->\r\n    <button class=\"slider-nav prev-btn\" (click)=\"slidePrev()\" [disabled]=\"currentSlide === 0\">\r\n      <ion-icon name=\"chevron-back\"></ion-icon>\r\n    </button>\r\n    <button class=\"slider-nav next-btn\" (click)=\"slideNext()\" [disabled]=\"currentSlide >= maxSlide\">\r\n      <ion-icon name=\"chevron-forward\"></ion-icon>\r\n    </button>\r\n\r\n    <!-- Slider Wrapper -->\r\n    <div class=\"brands-slider-wrapper\" (mouseenter)=\"pauseAutoSlide()\" (mouseleave)=\"resumeAutoSlide()\">\r\n      <div class=\"brands-slider\" [style.transform]=\"'translateX(' + slideOffset + 'px)'\">\r\n    <div \r\n      *ngFor=\"let brand of featuredBrands; trackBy: trackByBrandName\" \r\n      class=\"brand-card\"\r\n      (click)=\"onBrandClick(brand)\"\r\n    >\r\n      <!-- Brand Header -->\r\n      <div class=\"brand-header\">\r\n        <div class=\"brand-info\">\r\n          <h3 class=\"brand-name\">{{ brand.brand }}</h3>\r\n          <div class=\"brand-stats\">\r\n            <div class=\"stat-item\">\r\n              <ion-icon name=\"bag-outline\"></ion-icon>\r\n              <span>{{ brand.productCount }} Products</span>\r\n            </div>\r\n            <div class=\"stat-item\">\r\n              <ion-icon name=\"star\"></ion-icon>\r\n              <span>{{ brand.avgRating }}/5</span>\r\n            </div>\r\n            <div class=\"stat-item\">\r\n              <ion-icon name=\"eye-outline\"></ion-icon>\r\n              <span>{{ formatNumber(brand.totalViews) }} Views</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"brand-badge\">\r\n          <ion-icon name=\"diamond\"></ion-icon>\r\n          Featured\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Top Products -->\r\n      <div class=\"top-products\">\r\n        <h4 class=\"products-title\">Top Products</h4>\r\n        <div class=\"products-list\">\r\n          <div \r\n            *ngFor=\"let product of brand.topProducts; trackBy: trackByProductId\" \r\n            class=\"product-item\"\r\n            (click)=\"onProductClick(product, $event)\"\r\n          >\r\n            <div class=\"product-image-container\">\r\n              <img \r\n                [src]=\"product.images[0].url\"\r\n                [alt]=\"product.images[0].alt || product.name\"\r\n                class=\"product-image\"\r\n                loading=\"lazy\"\r\n              />\r\n              \r\n              <!-- Action Buttons -->\r\n              <div class=\"product-actions\">\r\n                <button\r\n                  class=\"action-btn like-btn\"\r\n                  [class.liked]=\"isProductLiked(product._id)\"\r\n                  (click)=\"onLikeProduct(product, $event)\"\r\n                  [attr.aria-label]=\"'Like ' + product.name\"\r\n                >\r\n                  <ion-icon [name]=\"isProductLiked(product._id) ? 'heart' : 'heart-outline'\"></ion-icon>\r\n                </button>\r\n                <button \r\n                  class=\"action-btn share-btn\" \r\n                  (click)=\"onShareProduct(product, $event)\"\r\n                  [attr.aria-label]=\"'Share ' + product.name\"\r\n                >\r\n                  <ion-icon name=\"share-outline\"></ion-icon>\r\n                </button>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"product-details\">\r\n              <h5 class=\"product-name\">{{ product.name }}</h5>\r\n              <div class=\"product-price\">\r\n                <span class=\"current-price\">{{ formatPrice(product.price) }}</span>\r\n                <span *ngIf=\"product.originalPrice && product.originalPrice > product.price\" \r\n                      class=\"original-price\">{{ formatPrice(product.originalPrice) }}</span>\r\n              </div>\r\n              <div class=\"product-rating\">\r\n                <div class=\"stars\">\r\n                  <ion-icon \r\n                    *ngFor=\"let star of [1,2,3,4,5]\" \r\n                    [name]=\"star <= product.rating.average ? 'star' : 'star-outline'\"\r\n                    [class.filled]=\"star <= product.rating.average\"\r\n                  ></ion-icon>\r\n                </div>\r\n                <span class=\"rating-count\">({{ product.rating.count }})</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- View More Button -->\r\n      <div class=\"view-more-section\">\r\n        <button class=\"view-more-btn\">\r\n          <span>View All {{ brand.brand }} Products</span>\r\n          <ion-icon name=\"chevron-forward\"></ion-icon>\r\n        </button>\r\n      </div>\r\n    </div>\r\n    </div> <!-- End brands-slider -->\r\n    </div> <!-- End brands-slider-wrapper -->\r\n  </div> <!-- End brands-slider-container -->\r\n\r\n  <!-- Empty State -->\r\n  <div *ngIf=\"!isLoading && !error && featuredBrands.length === 0\" class=\"empty-container\">\r\n    <ion-icon name=\"diamond-outline\" class=\"empty-icon\"></ion-icon>\r\n    <h3 class=\"empty-title\">No Featured Brands</h3>\r\n    <p class=\"empty-message\">Check back later for featured brand collections</p>\r\n  </div>\r\n</div>\r\n"], "mappings": ";AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,YAAY,QAAQ,MAAM;AAInC,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,cAAc,QAAQ,oBAAoB;;;;;;;;;;;;;ICL/CC,EADF,CAAAC,cAAA,cAAoD,iBAGZ;IAA9BD,EAAA,CAAAE,UAAA,mBAAAC,+DAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,iBAAA,EAAmB;IAAA,EAAC;IACnCT,EAAA,CAAAU,SAAA,mBAAyE;IACzEV,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAW,MAAA,GAA+B;IAC5DX,EAD4D,CAAAY,YAAA,EAAO,EAC1D;IAETZ,EAAA,CAAAC,cAAA,iBAAgE;IAAzBD,EAAA,CAAAE,UAAA,mBAAAW,+DAAA;MAAAb,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAQ,YAAA,EAAc;IAAA,EAAC;IAC7Dd,EAAA,CAAAU,SAAA,mBAA+C;IAC/CV,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAW,MAAA,GAAkC;IAC/DX,EAD+D,CAAAY,YAAA,EAAO,EAC7D;IAETZ,EAAA,CAAAC,cAAA,iBAA8D;IAAzBD,EAAA,CAAAE,UAAA,mBAAAa,+DAAA;MAAAf,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAU,YAAA,EAAc;IAAA,EAAC;IAC3DhB,EAAA,CAAAU,SAAA,oBAA+C;IAC/CV,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAW,MAAA,aAAK;IACjCX,EADiC,CAAAY,YAAA,EAAO,EAC/B;IAETZ,EAAA,CAAAC,cAAA,kBAE0C;IAAlCD,EAAA,CAAAE,UAAA,mBAAAe,gEAAA;MAAAjB,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAY,qBAAA,EAAuB;IAAA,EAAC;IACvClB,EAAA,CAAAU,SAAA,oBAAoF;IACtFV,EAAA,CAAAY,YAAA,EAAS;IAETZ,EAAA,CAAAC,cAAA,kBAAiE;IAA5BD,EAAA,CAAAE,UAAA,mBAAAiB,gEAAA;MAAAnB,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAc,eAAA,EAAiB;IAAA,EAAC;IAC9DpB,EAAA,CAAAU,SAAA,oBAA0C;IAC1CV,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAW,MAAA,aAAK;IAEnCX,EAFmC,CAAAY,YAAA,EAAO,EAC/B,EACL;;;;IA1BIZ,EAAA,CAAAqB,SAAA,EAA+B;IAA/BrB,EAAA,CAAAsB,WAAA,WAAAhB,MAAA,CAAAiB,cAAA,CAA+B;IAE3BvB,EAAA,CAAAqB,SAAA,EAAmD;IAAnDrB,EAAA,CAAAwB,UAAA,SAAAlB,MAAA,CAAAiB,cAAA,6BAAmD;IAClCvB,EAAA,CAAAqB,SAAA,GAA+B;IAA/BrB,EAAA,CAAAyB,iBAAA,CAAAnB,MAAA,CAAAoB,WAAA,CAAApB,MAAA,CAAAqB,YAAA,EAA+B;IAK/B3B,EAAA,CAAAqB,SAAA,GAAkC;IAAlCrB,EAAA,CAAAyB,iBAAA,CAAAnB,MAAA,CAAAoB,WAAA,CAAApB,MAAA,CAAAsB,eAAA,EAAkC;IASvD5B,EAAA,CAAAqB,SAAA,GAAoC;IAApCrB,EAAA,CAAAsB,WAAA,WAAAhB,MAAA,CAAAuB,mBAAA,CAAoC;IAEhC7B,EAAA,CAAAqB,SAAA,EAA8D;IAA9DrB,EAAA,CAAAwB,UAAA,SAAAlB,MAAA,CAAAuB,mBAAA,mCAA8D;;;;;IA6BpE7B,EAAA,CAAAU,SAAA,cAAgE;;;;;IALlEV,EADF,CAAAC,cAAA,cAA+D,cACjC;IAE1BD,EADA,CAAAU,SAAA,cAAsC,cACL;IACnCV,EAAA,CAAAY,YAAA,EAAM;IACNZ,EAAA,CAAAC,cAAA,cAA8B;IAC5BD,EAAA,CAAA8B,UAAA,IAAAC,kDAAA,kBAA0D;IAE9D/B,EADE,CAAAY,YAAA,EAAM,EACF;;;IAFoBZ,EAAA,CAAAqB,SAAA,GAAU;IAAVrB,EAAA,CAAAwB,UAAA,YAAAxB,EAAA,CAAAgC,eAAA,IAAAC,GAAA,EAAU;;;;;IAPtCjC,EADF,CAAAC,cAAA,cAAiD,cACrB;IACxBD,EAAA,CAAA8B,UAAA,IAAAI,4CAAA,kBAA+D;IAUnElC,EADE,CAAAY,YAAA,EAAM,EACF;;;IAVoBZ,EAAA,CAAAqB,SAAA,GAAY;IAAZrB,EAAA,CAAAwB,UAAA,YAAAxB,EAAA,CAAAgC,eAAA,IAAAG,GAAA,EAAY;;;;;;IAatCnC,EAAA,CAAAC,cAAA,cAAyD;IACvDD,EAAA,CAAAU,SAAA,mBAA4D;IAC5DV,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAW,MAAA,GAAW;IAAAX,EAAA,CAAAY,YAAA,EAAI;IACxCZ,EAAA,CAAAC,cAAA,iBAA8C;IAApBD,EAAA,CAAAE,UAAA,mBAAAkC,gEAAA;MAAApC,EAAA,CAAAI,aAAA,CAAAiC,GAAA;MAAA,MAAA/B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAgC,OAAA,EAAS;IAAA,EAAC;IAC3CtC,EAAA,CAAAU,SAAA,mBAAoC;IACpCV,EAAA,CAAAW,MAAA,kBACF;IACFX,EADE,CAAAY,YAAA,EAAS,EACL;;;;IALqBZ,EAAA,CAAAqB,SAAA,GAAW;IAAXrB,EAAA,CAAAyB,iBAAA,CAAAnB,MAAA,CAAAiC,KAAA,CAAW;;;;;IAgGxBvC,EAAA,CAAAC,cAAA,eAC6B;IAAAD,EAAA,CAAAW,MAAA,GAAwC;IAAAX,EAAA,CAAAY,YAAA,EAAO;;;;;IAA/CZ,EAAA,CAAAqB,SAAA,EAAwC;IAAxCrB,EAAA,CAAAyB,iBAAA,CAAAnB,MAAA,CAAAkC,WAAA,CAAAC,UAAA,CAAAC,aAAA,EAAwC;;;;;IAInE1C,EAAA,CAAAU,SAAA,mBAIY;;;;;IADVV,EAAA,CAAAsB,WAAA,WAAAqB,OAAA,IAAAF,UAAA,CAAAG,MAAA,CAAAC,OAAA,CAA+C;IAD/C7C,EAAA,CAAAwB,UAAA,SAAAmB,OAAA,IAAAF,UAAA,CAAAG,MAAA,CAAAC,OAAA,2BAAiE;;;;;;IA5C3E7C,EAAA,CAAAC,cAAA,cAIC;IADCD,EAAA,CAAAE,UAAA,mBAAA4C,0EAAAC,MAAA;MAAA,MAAAN,UAAA,GAAAzC,EAAA,CAAAI,aAAA,CAAA4C,GAAA,EAAAC,SAAA;MAAA,MAAA3C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA4C,cAAA,CAAAT,UAAA,EAAAM,MAAA,CAA+B;IAAA,EAAC;IAEzC/C,EAAA,CAAAC,cAAA,cAAqC;IACnCD,EAAA,CAAAU,SAAA,cAKE;IAIAV,EADF,CAAAC,cAAA,cAA6B,iBAM1B;IAFCD,EAAA,CAAAE,UAAA,mBAAAiD,6EAAAJ,MAAA;MAAA,MAAAN,UAAA,GAAAzC,EAAA,CAAAI,aAAA,CAAA4C,GAAA,EAAAC,SAAA;MAAA,MAAA3C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA8C,aAAA,CAAAX,UAAA,EAAAM,MAAA,CAA8B;IAAA,EAAC;IAGxC/C,EAAA,CAAAU,SAAA,mBAAsF;IACxFV,EAAA,CAAAY,YAAA,EAAS;IACTZ,EAAA,CAAAC,cAAA,iBAIC;IAFCD,EAAA,CAAAE,UAAA,mBAAAmD,6EAAAN,MAAA;MAAA,MAAAN,UAAA,GAAAzC,EAAA,CAAAI,aAAA,CAAA4C,GAAA,EAAAC,SAAA;MAAA,MAAA3C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAgD,cAAA,CAAAb,UAAA,EAAAM,MAAA,CAA+B;IAAA,EAAC;IAGzC/C,EAAA,CAAAU,SAAA,mBAA0C;IAGhDV,EAFI,CAAAY,YAAA,EAAS,EACL,EACF;IAGJZ,EADF,CAAAC,cAAA,cAA6B,aACF;IAAAD,EAAA,CAAAW,MAAA,IAAkB;IAAAX,EAAA,CAAAY,YAAA,EAAK;IAE9CZ,EADF,CAAAC,cAAA,eAA2B,gBACG;IAAAD,EAAA,CAAAW,MAAA,IAAgC;IAAAX,EAAA,CAAAY,YAAA,EAAO;IACnEZ,EAAA,CAAA8B,UAAA,KAAAyB,4DAAA,mBAC6B;IAC/BvD,EAAA,CAAAY,YAAA,EAAM;IAEJZ,EADF,CAAAC,cAAA,eAA4B,eACP;IACjBD,EAAA,CAAA8B,UAAA,KAAA0B,gEAAA,uBAIC;IACHxD,EAAA,CAAAY,YAAA,EAAM;IACNZ,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAW,MAAA,IAA4B;IAG7DX,EAH6D,CAAAY,YAAA,EAAO,EAC1D,EACF,EACF;;;;;IA5CAZ,EAAA,CAAAqB,SAAA,GAA6B;IAC7BrB,EADA,CAAAwB,UAAA,QAAAiB,UAAA,CAAAgB,MAAA,IAAAC,GAAA,EAAA1D,EAAA,CAAA2D,aAAA,CAA6B,QAAAlB,UAAA,CAAAgB,MAAA,IAAAG,GAAA,IAAAnB,UAAA,CAAAoB,IAAA,CACgB;IAS3C7D,EAAA,CAAAqB,SAAA,GAA2C;IAA3CrB,EAAA,CAAAsB,WAAA,UAAAhB,MAAA,CAAAwD,cAAA,CAAArB,UAAA,CAAAsB,GAAA,EAA2C;;IAIjC/D,EAAA,CAAAqB,SAAA,EAAgE;IAAhErB,EAAA,CAAAwB,UAAA,SAAAlB,MAAA,CAAAwD,cAAA,CAAArB,UAAA,CAAAsB,GAAA,8BAAgE;IAK1E/D,EAAA,CAAAqB,SAAA,EAA2C;;IAQtBrB,EAAA,CAAAqB,SAAA,GAAkB;IAAlBrB,EAAA,CAAAyB,iBAAA,CAAAgB,UAAA,CAAAoB,IAAA,CAAkB;IAEb7D,EAAA,CAAAqB,SAAA,GAAgC;IAAhCrB,EAAA,CAAAyB,iBAAA,CAAAnB,MAAA,CAAAkC,WAAA,CAAAC,UAAA,CAAAuB,KAAA,EAAgC;IACrDhE,EAAA,CAAAqB,SAAA,EAAoE;IAApErB,EAAA,CAAAwB,UAAA,SAAAiB,UAAA,CAAAC,aAAA,IAAAD,UAAA,CAAAC,aAAA,GAAAD,UAAA,CAAAuB,KAAA,CAAoE;IAMtDhE,EAAA,CAAAqB,SAAA,GAAc;IAAdrB,EAAA,CAAAwB,UAAA,YAAAxB,EAAA,CAAAgC,eAAA,KAAAiC,GAAA,EAAc;IAKRjE,EAAA,CAAAqB,SAAA,GAA4B;IAA5BrB,EAAA,CAAAkE,kBAAA,MAAAzB,UAAA,CAAAG,MAAA,CAAAuB,KAAA,MAA4B;;;;;;IAlFnEnE,EAAA,CAAAC,cAAA,cAIC;IADCD,EAAA,CAAAE,UAAA,mBAAAkE,mEAAA;MAAA,MAAAC,QAAA,GAAArE,EAAA,CAAAI,aAAA,CAAAkE,GAAA,EAAArB,SAAA;MAAA,MAAA3C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAiE,YAAA,CAAAF,QAAA,CAAmB;IAAA,EAAC;IAKzBrE,EAFJ,CAAAC,cAAA,cAA0B,cACA,aACC;IAAAD,EAAA,CAAAW,MAAA,GAAiB;IAAAX,EAAA,CAAAY,YAAA,EAAK;IAE3CZ,EADF,CAAAC,cAAA,cAAyB,cACA;IACrBD,EAAA,CAAAU,SAAA,mBAAwC;IACxCV,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAW,MAAA,GAAiC;IACzCX,EADyC,CAAAY,YAAA,EAAO,EAC1C;IACNZ,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAU,SAAA,oBAAiC;IACjCV,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAW,MAAA,IAAuB;IAC/BX,EAD+B,CAAAY,YAAA,EAAO,EAChC;IACNZ,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAU,SAAA,oBAAwC;IACxCV,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAW,MAAA,IAA0C;IAGtDX,EAHsD,CAAAY,YAAA,EAAO,EACnD,EACF,EACF;IACNZ,EAAA,CAAAC,cAAA,eAAyB;IACvBD,EAAA,CAAAU,SAAA,oBAAoC;IACpCV,EAAA,CAAAW,MAAA,kBACF;IACFX,EADE,CAAAY,YAAA,EAAM,EACF;IAIJZ,EADF,CAAAC,cAAA,eAA0B,cACG;IAAAD,EAAA,CAAAW,MAAA,oBAAY;IAAAX,EAAA,CAAAY,YAAA,EAAK;IAC5CZ,EAAA,CAAAC,cAAA,eAA2B;IACzBD,EAAA,CAAA8B,UAAA,KAAA0C,oDAAA,oBAIC;IAiDLxE,EADE,CAAAY,YAAA,EAAM,EACF;IAKFZ,EAFJ,CAAAC,cAAA,eAA+B,kBACC,YACtB;IAAAD,EAAA,CAAAW,MAAA,IAAmC;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAChDZ,EAAA,CAAAU,SAAA,oBAA4C;IAGlDV,EAFI,CAAAY,YAAA,EAAS,EACL,EACF;;;;;IAxFuBZ,EAAA,CAAAqB,SAAA,GAAiB;IAAjBrB,EAAA,CAAAyB,iBAAA,CAAA4C,QAAA,CAAAI,KAAA,CAAiB;IAI9BzE,EAAA,CAAAqB,SAAA,GAAiC;IAAjCrB,EAAA,CAAAkE,kBAAA,KAAAG,QAAA,CAAAK,YAAA,cAAiC;IAIjC1E,EAAA,CAAAqB,SAAA,GAAuB;IAAvBrB,EAAA,CAAAkE,kBAAA,KAAAG,QAAA,CAAAM,SAAA,OAAuB;IAIvB3E,EAAA,CAAAqB,SAAA,GAA0C;IAA1CrB,EAAA,CAAAkE,kBAAA,KAAA5D,MAAA,CAAAsE,YAAA,CAAAP,QAAA,CAAAQ,UAAA,YAA0C;IAe9B7E,EAAA,CAAAqB,SAAA,GAAsB;IAAArB,EAAtB,CAAAwB,UAAA,YAAA6C,QAAA,CAAAS,WAAA,CAAsB,iBAAAxE,MAAA,CAAAyE,gBAAA,CAAyB;IAyD/D/E,EAAA,CAAAqB,SAAA,GAAmC;IAAnCrB,EAAA,CAAAkE,kBAAA,cAAAG,QAAA,CAAAI,KAAA,cAAmC;;;;;;IAtG/CzE,EAFF,CAAAC,cAAA,cAA+F,iBAEH;IAAtDD,EAAA,CAAAE,UAAA,mBAAA8E,gEAAA;MAAAhF,EAAA,CAAAI,aAAA,CAAA6E,GAAA;MAAA,MAAA3E,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA4E,SAAA,EAAW;IAAA,EAAC;IACvDlF,EAAA,CAAAU,SAAA,mBAAyC;IAC3CV,EAAA,CAAAY,YAAA,EAAS;IACTZ,EAAA,CAAAC,cAAA,iBAAgG;IAA5DD,EAAA,CAAAE,UAAA,mBAAAiF,gEAAA;MAAAnF,EAAA,CAAAI,aAAA,CAAA6E,GAAA;MAAA,MAAA3E,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA8E,SAAA,EAAW;IAAA,EAAC;IACvDpF,EAAA,CAAAU,SAAA,mBAA4C;IAC9CV,EAAA,CAAAY,YAAA,EAAS;IAGTZ,EAAA,CAAAC,cAAA,cAAoG;IAAjCD,EAAhC,CAAAE,UAAA,wBAAAmF,kEAAA;MAAArF,EAAA,CAAAI,aAAA,CAAA6E,GAAA;MAAA,MAAA3E,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAcF,MAAA,CAAAgF,cAAA,EAAgB;IAAA,EAAC,wBAAAC,kEAAA;MAAAvF,EAAA,CAAAI,aAAA,CAAA6E,GAAA;MAAA,MAAA3E,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAeF,MAAA,CAAAkF,eAAA,EAAiB;IAAA,EAAC;IACjGxF,EAAA,CAAAC,cAAA,cAAmF;IACrFD,EAAA,CAAA8B,UAAA,IAAA2D,6CAAA,mBAIC;IA+FHzF,EAFE,CAAAY,YAAA,EAAM,EACA,EACF;;;;IA7GsDZ,EAAA,CAAAqB,SAAA,EAA+B;IAA/BrB,EAAA,CAAAwB,UAAA,aAAAlB,MAAA,CAAAoF,YAAA,OAA+B;IAG/B1F,EAAA,CAAAqB,SAAA,GAAqC;IAArCrB,EAAA,CAAAwB,UAAA,aAAAlB,MAAA,CAAAoF,YAAA,IAAApF,MAAA,CAAAqF,QAAA,CAAqC;IAMlE3F,EAAA,CAAAqB,SAAA,GAAuD;IAAvDrB,EAAA,CAAA4F,WAAA,8BAAAtF,MAAA,CAAAuF,WAAA,SAAuD;IAEhE7F,EAAA,CAAAqB,SAAA,EAAmB;IAAArB,EAAnB,CAAAwB,UAAA,YAAAlB,MAAA,CAAAwF,cAAA,CAAmB,iBAAAxF,MAAA,CAAAyF,gBAAA,CAAyB;;;;;IAqGlE/F,EAAA,CAAAC,cAAA,cAAyF;IACvFD,EAAA,CAAAU,SAAA,mBAA+D;IAC/DV,EAAA,CAAAC,cAAA,aAAwB;IAAAD,EAAA,CAAAW,MAAA,yBAAkB;IAAAX,EAAA,CAAAY,YAAA,EAAK;IAC/CZ,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAW,MAAA,sDAA+C;IAC1EX,EAD0E,CAAAY,YAAA,EAAI,EACxE;;;AD/KR,OAAM,MAAOoF,uBAAuB;EA2BlCC,YACUC,eAAgC,EAChCC,aAAwC,EACxCC,MAAc;IAFd,KAAAF,eAAe,GAAfA,eAAe;IACf,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,MAAM,GAANA,MAAM;IA7BhB,KAAAN,cAAc,GAAoB,EAAE;IACpC,KAAAO,SAAS,GAAG,IAAI;IAChB,KAAA9D,KAAK,GAAkB,IAAI;IAC3B,KAAA+D,aAAa,GAAG,IAAIC,GAAG,EAAU;IACzB,KAAAC,YAAY,GAAiB,IAAI3G,YAAY,EAAE;IAEvD;IACA,KAAA6F,YAAY,GAAG,CAAC;IAChB,KAAAG,WAAW,GAAG,CAAC;IACf,KAAAY,SAAS,GAAG,GAAG,CAAC,CAAC;IACjB,KAAAC,YAAY,GAAG,CAAC,CAAC,CAAC;IAClB,KAAAf,QAAQ,GAAG,CAAC;IAIZ,KAAAgB,cAAc,GAAG,IAAI,CAAC,CAAC;IACvB,KAAAC,aAAa,GAAG,IAAI;IACpB,KAAAC,QAAQ,GAAG,KAAK;IAEhB;IACA,KAAAtF,cAAc,GAAG,KAAK;IACtB,KAAAM,mBAAmB,GAAG,KAAK;IAC3B,KAAAF,YAAY,GAAG,GAAG;IAClB,KAAAC,eAAe,GAAG,EAAE;IACpB,KAAAkF,QAAQ,GAAG,KAAK;EAMb;EAEHC,QAAQA,CAAA;IACNC,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;IAClD,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAACC,uBAAuB,EAAE;IAC9B,IAAI,CAACC,sBAAsB,EAAE;IAC7B,IAAI,CAACC,wBAAwB,EAAE;IAC/B,IAAI,CAACC,mBAAmB,EAAE;IAC1B,IAAI,CAACC,iBAAiB,EAAE;EAC1B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAAChB,YAAY,CAACiB,WAAW,EAAE;IAC/B,IAAI,CAACC,aAAa,EAAE;EACtB;EAEQP,uBAAuBA,CAAA;IAC7B,IAAI,CAACX,YAAY,CAACmB,GAAG,CACnB,IAAI,CAACzB,eAAe,CAAC0B,eAAe,CAACC,SAAS,CAACC,MAAM,IAAG;MACtDd,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEa,MAAM,CAAC;MAC/D,IAAI,CAAChC,cAAc,GAAGgC,MAAM;MAC5B,IAAI,CAACzB,SAAS,GAAG,KAAK;MACtB,IAAI,CAAC0B,wBAAwB,EAAE;IACjC,CAAC,CAAC,CACH;EACH;EAEQX,sBAAsBA,CAAA;IAC5B,IAAI,CAACZ,YAAY,CAACmB,GAAG,CACnB,IAAI,CAACxB,aAAa,CAAC6B,cAAc,CAACH,SAAS,CAACvB,aAAa,IAAG;MAC1D,IAAI,CAACA,aAAa,GAAGA,aAAa;IACpC,CAAC,CAAC,CACH;EACH;EAEcY,kBAAkBA,CAAA;IAAA,IAAAe,KAAA;IAAA,OAAAC,iBAAA;MAC9B,IAAI;QACFD,KAAI,CAAC5B,SAAS,GAAG,IAAI;QACrB4B,KAAI,CAAC1F,KAAK,GAAG,IAAI;QACjB,MAAM0F,KAAI,CAAC/B,eAAe,CAACgB,kBAAkB,EAAE;OAChD,CAAC,OAAO3E,KAAK,EAAE;QACdyE,OAAO,CAACzE,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD0F,KAAI,CAAC1F,KAAK,GAAG,gCAAgC;QAC7C0F,KAAI,CAAC5B,SAAS,GAAG,KAAK;;IACvB;EACH;EAEA9B,YAAYA,CAACE,KAAoB;IAC/B,IAAI,CAAC2B,MAAM,CAAC+B,QAAQ,CAAC,CAAC,WAAW,CAAC,EAAE;MAClCC,WAAW,EAAE;QAAE3D,KAAK,EAAEA,KAAK,CAACA;MAAK;KAClC,CAAC;EACJ;EAEAvB,cAAcA,CAACmF,OAAgB,EAAEC,KAAY;IAC3CA,KAAK,CAACC,eAAe,EAAE;IACvB,IAAI,CAACnC,MAAM,CAAC+B,QAAQ,CAAC,CAAC,UAAU,EAAEE,OAAO,CAACtE,GAAG,CAAC,CAAC;EACjD;EAEMX,aAAaA,CAACiF,OAAgB,EAAEC,KAAY;IAAA,IAAAE,MAAA;IAAA,OAAAN,iBAAA;MAChDI,KAAK,CAACC,eAAe,EAAE;MACvB,IAAI;QACF,MAAME,MAAM,SAASD,MAAI,CAACrC,aAAa,CAACuC,WAAW,CAACL,OAAO,CAACtE,GAAG,CAAC;QAChE,IAAI0E,MAAM,CAACE,OAAO,EAAE;UAClB3B,OAAO,CAACC,GAAG,CAACwB,MAAM,CAACG,OAAO,CAAC;SAC5B,MAAM;UACL5B,OAAO,CAACzE,KAAK,CAAC,yBAAyB,EAAEkG,MAAM,CAACG,OAAO,CAAC;;OAE3D,CAAC,OAAOrG,KAAK,EAAE;QACdyE,OAAO,CAACzE,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;;IAC9C;EACH;EAEMe,cAAcA,CAAC+E,OAAgB,EAAEC,KAAY;IAAA,IAAAO,MAAA;IAAA,OAAAX,iBAAA;MACjDI,KAAK,CAACC,eAAe,EAAE;MACvB,IAAI;QACF,MAAMO,UAAU,GAAG,GAAGC,MAAM,CAACC,QAAQ,CAACC,MAAM,YAAYZ,OAAO,CAACtE,GAAG,EAAE;QACrE,MAAMmF,SAAS,CAACC,SAAS,CAACC,SAAS,CAACN,UAAU,CAAC;QAE/C,MAAMD,MAAI,CAAC1C,aAAa,CAACkD,YAAY,CAAChB,OAAO,CAACtE,GAAG,EAAE;UACjDuF,QAAQ,EAAE,WAAW;UACrBV,OAAO,EAAE,0BAA0BP,OAAO,CAACxE,IAAI,SAASwE,OAAO,CAAC5D,KAAK;SACtE,CAAC;QAEFuC,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;OACjD,CAAC,OAAO1E,KAAK,EAAE;QACdyE,OAAO,CAACzE,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;;IAC/C;EACH;EAEAC,WAAWA,CAACwB,KAAa;IACvB,OAAO,IAAIuF,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE;KACxB,CAAC,CAACC,MAAM,CAAC5F,KAAK,CAAC;EAClB;EAEAY,YAAYA,CAACiF,GAAW;IACtB,IAAIA,GAAG,IAAI,OAAO,EAAE;MAClB,OAAO,CAACA,GAAG,GAAG,OAAO,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;KACxC,MAAM,IAAID,GAAG,IAAI,IAAI,EAAE;MACtB,OAAO,CAACA,GAAG,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAEtC,OAAOD,GAAG,CAACE,QAAQ,EAAE;EACvB;EAEAzH,OAAOA,CAAA;IACL,IAAI,CAAC4E,kBAAkB,EAAE;EAC3B;EAEAnB,gBAAgBA,CAACiE,KAAa,EAAEvF,KAAoB;IAClD,OAAOA,KAAK,CAACA,KAAK;EACpB;EAEAX,cAAcA,CAACmG,SAAiB;IAC9B,OAAO,IAAI,CAAC3D,aAAa,CAAC4D,GAAG,CAACD,SAAS,CAAC;EAC1C;EAEAlF,gBAAgBA,CAACiF,KAAa,EAAE3B,OAAgB;IAC9C,OAAOA,OAAO,CAACtE,GAAG;EACpB;EAEA;EACQoG,cAAcA,CAAA;IACpB,IAAI,CAAC,IAAI,CAACvD,aAAa,IAAI,IAAI,CAACC,QAAQ,EAAE;IAE1C,IAAI,CAACa,aAAa,EAAE;IACpB,IAAI,CAAC0C,iBAAiB,GAAGC,WAAW,CAAC,MAAK;MACxC,IAAI,CAAC,IAAI,CAACxD,QAAQ,IAAI,IAAI,CAACf,cAAc,CAACwE,MAAM,GAAG,IAAI,CAAC5D,YAAY,EAAE;QACpE,IAAI,CAAC6D,aAAa,EAAE;;IAExB,CAAC,EAAE,IAAI,CAAC5D,cAAc,CAAC;EACzB;EAEQe,aAAaA,CAAA;IACnB,IAAI,IAAI,CAAC0C,iBAAiB,EAAE;MAC1BI,aAAa,CAAC,IAAI,CAACJ,iBAAiB,CAAC;MACrC,IAAI,CAACA,iBAAiB,GAAG,IAAI;;EAEjC;EAEQG,aAAaA,CAAA;IACnB,IAAI,IAAI,CAAC7E,YAAY,IAAI,IAAI,CAACC,QAAQ,EAAE;MACtC,IAAI,CAACD,YAAY,GAAG,CAAC;KACtB,MAAM;MACL,IAAI,CAACA,YAAY,EAAE;;IAErB,IAAI,CAAC+E,iBAAiB,EAAE;EAC1B;EAEAnF,cAAcA,CAAA;IACZ,IAAI,CAACuB,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACa,aAAa,EAAE;EACtB;EAEAlC,eAAeA,CAAA;IACb,IAAI,CAACqB,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACsD,cAAc,EAAE;EACvB;EAEA;EACQ9C,wBAAwBA,CAAA;IAC9B,MAAMqD,KAAK,GAAG3B,MAAM,CAAC4B,UAAU;IAC/B,IAAID,KAAK,IAAI,GAAG,EAAE;MAChB,IAAI,CAACjE,SAAS,GAAG,GAAG;MACpB,IAAI,CAACC,YAAY,GAAG,CAAC;KACtB,MAAM,IAAIgE,KAAK,IAAI,IAAI,EAAE;MACxB,IAAI,CAACjE,SAAS,GAAG,GAAG;MACpB,IAAI,CAACC,YAAY,GAAG,CAAC;KACtB,MAAM;MACL,IAAI,CAACD,SAAS,GAAG,GAAG;MACpB,IAAI,CAACC,YAAY,GAAG,CAAC;;IAEvB,IAAI,CAACkE,kBAAkB,EAAE;IACzB,IAAI,CAACH,iBAAiB,EAAE;EAC1B;EAEQnD,mBAAmBA,CAAA;IACzByB,MAAM,CAAC8B,gBAAgB,CAAC,QAAQ,EAAE,MAAK;MACrC,IAAI,CAACxD,wBAAwB,EAAE;IACjC,CAAC,CAAC;EACJ;EAEA;EACAuD,kBAAkBA,CAAA;IAChB,IAAI,CAACjF,QAAQ,GAAGmF,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,IAAI,CAACjF,cAAc,CAACwE,MAAM,GAAG,IAAI,CAAC5D,YAAY,CAAC;EAC7E;EAEAxB,SAASA,CAAA;IACP,IAAI,IAAI,CAACQ,YAAY,GAAG,CAAC,EAAE;MACzB,IAAI,CAACA,YAAY,EAAE;MACnB,IAAI,CAAC+E,iBAAiB,EAAE;MACxB,IAAI,CAACO,gCAAgC,EAAE;;EAE3C;EAEA5F,SAASA,CAAA;IACP,IAAI,IAAI,CAACM,YAAY,GAAG,IAAI,CAACC,QAAQ,EAAE;MACrC,IAAI,CAACD,YAAY,EAAE;MACnB,IAAI,CAAC+E,iBAAiB,EAAE;MACxB,IAAI,CAACO,gCAAgC,EAAE;;EAE3C;EAEQP,iBAAiBA,CAAA;IACvB,IAAI,CAAC5E,WAAW,GAAG,CAAC,IAAI,CAACH,YAAY,GAAG,IAAI,CAACe,SAAS;EACxD;EAEQuE,gCAAgCA,CAAA;IACtC,IAAI,CAACtD,aAAa,EAAE;IACpBuD,UAAU,CAAC,MAAK;MACd,IAAI,CAACd,cAAc,EAAE;IACvB,CAAC,EAAE,IAAI,CAAC;EACV;EAEA;EACQpC,wBAAwBA,CAAA;IAC9BkD,UAAU,CAAC,MAAK;MACd,IAAI,CAACL,kBAAkB,EAAE;MACzB,IAAI,CAAClF,YAAY,GAAG,CAAC;MACrB,IAAI,CAACG,WAAW,GAAG,CAAC;MACpB,IAAI,CAACsE,cAAc,EAAE;IACvB,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;EACA1J,iBAAiBA,CAAA;IACf,IAAI,CAACc,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAC1C,IAAI,IAAI,CAACA,cAAc,EAAE;MACvB,IAAI,CAACI,YAAY,EAAE;KACpB,MAAM;MACL,IAAI,CAACA,YAAY,EAAE;;EAEvB;EAEAT,qBAAqBA,CAAA;IACnB,IAAI,CAACW,mBAAmB,GAAG,CAAC,IAAI,CAACA,mBAAmB;EACtD;EAEAf,YAAYA,CAAA;IACVkG,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;EAC7D;EAEAjG,YAAYA,CAAA;IACV,IAAIkI,SAAS,CAACgC,KAAK,EAAE;MACnBhC,SAAS,CAACgC,KAAK,CAAC;QACdC,KAAK,EAAE,iBAAiB;QACxBC,IAAI,EAAE,kDAAkD;QACxD1H,GAAG,EAAEqF,MAAM,CAACC,QAAQ,CAACqC;OACtB,CAAC;KACH,MAAM;MACLnC,SAAS,CAACC,SAAS,CAACC,SAAS,CAACL,MAAM,CAACC,QAAQ,CAACqC,IAAI,CAAC;MACnDrE,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;;EAE3C;EAEA7F,eAAeA,CAAA;IACb4F,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;EACzD;EAEAvF,WAAWA,CAACyC,KAAa;IACvB,IAAIA,KAAK,IAAI,OAAO,EAAE;MACpB,OAAO,CAACA,KAAK,GAAG,OAAO,EAAE2F,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;KAC1C,MAAM,IAAI3F,KAAK,IAAI,IAAI,EAAE;MACxB,OAAO,CAACA,KAAK,GAAG,IAAI,EAAE2F,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAExC,OAAO3F,KAAK,CAAC4F,QAAQ,EAAE;EACzB;EAEQxC,iBAAiBA,CAAA;IACvB,IAAI,CAACT,QAAQ,GAAGiC,MAAM,CAAC4B,UAAU,IAAI,GAAG;EAC1C;;;uBA/SW3E,uBAAuB,EAAAhG,EAAA,CAAAsL,iBAAA,CAAAC,EAAA,CAAAC,eAAA,GAAAxL,EAAA,CAAAsL,iBAAA,CAAAG,EAAA,CAAAC,yBAAA,GAAA1L,EAAA,CAAAsL,iBAAA,CAAAK,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAvB5F,uBAAuB;MAAA6F,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA/L,EAAA,CAAAgM,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCjBpCtM,EAAA,CAAAC,cAAA,aAAuC;UAErCD,EAAA,CAAA8B,UAAA,IAAA0K,sCAAA,kBAAoD;UAiChDxM,EAFJ,CAAAC,cAAA,aAA4B,aACE,YACA;UACxBD,EAAA,CAAAU,SAAA,kBAAuD;UACvDV,EAAA,CAAAW,MAAA,wBACF;UAAAX,EAAA,CAAAY,YAAA,EAAK;UACLZ,EAAA,CAAAC,cAAA,WAA4B;UAAAD,EAAA,CAAAW,MAAA,0CAAmC;UAEnEX,EAFmE,CAAAY,YAAA,EAAI,EAC/D,EACF;UAkBNZ,EAfA,CAAA8B,UAAA,IAAA2K,sCAAA,iBAAiD,KAAAC,uCAAA,iBAeQ;UAWvD1M,EADF,CAAAC,cAAA,cAAmF,SAC9E;UAAAD,EAAA,CAAAW,MAAA,IAAsG;UAC3GX,EAD2G,CAAAY,YAAA,EAAI,EACzG;UAqHNZ,EAlHA,CAAA8B,UAAA,KAAA6K,uCAAA,kBAA+F,KAAAC,uCAAA,kBAkHN;UAK3F5M,EAAA,CAAAY,YAAA,EAAM;;;UA/LgCZ,EAAA,CAAAqB,SAAA,EAAc;UAAdrB,EAAA,CAAAwB,UAAA,SAAA+K,GAAA,CAAAzF,QAAA,CAAc;UA0C5C9G,EAAA,CAAAqB,SAAA,GAAe;UAAfrB,EAAA,CAAAwB,UAAA,SAAA+K,GAAA,CAAAlG,SAAA,CAAe;UAefrG,EAAA,CAAAqB,SAAA,EAAyB;UAAzBrB,EAAA,CAAAwB,UAAA,SAAA+K,GAAA,CAAAhK,KAAA,KAAAgK,GAAA,CAAAlG,SAAA,CAAyB;UAW1BrG,EAAA,CAAAqB,SAAA,GAAsG;UAAtGrB,EAAA,CAAA6M,kBAAA,wBAAAN,GAAA,CAAAlG,SAAA,gBAAAkG,GAAA,CAAAhK,KAAA,gCAAAgK,GAAA,CAAAzG,cAAA,CAAAwE,MAAA,KAAsG;UAIrGtK,EAAA,CAAAqB,SAAA,EAAuD;UAAvDrB,EAAA,CAAAwB,UAAA,UAAA+K,GAAA,CAAAlG,SAAA,KAAAkG,GAAA,CAAAhK,KAAA,IAAAgK,GAAA,CAAAzG,cAAA,CAAAwE,MAAA,KAAuD;UAkHvDtK,EAAA,CAAAqB,SAAA,EAAyD;UAAzDrB,EAAA,CAAAwB,UAAA,UAAA+K,GAAA,CAAAlG,SAAA,KAAAkG,GAAA,CAAAhK,KAAA,IAAAgK,GAAA,CAAAzG,cAAA,CAAAwE,MAAA,OAAyD;;;qBD/KrD1K,YAAY,EAAAkN,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAElN,WAAW,EAAAmN,EAAA,CAAAC,OAAA,EAAEnN,cAAc;MAAAoN,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}