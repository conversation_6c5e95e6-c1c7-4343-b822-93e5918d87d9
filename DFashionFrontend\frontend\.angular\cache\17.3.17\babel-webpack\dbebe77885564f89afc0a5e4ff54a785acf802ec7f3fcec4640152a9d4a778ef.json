{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashionFrontend/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CommonModule } from '@angular/common';\nimport { Subscription } from 'rxjs';\nimport { IonicModule } from '@ionic/angular';\nimport { CarouselModule } from 'ngx-owl-carousel-o';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/trending.service\";\nimport * as i2 from \"../../../../core/services/social-interactions.service\";\nimport * as i3 from \"../../../../core/services/cart.service\";\nimport * as i4 from \"../../../../core/services/wishlist.service\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@ionic/angular\";\nconst _c0 = () => [1, 2, 3, 4, 5, 6, 7, 8];\nconst _c1 = () => [1, 2, 3, 4, 5];\nfunction TrendingProductsComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function TrendingProductsComponent_div_1_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleSectionLike());\n    });\n    i0.ɵɵelement(2, \"ion-icon\", 15);\n    i0.ɵɵelementStart(3, \"span\", 16);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function TrendingProductsComponent_div_1_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openComments());\n    });\n    i0.ɵɵelement(6, \"ion-icon\", 18);\n    i0.ɵɵelementStart(7, \"span\", 16);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function TrendingProductsComponent_div_1_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.shareSection());\n    });\n    i0.ɵɵelement(10, \"ion-icon\", 20);\n    i0.ɵɵelementStart(11, \"span\", 21);\n    i0.ɵɵtext(12, \"Share\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function TrendingProductsComponent_div_1_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleSectionBookmark());\n    });\n    i0.ɵɵelement(14, \"ion-icon\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function TrendingProductsComponent_div_1_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openMusicPlayer());\n    });\n    i0.ɵɵelement(16, \"ion-icon\", 24);\n    i0.ɵɵelementStart(17, \"span\", 21);\n    i0.ɵɵtext(18, \"Music\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"active\", ctx_r1.isSectionLiked);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", ctx_r1.isSectionLiked ? \"heart\" : \"heart-outline\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.formatCount(ctx_r1.sectionLikes));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.formatCount(ctx_r1.sectionComments));\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"active\", ctx_r1.isSectionBookmarked);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", ctx_r1.isSectionBookmarked ? \"bookmark\" : \"bookmark-outline\");\n  }\n}\nfunction TrendingProductsComponent_div_12_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28);\n    i0.ɵɵelement(1, \"div\", 29);\n    i0.ɵɵelementStart(2, \"div\", 30);\n    i0.ɵɵelement(3, \"div\", 31)(4, \"div\", 32)(5, \"div\", 33);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TrendingProductsComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 26);\n    i0.ɵɵtemplate(2, TrendingProductsComponent_div_12_div_2_Template, 6, 0, \"div\", 27);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c0));\n  }\n}\nfunction TrendingProductsComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵelement(1, \"ion-icon\", 35);\n    i0.ɵɵelementStart(2, \"p\", 36);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function TrendingProductsComponent_div_13_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onRetry());\n    });\n    i0.ɵɵelement(5, \"ion-icon\", 38);\n    i0.ɵɵtext(6, \" Try Again \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction TrendingProductsComponent_div_14_div_7_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 69);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r6 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getDiscountPercentage(product_r6), \"% OFF \");\n  }\n}\nfunction TrendingProductsComponent_div_14_div_7_span_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 70);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r6 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.formatPrice(product_r6.originalPrice));\n  }\n}\nfunction TrendingProductsComponent_div_14_div_7_ion_icon_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ion-icon\", 15);\n  }\n  if (rf & 2) {\n    const star_r7 = ctx.$implicit;\n    const product_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵclassProp(\"filled\", star_r7 <= product_r6.rating.average);\n    i0.ɵɵproperty(\"name\", star_r7 <= product_r6.rating.average ? \"star\" : \"star-outline\");\n  }\n}\nfunction TrendingProductsComponent_div_14_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 46);\n    i0.ɵɵlistener(\"click\", function TrendingProductsComponent_div_14_div_7_Template_div_click_0_listener() {\n      const product_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onProductClick(product_r6));\n    });\n    i0.ɵɵelementStart(1, \"div\", 47);\n    i0.ɵɵelement(2, \"img\", 48);\n    i0.ɵɵelementStart(3, \"div\", 49);\n    i0.ɵɵelement(4, \"ion-icon\", 50);\n    i0.ɵɵtext(5, \" Trending \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, TrendingProductsComponent_div_14_div_7_div_6_Template, 2, 1, \"div\", 51);\n    i0.ɵɵelementStart(7, \"div\", 52)(8, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function TrendingProductsComponent_div_14_div_7_Template_button_click_8_listener($event) {\n      const product_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onLikeProduct(product_r6, $event));\n    });\n    i0.ɵɵelement(9, \"ion-icon\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function TrendingProductsComponent_div_14_div_7_Template_button_click_10_listener($event) {\n      const product_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onShareProduct(product_r6, $event));\n    });\n    i0.ɵɵelement(11, \"ion-icon\", 53);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"div\", 54)(13, \"div\", 55);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"h3\", 56);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 57)(18, \"span\", 58);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(20, TrendingProductsComponent_div_14_div_7_span_20_Template, 2, 1, \"span\", 59);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 60)(22, \"div\", 61);\n    i0.ɵɵtemplate(23, TrendingProductsComponent_div_14_div_7_ion_icon_23_Template, 1, 3, \"ion-icon\", 62);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"span\", 63);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\", 64)(27, \"button\", 65);\n    i0.ɵɵlistener(\"click\", function TrendingProductsComponent_div_14_div_7_Template_button_click_27_listener($event) {\n      const product_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onAddToCart(product_r6, $event));\n    });\n    i0.ɵɵelement(28, \"ion-icon\", 66);\n    i0.ɵɵtext(29, \" Add to Cart \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"button\", 67);\n    i0.ɵɵlistener(\"click\", function TrendingProductsComponent_div_14_div_7_Template_button_click_30_listener($event) {\n      const product_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onAddToWishlist(product_r6, $event));\n    });\n    i0.ɵɵelement(31, \"ion-icon\", 68);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const product_r6 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", product_r6.images[0].url, i0.ɵɵsanitizeUrl)(\"alt\", product_r6.images[0].alt || product_r6.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getDiscountPercentage(product_r6) > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"liked\", ctx_r1.isProductLiked(product_r6._id));\n    i0.ɵɵattribute(\"aria-label\", \"Like \" + product_r6.name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", ctx_r1.isProductLiked(product_r6._id) ? \"heart\" : \"heart-outline\");\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"aria-label\", \"Share \" + product_r6.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(product_r6.brand);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r6.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.formatPrice(product_r6.price));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r6.originalPrice && product_r6.originalPrice > product_r6.price);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(14, _c1));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", product_r6.rating.count, \")\");\n  }\n}\nfunction TrendingProductsComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function TrendingProductsComponent_div_14_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.slidePrev());\n    });\n    i0.ɵɵelement(2, \"ion-icon\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function TrendingProductsComponent_div_14_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.slideNext());\n    });\n    i0.ɵɵelement(4, \"ion-icon\", 8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 43);\n    i0.ɵɵlistener(\"mouseenter\", function TrendingProductsComponent_div_14_Template_div_mouseenter_5_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.pauseAutoSlide());\n    })(\"mouseleave\", function TrendingProductsComponent_div_14_Template_div_mouseleave_5_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.resumeAutoSlide());\n    });\n    i0.ɵɵelementStart(6, \"div\", 44);\n    i0.ɵɵtemplate(7, TrendingProductsComponent_div_14_div_7_Template, 32, 15, \"div\", 45);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentSlide === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentSlide >= ctx_r1.maxSlide);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"transform\", \"translateX(\" + ctx_r1.slideOffset + \"px)\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.trendingProducts)(\"ngForTrackBy\", ctx_r1.trackByProductId);\n  }\n}\nfunction TrendingProductsComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71);\n    i0.ɵɵelement(1, \"ion-icon\", 72);\n    i0.ɵɵelementStart(2, \"h3\", 73);\n    i0.ɵɵtext(3, \"No Trending Products\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 74);\n    i0.ɵɵtext(5, \"Check back later for trending items\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let TrendingProductsComponent = /*#__PURE__*/(() => {\n  class TrendingProductsComponent {\n    constructor(trendingService, socialService, cartService, wishlistService, router) {\n      this.trendingService = trendingService;\n      this.socialService = socialService;\n      this.cartService = cartService;\n      this.wishlistService = wishlistService;\n      this.router = router;\n      this.trendingProducts = [];\n      this.isLoading = true;\n      this.error = null;\n      this.likedProducts = new Set();\n      this.subscription = new Subscription();\n      // Slider properties\n      this.currentSlide = 0;\n      this.slideOffset = 0;\n      this.cardWidth = 280; // Width of each product card including margin\n      this.visibleCards = 4; // Number of cards visible at once\n      this.maxSlide = 0;\n      this.autoSlideDelay = 3000; // 3 seconds\n      this.isAutoSliding = true;\n      this.isPaused = false;\n      // Section interaction properties\n      this.isSectionLiked = false;\n      this.isSectionBookmarked = false;\n      this.sectionLikes = 365;\n      this.sectionComments = 105;\n      this.isMobile = false;\n      // Owl Carousel Options with Auto-sliding\n      this.carouselOptions = {\n        loop: true,\n        mouseDrag: true,\n        touchDrag: true,\n        pullDrag: false,\n        dots: true,\n        navSpeed: 700,\n        navText: ['<ion-icon name=\"chevron-back\"></ion-icon>', '<ion-icon name=\"chevron-forward\"></ion-icon>'],\n        autoplay: true,\n        autoplayTimeout: 4000,\n        autoplayHoverPause: true,\n        autoplaySpeed: 1000,\n        smartSpeed: 1000,\n        fluidSpeed: true,\n        responsive: {\n          0: {\n            items: 1,\n            margin: 10,\n            nav: false,\n            dots: true\n          },\n          576: {\n            items: 2,\n            margin: 15,\n            nav: true,\n            dots: true\n          },\n          768: {\n            items: 3,\n            margin: 20,\n            nav: true,\n            dots: true\n          },\n          992: {\n            items: 4,\n            margin: 20,\n            nav: true,\n            dots: false // Hide dots on desktop, show nav instead\n          }\n        },\n        nav: true,\n        margin: 20,\n        stagePadding: 0,\n        center: false,\n        animateOut: false,\n        animateIn: false\n      };\n    }\n    ngOnInit() {\n      this.loadTrendingProducts();\n      this.subscribeTrendingProducts();\n      this.subscribeLikedProducts();\n      this.updateResponsiveSettings();\n      this.setupResizeListener();\n      this.checkMobileDevice();\n    }\n    ngOnDestroy() {\n      this.subscription.unsubscribe();\n      this.stopAutoSlide();\n    }\n    subscribeTrendingProducts() {\n      this.subscription.add(this.trendingService.trendingProducts$.subscribe(products => {\n        this.trendingProducts = products;\n        this.isLoading = false;\n        this.updateSliderOnProductsLoad();\n      }));\n    }\n    subscribeLikedProducts() {\n      this.subscription.add(this.socialService.likedProducts$.subscribe(likedProducts => {\n        this.likedProducts = likedProducts;\n      }));\n    }\n    loadTrendingProducts() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        try {\n          _this.isLoading = true;\n          _this.error = null;\n          yield _this.trendingService.loadTrendingProducts(1, 8);\n        } catch (error) {\n          console.error('Error loading trending products:', error);\n          _this.error = 'Failed to load trending products';\n          _this.isLoading = false;\n        }\n      })();\n    }\n    onProductClick(product) {\n      this.router.navigate(['/product', product._id]);\n    }\n    onLikeProduct(product, event) {\n      var _this2 = this;\n      return _asyncToGenerator(function* () {\n        event.stopPropagation();\n        try {\n          const result = yield _this2.socialService.likeProduct(product._id);\n          if (result.success) {\n            console.log(result.message);\n          } else {\n            console.error('Failed to like product:', result.message);\n          }\n        } catch (error) {\n          console.error('Error liking product:', error);\n        }\n      })();\n    }\n    onShareProduct(product, event) {\n      var _this3 = this;\n      return _asyncToGenerator(function* () {\n        event.stopPropagation();\n        try {\n          // For now, copy link to clipboard\n          const productUrl = `${window.location.origin}/product/${product._id}`;\n          yield navigator.clipboard.writeText(productUrl);\n          // Track the share\n          yield _this3.socialService.shareProduct(product._id, {\n            platform: 'copy_link',\n            message: `Check out this amazing ${product.name} from ${product.brand}!`\n          });\n          console.log('Product link copied to clipboard!');\n        } catch (error) {\n          console.error('Error sharing product:', error);\n        }\n      })();\n    }\n    onAddToCart(product, event) {\n      var _this4 = this;\n      return _asyncToGenerator(function* () {\n        event.stopPropagation();\n        try {\n          yield _this4.cartService.addToCart(product._id, 1);\n          console.log('Product added to cart!');\n        } catch (error) {\n          console.error('Error adding to cart:', error);\n        }\n      })();\n    }\n    onAddToWishlist(product, event) {\n      var _this5 = this;\n      return _asyncToGenerator(function* () {\n        event.stopPropagation();\n        try {\n          yield _this5.wishlistService.addToWishlist(product._id);\n          console.log('Product added to wishlist!');\n        } catch (error) {\n          console.error('Error adding to wishlist:', error);\n        }\n      })();\n    }\n    getDiscountPercentage(product) {\n      if (product.originalPrice && product.originalPrice > product.price) {\n        return Math.round((product.originalPrice - product.price) / product.originalPrice * 100);\n      }\n      return 0;\n    }\n    formatPrice(price) {\n      return new Intl.NumberFormat('en-IN', {\n        style: 'currency',\n        currency: 'INR',\n        minimumFractionDigits: 0\n      }).format(price);\n    }\n    onRetry() {\n      this.loadTrendingProducts();\n    }\n    onViewAll() {\n      this.router.navigate(['/products'], {\n        queryParams: {\n          filter: 'trending'\n        }\n      });\n    }\n    isProductLiked(productId) {\n      return this.likedProducts.has(productId);\n    }\n    trackByProductId(index, product) {\n      return product._id;\n    }\n    // Auto-sliding methods\n    startAutoSlide() {\n      if (!this.isAutoSliding || this.isPaused) return;\n      this.stopAutoSlide();\n      this.autoSlideInterval = setInterval(() => {\n        if (!this.isPaused && this.trendingProducts.length > this.visibleCards) {\n          this.autoSlideNext();\n        }\n      }, this.autoSlideDelay);\n    }\n    stopAutoSlide() {\n      if (this.autoSlideInterval) {\n        clearInterval(this.autoSlideInterval);\n        this.autoSlideInterval = null;\n      }\n    }\n    autoSlideNext() {\n      if (this.currentSlide >= this.maxSlide) {\n        // Reset to beginning for infinite loop\n        this.currentSlide = 0;\n      } else {\n        this.currentSlide++;\n      }\n      this.updateSlideOffset();\n    }\n    pauseAutoSlide() {\n      this.isPaused = true;\n      this.stopAutoSlide();\n    }\n    resumeAutoSlide() {\n      this.isPaused = false;\n      this.startAutoSlide();\n    }\n    // Responsive methods\n    updateResponsiveSettings() {\n      const width = window.innerWidth;\n      if (width <= 480) {\n        this.cardWidth = 195; // 180px + 15px gap\n        this.visibleCards = 1;\n      } else if (width <= 768) {\n        this.cardWidth = 215; // 200px + 15px gap\n        this.visibleCards = 2;\n      } else if (width <= 1200) {\n        this.cardWidth = 260; // 240px + 20px gap\n        this.visibleCards = 3;\n      } else {\n        this.cardWidth = 280; // 260px + 20px gap\n        this.visibleCards = 4;\n      }\n      this.updateSliderLimits();\n      this.updateSlideOffset();\n    }\n    setupResizeListener() {\n      window.addEventListener('resize', () => {\n        this.updateResponsiveSettings();\n      });\n    }\n    // Slider methods\n    updateSliderLimits() {\n      this.maxSlide = Math.max(0, this.trendingProducts.length - this.visibleCards);\n    }\n    slidePrev() {\n      if (this.currentSlide > 0) {\n        this.currentSlide--;\n        this.updateSlideOffset();\n        this.restartAutoSlideAfterInteraction();\n      }\n    }\n    slideNext() {\n      if (this.currentSlide < this.maxSlide) {\n        this.currentSlide++;\n        this.updateSlideOffset();\n        this.restartAutoSlideAfterInteraction();\n      }\n    }\n    restartAutoSlideAfterInteraction() {\n      this.stopAutoSlide();\n      setTimeout(() => {\n        this.startAutoSlide();\n      }, 2000); // Wait 2 seconds before resuming auto-slide\n    }\n    updateSlideOffset() {\n      this.slideOffset = -this.currentSlide * this.cardWidth;\n    }\n    // Update slider when products load\n    updateSliderOnProductsLoad() {\n      setTimeout(() => {\n        this.updateSliderLimits();\n        this.currentSlide = 0;\n        this.slideOffset = 0;\n        this.startAutoSlide();\n      }, 100);\n    }\n    // Section interaction methods\n    toggleSectionLike() {\n      this.isSectionLiked = !this.isSectionLiked;\n      if (this.isSectionLiked) {\n        this.sectionLikes++;\n      } else {\n        this.sectionLikes--;\n      }\n    }\n    toggleSectionBookmark() {\n      this.isSectionBookmarked = !this.isSectionBookmarked;\n    }\n    openComments() {\n      // Open comments modal/sheet\n      console.log('Opening comments for trending products section');\n    }\n    shareSection() {\n      // Share section functionality\n      if (navigator.share) {\n        navigator.share({\n          title: 'Trending Products',\n          text: 'Check out these trending fashion products!',\n          url: window.location.href\n        });\n      } else {\n        // Fallback for browsers that don't support Web Share API\n        navigator.clipboard.writeText(window.location.href);\n        console.log('Link copied to clipboard');\n      }\n    }\n    openMusicPlayer() {\n      // Open music player for section\n      console.log('Opening music player for trending products');\n    }\n    formatCount(count) {\n      if (count >= 1000000) {\n        return (count / 1000000).toFixed(1) + 'M';\n      } else if (count >= 1000) {\n        return (count / 1000).toFixed(1) + 'K';\n      }\n      return count.toString();\n    }\n    checkMobileDevice() {\n      this.isMobile = window.innerWidth <= 768;\n    }\n    static {\n      this.ɵfac = function TrendingProductsComponent_Factory(t) {\n        return new (t || TrendingProductsComponent)(i0.ɵɵdirectiveInject(i1.TrendingService), i0.ɵɵdirectiveInject(i2.SocialInteractionsService), i0.ɵɵdirectiveInject(i3.CartService), i0.ɵɵdirectiveInject(i4.WishlistService), i0.ɵɵdirectiveInject(i5.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: TrendingProductsComponent,\n        selectors: [[\"app-trending-products\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 16,\n        vars: 5,\n        consts: [[1, \"trending-products-container\"], [\"class\", \"mobile-action-buttons\", 4, \"ngIf\"], [1, \"section-header\"], [1, \"header-content\"], [1, \"section-title\"], [\"name\", \"trending-up\", 1, \"title-icon\"], [1, \"section-subtitle\"], [1, \"view-all-btn\", 3, \"click\"], [\"name\", \"chevron-forward\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"products-slider-container\", 4, \"ngIf\"], [\"class\", \"empty-container\", 4, \"ngIf\"], [1, \"mobile-action-buttons\"], [1, \"action-btn\", \"like-btn\", 3, \"click\"], [3, \"name\"], [1, \"action-count\"], [1, \"action-btn\", \"comment-btn\", 3, \"click\"], [\"name\", \"chatbubble-outline\"], [1, \"action-btn\", \"share-btn\", 3, \"click\"], [\"name\", \"arrow-redo-outline\"], [1, \"action-text\"], [1, \"action-btn\", \"bookmark-btn\", 3, \"click\"], [1, \"action-btn\", \"music-btn\", 3, \"click\"], [\"name\", \"musical-notes\"], [1, \"loading-container\"], [1, \"loading-grid\"], [\"class\", \"loading-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"loading-card\"], [1, \"loading-image\"], [1, \"loading-content\"], [1, \"loading-line\", \"short\"], [1, \"loading-line\", \"medium\"], [1, \"loading-line\", \"long\"], [1, \"error-container\"], [\"name\", \"alert-circle\", 1, \"error-icon\"], [1, \"error-message\"], [1, \"retry-btn\", 3, \"click\"], [\"name\", \"refresh\"], [1, \"products-slider-container\"], [1, \"slider-nav\", \"prev-btn\", 3, \"click\", \"disabled\"], [\"name\", \"chevron-back\"], [1, \"slider-nav\", \"next-btn\", 3, \"click\", \"disabled\"], [1, \"products-slider-wrapper\", 3, \"mouseenter\", \"mouseleave\"], [1, \"products-slider\"], [\"class\", \"product-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"product-card\", 3, \"click\"], [1, \"product-image-container\"], [\"loading\", \"lazy\", 1, \"product-image\", 3, \"src\", \"alt\"], [1, \"trending-badge\"], [\"name\", \"trending-up\"], [\"class\", \"discount-badge\", 4, \"ngIf\"], [1, \"action-buttons\"], [\"name\", \"share-outline\"], [1, \"product-info\"], [1, \"product-brand\"], [1, \"product-name\"], [1, \"price-section\"], [1, \"current-price\"], [\"class\", \"original-price\", 4, \"ngIf\"], [1, \"rating-section\"], [1, \"stars\"], [3, \"name\", \"filled\", 4, \"ngFor\", \"ngForOf\"], [1, \"rating-text\"], [1, \"product-actions\"], [1, \"cart-btn\", 3, \"click\"], [\"name\", \"bag-add-outline\"], [1, \"wishlist-btn\", 3, \"click\"], [\"name\", \"heart-outline\"], [1, \"discount-badge\"], [1, \"original-price\"], [1, \"empty-container\"], [\"name\", \"trending-up-outline\", 1, \"empty-icon\"], [1, \"empty-title\"], [1, \"empty-message\"]],\n        template: function TrendingProductsComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0);\n            i0.ɵɵtemplate(1, TrendingProductsComponent_div_1_Template, 19, 8, \"div\", 1);\n            i0.ɵɵelementStart(2, \"div\", 2)(3, \"div\", 3)(4, \"h2\", 4);\n            i0.ɵɵelement(5, \"ion-icon\", 5);\n            i0.ɵɵtext(6, \" Trending Now \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(7, \"p\", 6);\n            i0.ɵɵtext(8, \"Most popular products this week\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(9, \"button\", 7);\n            i0.ɵɵlistener(\"click\", function TrendingProductsComponent_Template_button_click_9_listener() {\n              return ctx.onViewAll();\n            });\n            i0.ɵɵtext(10, \" View All \");\n            i0.ɵɵelement(11, \"ion-icon\", 8);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(12, TrendingProductsComponent_div_12_Template, 3, 2, \"div\", 9)(13, TrendingProductsComponent_div_13_Template, 7, 1, \"div\", 10)(14, TrendingProductsComponent_div_14_Template, 8, 6, \"div\", 11)(15, TrendingProductsComponent_div_15_Template, 6, 0, \"div\", 12);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isMobile);\n            i0.ɵɵadvance(11);\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.error && !ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.trendingProducts.length > 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.trendingProducts.length === 0);\n          }\n        },\n        dependencies: [CommonModule, i6.NgForOf, i6.NgIf, IonicModule, i7.IonIcon, CarouselModule],\n        styles: [\".trending-products-container[_ngcontent-%COMP%]{padding:20px;background:linear-gradient(135deg,#f8f9fa,#e9ecef);border-radius:16px;margin-bottom:24px;position:relative;max-width:675px}.mobile-action-buttons[_ngcontent-%COMP%]{position:absolute;right:15px;top:50%;transform:translateY(-50%);display:flex;flex-direction:column;gap:20px;z-index:10}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]{width:48px;height:48px;border-radius:50%;border:none;background:#fff3;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);color:#fff;display:flex;flex-direction:column;align-items:center;justify-content:center;cursor:pointer;transition:all .3s ease;position:relative}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:24px;margin-bottom:2px}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   .action-count[_ngcontent-%COMP%], .mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   .action-text[_ngcontent-%COMP%]{font-size:10px;font-weight:600;line-height:1}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:hover{transform:scale(1.1);background:#ffffff4d}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active[_ngcontent-%COMP%]{background:#ffffffe6;color:#ff6b35}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active.like-btn[_ngcontent-%COMP%]{color:#ff3040}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active.like-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_heartBeat .6s ease-in-out}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active.bookmark-btn[_ngcontent-%COMP%]{color:gold}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.like-btn.active[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:#ff3040}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.comment-btn[_ngcontent-%COMP%]:hover, .mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.share-btn[_ngcontent-%COMP%]:hover{background:#ffffff4d}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.music-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,#ff3040,#ff6b35)}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.music-btn[_ngcontent-%COMP%]:hover{transform:scale(1.1) rotate(15deg)}@keyframes _ngcontent-%COMP%_heartBeat{0%{transform:scale(1)}25%{transform:scale(1.3)}50%{transform:scale(1.1)}75%{transform:scale(1.25)}to{transform:scale(1)}}@media (min-width: 769px){.mobile-action-buttons[_ngcontent-%COMP%]{display:none}}.section-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:24px}.section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]{flex:1}.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{font-size:24px;font-weight:700;color:#1a1a1a;margin:0 0 8px;display:flex;align-items:center;gap:12px}.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   .title-icon[_ngcontent-%COMP%]{font-size:28px;color:#ff6b35}.section-header[_ngcontent-%COMP%]   .section-subtitle[_ngcontent-%COMP%]{font-size:14px;color:#666;margin:0}.section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,#ff6b35,#f7931e);color:#fff;border:none;padding:12px 20px;border-radius:25px;font-weight:600;font-size:14px;display:flex;align-items:center;gap:8px;cursor:pointer;transition:all .3s ease;box-shadow:0 4px 15px #ff6b354d}.section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 6px 20px #ff6b3566}.section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:16px}.loading-container[_ngcontent-%COMP%]   .loading-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(280px,1fr));gap:20px}.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]{background:#fff;border-radius:16px;overflow:hidden;box-shadow:0 4px 20px #00000014}.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-image[_ngcontent-%COMP%]{width:100%;height:200px;background:linear-gradient(90deg,#f0f0f0 25%,#e0e0e0,#f0f0f0 75%);background-size:200% 100%;animation:_ngcontent-%COMP%_loading 1.5s infinite}.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]{padding:16px}.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line[_ngcontent-%COMP%]{height:12px;background:linear-gradient(90deg,#f0f0f0 25%,#e0e0e0,#f0f0f0 75%);background-size:200% 100%;animation:_ngcontent-%COMP%_loading 1.5s infinite;border-radius:6px;margin-bottom:8px}.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.short[_ngcontent-%COMP%]{width:40%}.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.medium[_ngcontent-%COMP%]{width:60%}.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.long[_ngcontent-%COMP%]{width:80%}@keyframes _ngcontent-%COMP%_loading{0%{background-position:200% 0}to{background-position:-200% 0}}.error-container[_ngcontent-%COMP%]{text-align:center;padding:40px 20px}.error-container[_ngcontent-%COMP%]   .error-icon[_ngcontent-%COMP%]{font-size:48px;color:#dc3545;margin-bottom:16px}.error-container[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%]{font-size:16px;color:#666;margin-bottom:20px}.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]{background:#007bff;color:#fff;border:none;padding:12px 24px;border-radius:8px;font-weight:600;cursor:pointer;display:flex;align-items:center;gap:8px;margin:0 auto;transition:background .3s ease}.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]:hover{background:#0056b3}.products-slider-container[_ngcontent-%COMP%]{position:relative;padding:0 30px}.products-slider-container[_ngcontent-%COMP%]:hover   .owl-carousel[_ngcontent-%COMP%]   .owl-dots[_ngcontent-%COMP%]   .owl-dot.active[_ngcontent-%COMP%]:after{animation-play-state:paused;border-color:#ff6b3580}.products-slider-container[_ngcontent-%COMP%]     .owl-carousel{position:relative}.products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-stage{transition:transform 1s ease-in-out!important}.products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav{position:absolute;top:50%;transform:translateY(-50%);width:100%;z-index:10}.products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev, .products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next{position:absolute;background:#000000b3!important;color:#fff!important;border:none!important;width:40px!important;height:40px!important;border-radius:50%!important;display:flex!important;align-items:center!important;justify-content:center!important;cursor:pointer!important;transition:all .3s ease!important;font-size:16px!important;outline:none!important}.products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev ion-icon, .products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next ion-icon{font-size:18px}.products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev:hover, .products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next:hover{background:#000000e6!important;transform:scale(1.1)!important;box-shadow:0 4px 15px #0000004d!important}.products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev.disabled, .products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next.disabled{opacity:.3!important;cursor:not-allowed!important}.products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev{left:-30px}.products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next{right:-30px}.products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-dots{text-align:center;margin-top:20px;padding:10px 0}.products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-dots .owl-dot{width:10px!important;height:10px!important;border-radius:50%!important;background:#ff6b354d!important;margin:0 6px!important;cursor:pointer!important;transition:all .4s ease!important;border:2px solid transparent!important;outline:none!important;position:relative!important}.products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-dots .owl-dot.active{background:#ff6b35!important;transform:scale(1.3)!important;border-color:#ff6b354d!important}.products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-dots .owl-dot.active:after{content:\\\"\\\";position:absolute;inset:-3px;border:2px solid #ff6b35;border-radius:50%;animation:_ngcontent-%COMP%_progress-ring 4s linear infinite}.products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-dots .owl-dot:hover:not(.active){background:#ff6b3599!important;transform:scale(1.1)!important}.products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-stage-outer{padding:0;overflow:visible}@keyframes _ngcontent-%COMP%_progress-ring{0%{transform:rotate(0);border-color:#ff6b35 transparent transparent transparent}25%{border-color:#ff6b35 #ff6b35 transparent transparent}50%{border-color:#ff6b35 #ff6b35 #ff6b35 transparent}75%{border-color:#ff6b35 #ff6b35 #ff6b35 #ff6b35}to{transform:rotate(360deg);border-color:#ff6b35 transparent transparent transparent}}.products-slider-container[_ngcontent-%COMP%]{position:relative;margin:0 -20px}.products-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]{position:absolute;top:50%;transform:translateY(-50%);z-index:10;background:#000000b3;color:#fff;border:none;width:40px;height:40px;border-radius:50%;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all .3s ease}.products-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]:hover:not(:disabled){background:#000000e6;transform:translateY(-50%) scale(1.1)}.products-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]:disabled{opacity:.3;cursor:not-allowed}.products-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px}.products-slider-container[_ngcontent-%COMP%]   .slider-nav.prev-btn[_ngcontent-%COMP%]{left:-20px}.products-slider-container[_ngcontent-%COMP%]   .slider-nav.next-btn[_ngcontent-%COMP%]{right:-20px}.products-slider-wrapper[_ngcontent-%COMP%]{overflow:hidden;padding:0 20px}.products-slider[_ngcontent-%COMP%]{display:flex;transition:transform .4s cubic-bezier(.25,.46,.45,.94);gap:20px}.products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]{flex:0 0 260px;width:260px}@media (max-width: 1200px){.products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]{flex:0 0 240px;width:240px}}@media (max-width: 768px){.products-slider-container[_ngcontent-%COMP%]{margin:0 -10px}.products-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]{width:35px;height:35px}.products-slider-container[_ngcontent-%COMP%]   .slider-nav.prev-btn[_ngcontent-%COMP%]{left:-15px}.products-slider-container[_ngcontent-%COMP%]   .slider-nav.next-btn[_ngcontent-%COMP%]{right:-15px}.products-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:16px}.products-slider-wrapper[_ngcontent-%COMP%]{padding:0 10px}.products-slider[_ngcontent-%COMP%]{gap:15px}.products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]{flex:0 0 200px;width:200px}}@media (max-width: 480px){.products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]{flex:0 0 180px;width:180px}}.product-card[_ngcontent-%COMP%]{background:#fff;border-radius:16px;overflow:hidden;box-shadow:0 4px 20px #00000014;transition:all .3s ease;cursor:pointer;width:100%;height:auto}.product-card[_ngcontent-%COMP%]:hover{transform:translateY(-8px);box-shadow:0 12px 40px #00000026}.product-card[_ngcontent-%COMP%]:hover   .action-buttons[_ngcontent-%COMP%]{opacity:1;transform:translateY(0)}.product-image-container[_ngcontent-%COMP%]{position:relative;overflow:hidden}.product-image-container[_ngcontent-%COMP%]   .product-image[_ngcontent-%COMP%]{width:100%;height:200px;object-fit:cover;transition:transform .3s ease}.product-image-container[_ngcontent-%COMP%]   .trending-badge[_ngcontent-%COMP%]{position:absolute;top:12px;left:12px;background:linear-gradient(135deg,#ff6b35,#f7931e);color:#fff;padding:6px 12px;border-radius:20px;font-size:12px;font-weight:600;display:flex;align-items:center;gap:4px}.product-image-container[_ngcontent-%COMP%]   .trending-badge[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:14px}.product-image-container[_ngcontent-%COMP%]   .discount-badge[_ngcontent-%COMP%]{position:absolute;top:12px;right:12px;background:#dc3545;color:#fff;padding:6px 10px;border-radius:12px;font-size:12px;font-weight:700}.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]{position:absolute;top:50%;right:12px;transform:translateY(-50%);display:flex;flex-direction:column;gap:8px;opacity:0;transition:all .3s ease}.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;border:none;background:#ffffffe6;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all .3s ease}.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:20px;color:#333}.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:hover{background:#fff;transform:scale(1.1)}.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn.like-btn[_ngcontent-%COMP%]:hover   ion-icon[_ngcontent-%COMP%]{color:#dc3545}.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn.like-btn.liked[_ngcontent-%COMP%]{background:#dc35451a}.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn.like-btn.liked[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:#dc3545}.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn.share-btn[_ngcontent-%COMP%]:hover   ion-icon[_ngcontent-%COMP%]{color:#007bff}.product-info[_ngcontent-%COMP%]{padding:16px}.product-info[_ngcontent-%COMP%]   .product-brand[_ngcontent-%COMP%]{font-size:12px;color:#666;text-transform:uppercase;font-weight:600;letter-spacing:.5px;margin-bottom:4px}.product-info[_ngcontent-%COMP%]   .product-name[_ngcontent-%COMP%]{font-size:16px;font-weight:600;color:#1a1a1a;margin:0 0 12px;line-height:1.4;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;overflow:hidden}.product-info[_ngcontent-%COMP%]   .price-section[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;margin-bottom:12px}.product-info[_ngcontent-%COMP%]   .price-section[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%]{font-size:18px;font-weight:700;color:#ff6b35}.product-info[_ngcontent-%COMP%]   .price-section[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%]{font-size:14px;color:#999;text-decoration:line-through}.product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;margin-bottom:16px}.product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]{display:flex;gap:2px}.product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:14px;color:#ddd}.product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   ion-icon.filled[_ngcontent-%COMP%]{color:#ffc107}.product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%]   .rating-text[_ngcontent-%COMP%]{font-size:12px;color:#666}.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]{display:flex;gap:8px}.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .cart-btn[_ngcontent-%COMP%]{flex:1;background:linear-gradient(135deg,#ff6b35,#f7931e);color:#fff;border:none;padding:12px 16px;border-radius:8px;font-weight:600;font-size:14px;display:flex;align-items:center;justify-content:center;gap:8px;cursor:pointer;transition:all .3s ease}.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .cart-btn[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 15px #ff6b354d}.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .cart-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:16px}.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .wishlist-btn[_ngcontent-%COMP%]{width:44px;height:44px;border:2px solid #e9ecef;background:#fff;border-radius:8px;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all .3s ease}.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .wishlist-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:20px;color:#666}.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .wishlist-btn[_ngcontent-%COMP%]:hover{border-color:#ff6b35}.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .wishlist-btn[_ngcontent-%COMP%]:hover   ion-icon[_ngcontent-%COMP%]{color:#ff6b35}.empty-container[_ngcontent-%COMP%]{text-align:center;padding:60px 20px}.empty-container[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%]{font-size:64px;color:#ccc;margin-bottom:20px}.empty-container[_ngcontent-%COMP%]   .empty-title[_ngcontent-%COMP%]{font-size:20px;font-weight:600;color:#333;margin-bottom:8px}.empty-container[_ngcontent-%COMP%]   .empty-message[_ngcontent-%COMP%]{font-size:14px;color:#666}@media (max-width: 768px){.trending-products-container[_ngcontent-%COMP%]{padding:16px}.section-header[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start;gap:16px}.section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]{align-self:flex-end}.products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-stage-outer{padding:0 10px}.products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev, .products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next{width:35px!important;height:35px!important;font-size:16px!important}.products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev{left:-15px}.products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next{right:-15px}.section-title[_ngcontent-%COMP%]{font-size:20px}}@media (max-width: 575.98px){.products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-stage-outer{padding:0 5px}.products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev, .products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next{width:30px!important;height:30px!important;font-size:14px!important}.products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev{left:-10px}.products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next{right:-10px}}\"]\n      });\n    }\n  }\n  return TrendingProductsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}