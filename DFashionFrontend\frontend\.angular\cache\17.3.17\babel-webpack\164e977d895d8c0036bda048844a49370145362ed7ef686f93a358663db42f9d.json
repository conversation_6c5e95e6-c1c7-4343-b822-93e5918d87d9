{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashionFrontend/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CommonModule } from '@angular/common';\nimport { Subscription } from 'rxjs';\nimport { IonicModule } from '@ionic/angular';\nimport { CarouselModule } from 'ngx-owl-carousel-o';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/trending.service\";\nimport * as i2 from \"../../../../core/services/social-interactions.service\";\nimport * as i3 from \"../../../../core/services/cart.service\";\nimport * as i4 from \"../../../../core/services/wishlist.service\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@ionic/angular\";\nconst _c0 = () => [1, 2, 3, 4, 5, 6];\nconst _c1 = () => [1, 2, 3, 4, 5];\nfunction NewArrivalsComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_div_1_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleSectionLike());\n    });\n    i0.ɵɵelement(2, \"ion-icon\", 14);\n    i0.ɵɵelementStart(3, \"span\", 15);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_div_1_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openComments());\n    });\n    i0.ɵɵelement(6, \"ion-icon\", 17);\n    i0.ɵɵelementStart(7, \"span\", 15);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_div_1_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.shareSection());\n    });\n    i0.ɵɵelement(10, \"ion-icon\", 19);\n    i0.ɵɵelementStart(11, \"span\", 20);\n    i0.ɵɵtext(12, \"Share\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_div_1_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleSectionBookmark());\n    });\n    i0.ɵɵelement(14, \"ion-icon\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_div_1_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openMusicPlayer());\n    });\n    i0.ɵɵelement(16, \"ion-icon\", 23);\n    i0.ɵɵelementStart(17, \"span\", 20);\n    i0.ɵɵtext(18, \"Music\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"active\", ctx_r1.isSectionLiked);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", ctx_r1.isSectionLiked ? \"heart\" : \"heart-outline\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.formatCount(ctx_r1.sectionLikes));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.formatCount(ctx_r1.sectionComments));\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"active\", ctx_r1.isSectionBookmarked);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", ctx_r1.isSectionBookmarked ? \"bookmark\" : \"bookmark-outline\");\n  }\n}\nfunction NewArrivalsComponent_div_12_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵelement(1, \"div\", 28);\n    i0.ɵɵelementStart(2, \"div\", 29);\n    i0.ɵɵelement(3, \"div\", 30)(4, \"div\", 31)(5, \"div\", 32);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction NewArrivalsComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"div\", 25);\n    i0.ɵɵtemplate(2, NewArrivalsComponent_div_12_div_2_Template, 6, 0, \"div\", 26);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c0));\n  }\n}\nfunction NewArrivalsComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵelement(1, \"ion-icon\", 34);\n    i0.ɵɵelementStart(2, \"p\", 35);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_div_13_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onRetry());\n    });\n    i0.ɵɵelement(5, \"ion-icon\", 37);\n    i0.ɵɵtext(6, \" Try Again \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction NewArrivalsComponent_div_14_div_7_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 70);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r6 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getDiscountPercentage(product_r6), \"% OFF \");\n  }\n}\nfunction NewArrivalsComponent_div_14_div_7_span_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 71);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const product_r6 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.formatPrice(product_r6.originalPrice));\n  }\n}\nfunction NewArrivalsComponent_div_14_div_7_ion_icon_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ion-icon\", 14);\n  }\n  if (rf & 2) {\n    const star_r7 = ctx.$implicit;\n    const product_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵclassProp(\"filled\", star_r7 <= product_r6.rating.average);\n    i0.ɵɵproperty(\"name\", star_r7 <= product_r6.rating.average ? \"star\" : \"star-outline\");\n  }\n}\nfunction NewArrivalsComponent_div_14_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 46);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_div_14_div_7_Template_div_click_0_listener() {\n      const product_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onProductClick(product_r6));\n    });\n    i0.ɵɵelementStart(1, \"div\", 47);\n    i0.ɵɵelement(2, \"img\", 48);\n    i0.ɵɵelementStart(3, \"div\", 49);\n    i0.ɵɵelement(4, \"ion-icon\", 50);\n    i0.ɵɵtext(5, \" New \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 51);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, NewArrivalsComponent_div_14_div_7_div_8_Template, 2, 1, \"div\", 52);\n    i0.ɵɵelementStart(9, \"div\", 53)(10, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_div_14_div_7_Template_button_click_10_listener($event) {\n      const product_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onLikeProduct(product_r6, $event));\n    });\n    i0.ɵɵelement(11, \"ion-icon\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_div_14_div_7_Template_button_click_12_listener($event) {\n      const product_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onShareProduct(product_r6, $event));\n    });\n    i0.ɵɵelement(13, \"ion-icon\", 54);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"div\", 55)(15, \"div\", 56);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"h3\", 57);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 58)(20, \"span\", 59);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(22, NewArrivalsComponent_div_14_div_7_span_22_Template, 2, 1, \"span\", 60);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 61)(24, \"div\", 62);\n    i0.ɵɵtemplate(25, NewArrivalsComponent_div_14_div_7_ion_icon_25_Template, 1, 3, \"ion-icon\", 63);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"span\", 64);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\", 65)(29, \"button\", 66);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_div_14_div_7_Template_button_click_29_listener($event) {\n      const product_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onAddToCart(product_r6, $event));\n    });\n    i0.ɵɵelement(30, \"ion-icon\", 67);\n    i0.ɵɵtext(31, \" Add to Cart \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"button\", 68);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_div_14_div_7_Template_button_click_32_listener($event) {\n      const product_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onAddToWishlist(product_r6, $event));\n    });\n    i0.ɵɵelement(33, \"ion-icon\", 69);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const product_r6 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r1.cardWidth, \"px\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", product_r6.images[0].url, i0.ɵɵsanitizeUrl)(\"alt\", product_r6.images[0].alt || product_r6.name);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getDaysAgo(product_r6.createdAt), \" days ago \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getDiscountPercentage(product_r6) > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"liked\", ctx_r1.isProductLiked(product_r6._id));\n    i0.ɵɵattribute(\"aria-label\", \"Like \" + product_r6.name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", ctx_r1.isProductLiked(product_r6._id) ? \"heart\" : \"heart-outline\");\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"aria-label\", \"Share \" + product_r6.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(product_r6.brand);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r6.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.formatPrice(product_r6.price));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", product_r6.originalPrice && product_r6.originalPrice > product_r6.price);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(17, _c1));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", product_r6.rating.count, \")\");\n  }\n}\nfunction NewArrivalsComponent_div_14_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 72);\n    i0.ɵɵelement(1, \"ion-icon\", 73);\n    i0.ɵɵelementStart(2, \"h3\", 74);\n    i0.ɵɵtext(3, \"No New Arrivals\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 75);\n    i0.ɵɵtext(5, \"Check back soon for fresh new styles\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction NewArrivalsComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵlistener(\"mouseenter\", function NewArrivalsComponent_div_14_Template_div_mouseenter_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.pauseAutoSlide());\n    })(\"mouseleave\", function NewArrivalsComponent_div_14_Template_div_mouseleave_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.resumeAutoSlide());\n    });\n    i0.ɵɵelementStart(1, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_div_14_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.prevSlide());\n    });\n    i0.ɵɵelement(2, \"ion-icon\", 40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function NewArrivalsComponent_div_14_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.nextSlide());\n    });\n    i0.ɵɵelement(4, \"ion-icon\", 8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 42)(6, \"div\", 43);\n    i0.ɵɵtemplate(7, NewArrivalsComponent_div_14_div_7_Template, 34, 18, \"div\", 44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, NewArrivalsComponent_div_14_div_8_Template, 6, 0, \"div\", 45);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.canGoPrev);\n    i0.ɵɵattribute(\"aria-label\", \"Previous products\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.canGoNext);\n    i0.ɵɵattribute(\"aria-label\", \"Next products\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"transform\", \"translateX(-\" + ctx_r1.slideOffset + \"px)\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.newArrivals)(\"ngForTrackBy\", ctx_r1.trackByProductId);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isLoading && !ctx_r1.error && ctx_r1.newArrivals.length === 0);\n  }\n}\nexport class NewArrivalsComponent {\n  constructor(trendingService, socialService, cartService, wishlistService, router) {\n    this.trendingService = trendingService;\n    this.socialService = socialService;\n    this.cartService = cartService;\n    this.wishlistService = wishlistService;\n    this.router = router;\n    this.newArrivals = [];\n    this.isLoading = true;\n    this.error = null;\n    this.likedProducts = new Set();\n    this.subscription = new Subscription();\n    // Slider properties\n    this.currentSlide = 0;\n    this.slideOffset = 0;\n    this.cardWidth = 280;\n    this.visibleCards = 4;\n    this.maxSlide = 0;\n    this.autoSlideDelay = 3500; // 3.5 seconds for new arrivals\n    // Section interaction properties\n    this.isSectionLiked = false;\n    this.isSectionBookmarked = false;\n    this.sectionLikes = 421;\n    this.sectionComments = 156;\n    this.isMobile = false;\n  }\n  ngOnInit() {\n    console.log('NewArrivalsComponent initialized');\n    this.loadNewArrivals();\n    this.subscribeNewArrivals();\n    this.subscribeLikedProducts();\n    this.initializeSlider();\n    this.startAutoSlide();\n    this.checkMobileDevice();\n  }\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n    this.stopAutoSlide();\n  }\n  subscribeNewArrivals() {\n    this.subscription.add(this.trendingService.newArrivals$.subscribe(products => {\n      this.newArrivals = products;\n      this.isLoading = false;\n      this.calculateMaxSlide();\n      this.currentSlide = 0;\n      this.updateSlidePosition();\n    }));\n  }\n  subscribeLikedProducts() {\n    this.subscription.add(this.socialService.likedProducts$.subscribe(likedProducts => {\n      this.likedProducts = likedProducts;\n    }));\n  }\n  loadNewArrivals() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this.isLoading = true;\n        _this.error = null;\n        yield _this.trendingService.loadNewArrivals(1, 6);\n      } catch (error) {\n        console.error('Error loading new arrivals:', error);\n        _this.error = 'Failed to load new arrivals';\n        _this.isLoading = false;\n      }\n    })();\n  }\n  onProductClick(product) {\n    this.router.navigate(['/product', product._id]);\n  }\n  onLikeProduct(product, event) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      event.stopPropagation();\n      try {\n        const result = yield _this2.socialService.likeProduct(product._id);\n        if (result.success) {\n          console.log(result.message);\n        } else {\n          console.error('Failed to like product:', result.message);\n        }\n      } catch (error) {\n        console.error('Error liking product:', error);\n      }\n    })();\n  }\n  onShareProduct(product, event) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      event.stopPropagation();\n      try {\n        const productUrl = `${window.location.origin}/product/${product._id}`;\n        yield navigator.clipboard.writeText(productUrl);\n        yield _this3.socialService.shareProduct(product._id, {\n          platform: 'copy_link',\n          message: `Check out this fresh arrival: ${product.name} from ${product.brand}!`\n        });\n        console.log('Product link copied to clipboard!');\n      } catch (error) {\n        console.error('Error sharing product:', error);\n      }\n    })();\n  }\n  onAddToCart(product, event) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      event.stopPropagation();\n      try {\n        yield _this4.cartService.addToCart(product._id, 1);\n        console.log('Product added to cart!');\n      } catch (error) {\n        console.error('Error adding to cart:', error);\n      }\n    })();\n  }\n  onAddToWishlist(product, event) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      event.stopPropagation();\n      try {\n        yield _this5.wishlistService.addToWishlist(product._id);\n        console.log('Product added to wishlist!');\n      } catch (error) {\n        console.error('Error adding to wishlist:', error);\n      }\n    })();\n  }\n  getDiscountPercentage(product) {\n    if (product.originalPrice && product.originalPrice > product.price) {\n      return Math.round((product.originalPrice - product.price) / product.originalPrice * 100);\n    }\n    return 0;\n  }\n  formatPrice(price) {\n    return new Intl.NumberFormat('en-IN', {\n      style: 'currency',\n      currency: 'INR',\n      minimumFractionDigits: 0\n    }).format(price);\n  }\n  getDaysAgo(createdAt) {\n    const now = new Date();\n    const created = new Date(createdAt);\n    const diffTime = Math.abs(now.getTime() - created.getTime());\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return diffDays;\n  }\n  onRetry() {\n    this.loadNewArrivals();\n  }\n  onViewAll() {\n    this.router.navigate(['/products'], {\n      queryParams: {\n        filter: 'new-arrivals'\n      }\n    });\n  }\n  isProductLiked(productId) {\n    return this.likedProducts.has(productId);\n  }\n  trackByProductId(index, product) {\n    return product._id;\n  }\n  // Slider methods\n  initializeSlider() {\n    this.updateResponsiveSettings();\n    this.calculateMaxSlide();\n    window.addEventListener('resize', () => this.updateResponsiveSettings());\n  }\n  updateResponsiveSettings() {\n    const containerWidth = window.innerWidth;\n    if (containerWidth >= 1200) {\n      this.visibleCards = 4;\n      this.cardWidth = 280;\n    } else if (containerWidth >= 992) {\n      this.visibleCards = 3;\n      this.cardWidth = 260;\n    } else if (containerWidth >= 768) {\n      this.visibleCards = 2;\n      this.cardWidth = 240;\n    } else {\n      this.visibleCards = 1;\n      this.cardWidth = 220;\n    }\n    this.calculateMaxSlide();\n    this.updateSlidePosition();\n  }\n  calculateMaxSlide() {\n    this.maxSlide = Math.max(0, this.newArrivals.length - this.visibleCards);\n  }\n  updateSlidePosition() {\n    this.slideOffset = this.currentSlide * (this.cardWidth + 16); // 16px gap\n  }\n  nextSlide() {\n    if (this.currentSlide < this.maxSlide) {\n      this.currentSlide++;\n      this.updateSlidePosition();\n    }\n  }\n  prevSlide() {\n    if (this.currentSlide > 0) {\n      this.currentSlide--;\n      this.updateSlidePosition();\n    }\n  }\n  startAutoSlide() {\n    this.autoSlideInterval = setInterval(() => {\n      if (this.currentSlide >= this.maxSlide) {\n        this.currentSlide = 0;\n      } else {\n        this.currentSlide++;\n      }\n      this.updateSlidePosition();\n    }, this.autoSlideDelay);\n  }\n  stopAutoSlide() {\n    if (this.autoSlideInterval) {\n      clearInterval(this.autoSlideInterval);\n      this.autoSlideInterval = null;\n    }\n  }\n  pauseAutoSlide() {\n    this.stopAutoSlide();\n  }\n  resumeAutoSlide() {\n    this.startAutoSlide();\n  }\n  get canGoPrev() {\n    return this.currentSlide > 0;\n  }\n  get canGoNext() {\n    return this.currentSlide < this.maxSlide;\n  }\n  // Section interaction methods\n  toggleSectionLike() {\n    this.isSectionLiked = !this.isSectionLiked;\n    if (this.isSectionLiked) {\n      this.sectionLikes++;\n    } else {\n      this.sectionLikes--;\n    }\n  }\n  toggleSectionBookmark() {\n    this.isSectionBookmarked = !this.isSectionBookmarked;\n  }\n  openComments() {\n    console.log('Opening comments for new arrivals section');\n  }\n  shareSection() {\n    if (navigator.share) {\n      navigator.share({\n        title: 'New Arrivals',\n        text: 'Check out these fresh new fashion arrivals!',\n        url: window.location.href\n      });\n    } else {\n      navigator.clipboard.writeText(window.location.href);\n      console.log('Link copied to clipboard');\n    }\n  }\n  openMusicPlayer() {\n    console.log('Opening music player for new arrivals');\n  }\n  formatCount(count) {\n    if (count >= 1000000) {\n      return (count / 1000000).toFixed(1) + 'M';\n    } else if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'K';\n    }\n    return count.toString();\n  }\n  checkMobileDevice() {\n    this.isMobile = window.innerWidth <= 768;\n  }\n  static {\n    this.ɵfac = function NewArrivalsComponent_Factory(t) {\n      return new (t || NewArrivalsComponent)(i0.ɵɵdirectiveInject(i1.TrendingService), i0.ɵɵdirectiveInject(i2.SocialInteractionsService), i0.ɵɵdirectiveInject(i3.CartService), i0.ɵɵdirectiveInject(i4.WishlistService), i0.ɵɵdirectiveInject(i5.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: NewArrivalsComponent,\n      selectors: [[\"app-new-arrivals\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 15,\n      vars: 4,\n      consts: [[1, \"new-arrivals-container\"], [\"class\", \"mobile-action-buttons\", 4, \"ngIf\"], [1, \"section-header\"], [1, \"header-content\"], [1, \"section-title\"], [\"name\", \"sparkles\", 1, \"title-icon\"], [1, \"section-subtitle\"], [1, \"view-all-btn\", 3, \"click\"], [\"name\", \"chevron-forward\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"products-slider-container\", 3, \"mouseenter\", \"mouseleave\", 4, \"ngIf\"], [1, \"mobile-action-buttons\"], [1, \"action-btn\", \"like-btn\", 3, \"click\"], [3, \"name\"], [1, \"action-count\"], [1, \"action-btn\", \"comment-btn\", 3, \"click\"], [\"name\", \"chatbubble-outline\"], [1, \"action-btn\", \"share-btn\", 3, \"click\"], [\"name\", \"arrow-redo-outline\"], [1, \"action-text\"], [1, \"action-btn\", \"bookmark-btn\", 3, \"click\"], [1, \"action-btn\", \"music-btn\", 3, \"click\"], [\"name\", \"musical-notes\"], [1, \"loading-container\"], [1, \"loading-grid\"], [\"class\", \"loading-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"loading-card\"], [1, \"loading-image\"], [1, \"loading-content\"], [1, \"loading-line\", \"short\"], [1, \"loading-line\", \"medium\"], [1, \"loading-line\", \"long\"], [1, \"error-container\"], [\"name\", \"alert-circle\", 1, \"error-icon\"], [1, \"error-message\"], [1, \"retry-btn\", 3, \"click\"], [\"name\", \"refresh\"], [1, \"products-slider-container\", 3, \"mouseenter\", \"mouseleave\"], [1, \"nav-btn\", \"prev-btn\", 3, \"click\", \"disabled\"], [\"name\", \"chevron-back\"], [1, \"nav-btn\", \"next-btn\", 3, \"click\", \"disabled\"], [1, \"products-slider-wrapper\"], [1, \"products-slider\"], [\"class\", \"product-card\", 3, \"width\", \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"empty-container\", 4, \"ngIf\"], [1, \"product-card\", 3, \"click\"], [1, \"product-image-container\"], [\"loading\", \"lazy\", 1, \"product-image\", 3, \"src\", \"alt\"], [1, \"new-badge\"], [\"name\", \"sparkles\"], [1, \"days-badge\"], [\"class\", \"discount-badge\", 4, \"ngIf\"], [1, \"action-buttons\"], [\"name\", \"share-outline\"], [1, \"product-info\"], [1, \"product-brand\"], [1, \"product-name\"], [1, \"price-section\"], [1, \"current-price\"], [\"class\", \"original-price\", 4, \"ngIf\"], [1, \"rating-section\"], [1, \"stars\"], [3, \"name\", \"filled\", 4, \"ngFor\", \"ngForOf\"], [1, \"rating-text\"], [1, \"product-actions\"], [1, \"cart-btn\", 3, \"click\"], [\"name\", \"bag-add-outline\"], [1, \"wishlist-btn\", 3, \"click\"], [\"name\", \"heart-outline\"], [1, \"discount-badge\"], [1, \"original-price\"], [1, \"empty-container\"], [\"name\", \"sparkles-outline\", 1, \"empty-icon\"], [1, \"empty-title\"], [1, \"empty-message\"]],\n      template: function NewArrivalsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, NewArrivalsComponent_div_1_Template, 19, 8, \"div\", 1);\n          i0.ɵɵelementStart(2, \"div\", 2)(3, \"div\", 3)(4, \"h2\", 4);\n          i0.ɵɵelement(5, \"ion-icon\", 5);\n          i0.ɵɵtext(6, \" New Arrivals \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"p\", 6);\n          i0.ɵɵtext(8, \"Fresh styles just landed\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"button\", 7);\n          i0.ɵɵlistener(\"click\", function NewArrivalsComponent_Template_button_click_9_listener() {\n            return ctx.onViewAll();\n          });\n          i0.ɵɵtext(10, \" View All \");\n          i0.ɵɵelement(11, \"ion-icon\", 8);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(12, NewArrivalsComponent_div_12_Template, 3, 2, \"div\", 9)(13, NewArrivalsComponent_div_13_Template, 7, 1, \"div\", 10)(14, NewArrivalsComponent_div_14_Template, 9, 9, \"div\", 11);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isMobile);\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.error && !ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.newArrivals.length > 0);\n        }\n      },\n      dependencies: [CommonModule, i6.NgForOf, i6.NgIf, IonicModule, i7.IonIcon, CarouselModule],\n      styles: [\".new-arrivals-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  border-radius: 16px;\\n  margin-bottom: 24px;\\n  position: relative;\\n}\\n\\n.mobile-action-buttons[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 15px;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  display: flex;\\n  flex-direction: column;\\n  gap: 20px;\\n  z-index: 10;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%] {\\n  width: 48px;\\n  height: 48px;\\n  border-radius: 50%;\\n  border: none;\\n  background: rgba(255, 255, 255, 0.2);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  color: white;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  position: relative;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  margin-bottom: 2px;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   .action-count[_ngcontent-%COMP%], .mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   .action-text[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n  font-weight: 600;\\n  line-height: 1;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1);\\n  background: rgba(255, 255, 255, 0.3);\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.9);\\n  color: #667eea;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active.like-btn[_ngcontent-%COMP%] {\\n  color: #ff3040;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active.like-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_heartBeat 0.6s ease-in-out;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active.bookmark-btn[_ngcontent-%COMP%] {\\n  color: #ffd700;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.like-btn.active[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  color: #ff3040;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.music-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ff3040 0%, #667eea 100%);\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.music-btn[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1) rotate(15deg);\\n}\\n\\n@keyframes _ngcontent-%COMP%_heartBeat {\\n  0% {\\n    transform: scale(1);\\n  }\\n  25% {\\n    transform: scale(1.3);\\n  }\\n  50% {\\n    transform: scale(1.1);\\n  }\\n  75% {\\n    transform: scale(1.25);\\n  }\\n  100% {\\n    transform: scale(1);\\n  }\\n}\\n@media (min-width: 769px) {\\n  .mobile-action-buttons[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n.section-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 24px;\\n}\\n.section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 700;\\n  color: white;\\n  margin: 0 0 8px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   .title-icon[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  color: #ffd700;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-subtitle[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: rgba(255, 255, 255, 0.8);\\n  margin: 0;\\n}\\n.section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  color: white;\\n  border: 2px solid rgba(255, 255, 255, 0.3);\\n  padding: 12px 20px;\\n  border-radius: 25px;\\n  font-weight: 600;\\n  font-size: 14px;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3);\\n  border-color: rgba(255, 255, 255, 0.5);\\n  transform: translateY(-2px);\\n}\\n.section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n\\n.loading-container[_ngcontent-%COMP%]   .loading-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\\n  gap: 20px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 16px;\\n  overflow: hidden;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 200px;\\n  background: rgba(255, 255, 255, 0.2);\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%] {\\n  padding: 16px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line[_ngcontent-%COMP%] {\\n  height: 12px;\\n  background: rgba(255, 255, 255, 0.2);\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n  border-radius: 6px;\\n  margin-bottom: 8px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.short[_ngcontent-%COMP%] {\\n  width: 40%;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.medium[_ngcontent-%COMP%] {\\n  width: 60%;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.long[_ngcontent-%COMP%] {\\n  width: 80%;\\n}\\n\\n@keyframes _ngcontent-%COMP%_loading {\\n  0%, 100% {\\n    opacity: 0.6;\\n  }\\n  50% {\\n    opacity: 1;\\n  }\\n}\\n.error-container[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 40px 20px;\\n}\\n.error-container[_ngcontent-%COMP%]   .error-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  color: #ff6b6b;\\n  margin-bottom: 16px;\\n}\\n.error-container[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: rgba(255, 255, 255, 0.8);\\n  margin-bottom: 20px;\\n}\\n.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  color: white;\\n  border: 2px solid rgba(255, 255, 255, 0.3);\\n  padding: 12px 24px;\\n  border-radius: 8px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin: 0 auto;\\n  transition: all 0.3s ease;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3);\\n  border-color: rgba(255, 255, 255, 0.5);\\n}\\n\\n.products-slider-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  overflow: hidden;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  z-index: 10;\\n  background: rgba(0, 0, 0, 0.7);\\n  color: white;\\n  border: none;\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: rgba(0, 0, 0, 0.9);\\n  transform: translateY(-50%) scale(1.1);\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.3;\\n  cursor: not-allowed;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .nav-btn.prev-btn[_ngcontent-%COMP%] {\\n  left: -20px;\\n}\\n.products-slider-container[_ngcontent-%COMP%]   .nav-btn.next-btn[_ngcontent-%COMP%] {\\n  right: -20px;\\n}\\n\\n.products-slider-wrapper[_ngcontent-%COMP%] {\\n  overflow: hidden;\\n  padding: 0 10px;\\n}\\n\\n.products-slider[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  transition: transform 0.3s ease;\\n  will-change: transform;\\n}\\n\\n.product-card[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 16px;\\n  overflow: hidden;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n  flex-shrink: 0;\\n  min-width: 280px;\\n}\\n.product-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-8px);\\n  background: rgba(255, 255, 255, 0.15);\\n  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);\\n}\\n.product-card[_ngcontent-%COMP%]:hover   .action-buttons[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  transform: translateY(0);\\n}\\n\\n.product-image-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  overflow: hidden;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .product-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 200px;\\n  object-fit: cover;\\n  transition: transform 0.3s ease;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .new-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 12px;\\n  left: 12px;\\n  background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);\\n  color: #333;\\n  padding: 6px 12px;\\n  border-radius: 20px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .new-badge[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .days-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50px;\\n  left: 12px;\\n  background: rgba(255, 255, 255, 0.9);\\n  color: #333;\\n  padding: 4px 8px;\\n  border-radius: 12px;\\n  font-size: 10px;\\n  font-weight: 600;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .discount-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 12px;\\n  right: 12px;\\n  background: #dc3545;\\n  color: white;\\n  padding: 6px 10px;\\n  border-radius: 12px;\\n  font-size: 12px;\\n  font-weight: 700;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  right: 12px;\\n  transform: translateY(-50%);\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n  opacity: 0;\\n  transition: all 0.3s ease;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  border: none;\\n  background: rgba(255, 255, 255, 0.9);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  color: #333;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:hover {\\n  background: white;\\n  transform: scale(1.1);\\n}\\n.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn.like-btn[_ngcontent-%COMP%]:hover   ion-icon[_ngcontent-%COMP%] {\\n  color: #dc3545;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn.like-btn.liked[_ngcontent-%COMP%] {\\n  background: rgba(220, 53, 69, 0.15);\\n}\\n.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn.like-btn.liked[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  color: #dc3545;\\n}\\n.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn.share-btn[_ngcontent-%COMP%]:hover   ion-icon[_ngcontent-%COMP%] {\\n  color: #007bff;\\n}\\n\\n.product-info[_ngcontent-%COMP%] {\\n  padding: 16px;\\n}\\n.product-info[_ngcontent-%COMP%]   .product-brand[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: rgba(255, 255, 255, 0.7);\\n  text-transform: uppercase;\\n  font-weight: 600;\\n  letter-spacing: 0.5px;\\n  margin-bottom: 4px;\\n}\\n.product-info[_ngcontent-%COMP%]   .product-name[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: white;\\n  margin: 0 0 12px 0;\\n  line-height: 1.4;\\n  display: -webkit-box;\\n  -webkit-line-clamp: 2;\\n  -webkit-box-orient: vertical;\\n  overflow: hidden;\\n}\\n.product-info[_ngcontent-%COMP%]   .price-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin-bottom: 12px;\\n}\\n.product-info[_ngcontent-%COMP%]   .price-section[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 700;\\n  color: #ffd700;\\n}\\n.product-info[_ngcontent-%COMP%]   .price-section[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: rgba(255, 255, 255, 0.6);\\n  text-decoration: line-through;\\n}\\n.product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin-bottom: 16px;\\n}\\n.product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 2px;\\n}\\n.product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: rgba(255, 255, 255, 0.3);\\n}\\n.product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   ion-icon.filled[_ngcontent-%COMP%] {\\n  color: #ffd700;\\n}\\n.product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%]   .rating-text[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: rgba(255, 255, 255, 0.6);\\n}\\n.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .cart-btn[_ngcontent-%COMP%] {\\n  flex: 1;\\n  background: rgba(255, 255, 255, 0.2);\\n  color: white;\\n  border: 2px solid rgba(255, 255, 255, 0.3);\\n  padding: 12px 16px;\\n  border-radius: 8px;\\n  font-weight: 600;\\n  font-size: 14px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 8px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .cart-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3);\\n  border-color: rgba(255, 255, 255, 0.5);\\n  transform: translateY(-2px);\\n}\\n.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .cart-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .wishlist-btn[_ngcontent-%COMP%] {\\n  width: 44px;\\n  height: 44px;\\n  border: 2px solid rgba(255, 255, 255, 0.3);\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 8px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .wishlist-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  color: rgba(255, 255, 255, 0.8);\\n}\\n.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .wishlist-btn[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.2);\\n  border-color: rgba(255, 255, 255, 0.5);\\n}\\n.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .wishlist-btn[_ngcontent-%COMP%]:hover   ion-icon[_ngcontent-%COMP%] {\\n  color: #ffd700;\\n}\\n\\n.empty-container[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 60px 20px;\\n}\\n.empty-container[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%] {\\n  font-size: 64px;\\n  color: rgba(255, 255, 255, 0.4);\\n  margin-bottom: 20px;\\n}\\n.empty-container[_ngcontent-%COMP%]   .empty-title[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: white;\\n  margin-bottom: 8px;\\n}\\n.empty-container[_ngcontent-%COMP%]   .empty-message[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: rgba(255, 255, 255, 0.7);\\n}\\n\\n@media (max-width: 768px) {\\n  .new-arrivals-container[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .section-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 16px;\\n  }\\n  .section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%] {\\n    align-self: flex-end;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]   .nav-btn.prev-btn[_ngcontent-%COMP%] {\\n    left: -15px;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]   .nav-btn.next-btn[_ngcontent-%COMP%] {\\n    right: -15px;\\n  }\\n  .product-card[_ngcontent-%COMP%] {\\n    min-width: 250px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .products-slider-container[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%] {\\n    width: 35px;\\n    height: 35px;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]   .nav-btn.prev-btn[_ngcontent-%COMP%] {\\n    left: -10px;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]   .nav-btn.next-btn[_ngcontent-%COMP%] {\\n    right: -10px;\\n  }\\n  .products-slider-container[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n  }\\n  .products-slider-wrapper[_ngcontent-%COMP%] {\\n    padding: 0 5px;\\n  }\\n  .products-slider[_ngcontent-%COMP%] {\\n    gap: 12px;\\n  }\\n  .product-card[_ngcontent-%COMP%] {\\n    min-width: 220px;\\n  }\\n  .section-title[_ngcontent-%COMP%] {\\n    font-size: 20px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "Subscription", "IonicModule", "CarouselModule", "i0", "ɵɵelementStart", "ɵɵlistener", "NewArrivalsComponent_div_1_Template_button_click_1_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "toggleSectionLike", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "NewArrivalsComponent_div_1_Template_button_click_5_listener", "openComments", "NewArrivalsComponent_div_1_Template_button_click_9_listener", "shareSection", "NewArrivalsComponent_div_1_Template_button_click_13_listener", "toggleSectionBookmark", "NewArrivalsComponent_div_1_Template_button_click_15_listener", "openMusicPlayer", "ɵɵadvance", "ɵɵclassProp", "isSectionLiked", "ɵɵproperty", "ɵɵtextInterpolate", "formatCount", "sectionLikes", "sectionComments", "isSectionBookmarked", "ɵɵtemplate", "NewArrivalsComponent_div_12_div_2_Template", "ɵɵpureFunction0", "_c0", "NewArrivalsComponent_div_13_Template_button_click_4_listener", "_r3", "onRetry", "error", "ɵɵtextInterpolate1", "getDiscountPercentage", "product_r6", "formatPrice", "originalPrice", "star_r7", "rating", "average", "NewArrivalsComponent_div_14_div_7_Template_div_click_0_listener", "_r5", "$implicit", "onProductClick", "NewArrivalsComponent_div_14_div_7_div_8_Template", "NewArrivalsComponent_div_14_div_7_Template_button_click_10_listener", "$event", "onLikeProduct", "NewArrivalsComponent_div_14_div_7_Template_button_click_12_listener", "onShareProduct", "NewArrivalsComponent_div_14_div_7_span_22_Template", "NewArrivalsComponent_div_14_div_7_ion_icon_25_Template", "NewArrivalsComponent_div_14_div_7_Template_button_click_29_listener", "onAddToCart", "NewArrivalsComponent_div_14_div_7_Template_button_click_32_listener", "onAddToWishlist", "ɵɵstyleProp", "<PERSON><PERSON><PERSON><PERSON>", "images", "url", "ɵɵsanitizeUrl", "alt", "name", "getDaysAgo", "createdAt", "isProductLiked", "_id", "brand", "price", "_c1", "count", "NewArrivalsComponent_div_14_Template_div_mouseenter_0_listener", "_r4", "pauseAutoSlide", "NewArrivalsComponent_div_14_Template_div_mouseleave_0_listener", "resumeAutoSlide", "NewArrivalsComponent_div_14_Template_button_click_1_listener", "prevSlide", "NewArrivalsComponent_div_14_Template_button_click_3_listener", "nextSlide", "NewArrivalsComponent_div_14_div_7_Template", "NewArrivalsComponent_div_14_div_8_Template", "canGoPrev", "canGoNext", "slideOffset", "newArrivals", "trackByProductId", "isLoading", "length", "NewArrivalsComponent", "constructor", "trendingService", "socialService", "cartService", "wishlistService", "router", "likedProducts", "Set", "subscription", "currentSlide", "visibleCards", "maxSlide", "autoSlideDelay", "isMobile", "ngOnInit", "console", "log", "loadNewArrivals", "subscribeNewArrivals", "subscribeLikedProducts", "initializeSlider", "startAutoSlide", "checkMobileDevice", "ngOnDestroy", "unsubscribe", "stopAutoSlide", "add", "newArrivals$", "subscribe", "products", "calculateMaxSlide", "updateSlidePosition", "likedProducts$", "_this", "_asyncToGenerator", "product", "navigate", "event", "_this2", "stopPropagation", "result", "likeProduct", "success", "message", "_this3", "productUrl", "window", "location", "origin", "navigator", "clipboard", "writeText", "shareProduct", "platform", "_this4", "addToCart", "_this5", "addToWishlist", "Math", "round", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "format", "now", "Date", "created", "diffTime", "abs", "getTime", "diffDays", "ceil", "onViewAll", "queryParams", "filter", "productId", "has", "index", "updateResponsiveSettings", "addEventListener", "containerWidth", "innerWidth", "max", "autoSlideInterval", "setInterval", "clearInterval", "share", "title", "text", "href", "toFixed", "toString", "ɵɵdirectiveInject", "i1", "TrendingService", "i2", "SocialInteractionsService", "i3", "CartService", "i4", "WishlistService", "i5", "Router", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "NewArrivalsComponent_Template", "rf", "ctx", "NewArrivalsComponent_div_1_Template", "NewArrivalsComponent_Template_button_click_9_listener", "NewArrivalsComponent_div_12_Template", "NewArrivalsComponent_div_13_Template", "NewArrivalsComponent_div_14_Template", "i6", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i7", "IonIcon", "styles"], "sources": ["E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\features\\home\\components\\new-arrivals\\new-arrivals.component.ts", "E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\features\\home\\components\\new-arrivals\\new-arrivals.component.html"], "sourcesContent": ["import { Component, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { Router } from '@angular/router';\r\nimport { Subscription } from 'rxjs';\r\nimport { TrendingService } from '../../../../core/services/trending.service';\r\nimport { Product } from '../../../../core/models/product.model';\r\nimport { SocialInteractionsService } from '../../../../core/services/social-interactions.service';\r\nimport { CartService } from '../../../../core/services/cart.service';\r\nimport { WishlistService } from '../../../../core/services/wishlist.service';\r\nimport { IonicModule } from '@ionic/angular';\r\nimport { CarouselModule } from 'ngx-owl-carousel-o';\r\n\r\n@Component({\r\n  selector: 'app-new-arrivals',\r\n  standalone: true,\r\n  imports: [CommonModule, IonicModule, CarouselModule],\r\n  templateUrl: './new-arrivals.component.html',\r\n  styleUrls: ['./new-arrivals.component.scss']\r\n})\r\nexport class NewArrivalsComponent implements OnInit, OnDestroy {\r\n  newArrivals: Product[] = [];\r\n  isLoading = true;\r\n  error: string | null = null;\r\n  likedProducts = new Set<string>();\r\n  private subscription: Subscription = new Subscription();\r\n\r\n  // Slider properties\r\n  currentSlide = 0;\r\n  slideOffset = 0;\r\n  cardWidth = 280;\r\n  visibleCards = 4;\r\n  maxSlide = 0;\r\n  autoSlideInterval: any;\r\n  autoSlideDelay = 3500; // 3.5 seconds for new arrivals\r\n\r\n  // Section interaction properties\r\n  isSectionLiked = false;\r\n  isSectionBookmarked = false;\r\n  sectionLikes = 421;\r\n  sectionComments = 156;\r\n  isMobile = false;\r\n\r\n  constructor(\r\n    private trendingService: TrendingService,\r\n    private socialService: SocialInteractionsService,\r\n    private cartService: CartService,\r\n    private wishlistService: WishlistService,\r\n    private router: Router\r\n  ) {}\r\n\r\n  ngOnInit() {\r\n    console.log('NewArrivalsComponent initialized');\r\n    this.loadNewArrivals();\r\n    this.subscribeNewArrivals();\r\n    this.subscribeLikedProducts();\r\n    this.initializeSlider();\r\n    this.startAutoSlide();\r\n    this.checkMobileDevice();\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.subscription.unsubscribe();\r\n    this.stopAutoSlide();\r\n  }\r\n\r\n  private subscribeNewArrivals() {\r\n    this.subscription.add(\r\n      this.trendingService.newArrivals$.subscribe(products => {\r\n        this.newArrivals = products;\r\n        this.isLoading = false;\r\n        this.calculateMaxSlide();\r\n        this.currentSlide = 0;\r\n        this.updateSlidePosition();\r\n      })\r\n    );\r\n  }\r\n\r\n  private subscribeLikedProducts() {\r\n    this.subscription.add(\r\n      this.socialService.likedProducts$.subscribe(likedProducts => {\r\n        this.likedProducts = likedProducts;\r\n      })\r\n    );\r\n  }\r\n\r\n  private async loadNewArrivals() {\r\n    try {\r\n      this.isLoading = true;\r\n      this.error = null;\r\n      await this.trendingService.loadNewArrivals(1, 6);\r\n    } catch (error) {\r\n      console.error('Error loading new arrivals:', error);\r\n      this.error = 'Failed to load new arrivals';\r\n      this.isLoading = false;\r\n    }\r\n  }\r\n\r\n  onProductClick(product: Product) {\r\n    this.router.navigate(['/product', product._id]);\r\n  }\r\n\r\n  async onLikeProduct(product: Product, event: Event) {\r\n    event.stopPropagation();\r\n    try {\r\n      const result = await this.socialService.likeProduct(product._id);\r\n      if (result.success) {\r\n        console.log(result.message);\r\n      } else {\r\n        console.error('Failed to like product:', result.message);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error liking product:', error);\r\n    }\r\n  }\r\n\r\n  async onShareProduct(product: Product, event: Event) {\r\n    event.stopPropagation();\r\n    try {\r\n      const productUrl = `${window.location.origin}/product/${product._id}`;\r\n      await navigator.clipboard.writeText(productUrl);\r\n\r\n      await this.socialService.shareProduct(product._id, {\r\n        platform: 'copy_link',\r\n        message: `Check out this fresh arrival: ${product.name} from ${product.brand}!`\r\n      });\r\n\r\n      console.log('Product link copied to clipboard!');\r\n    } catch (error) {\r\n      console.error('Error sharing product:', error);\r\n    }\r\n  }\r\n\r\n  async onAddToCart(product: Product, event: Event) {\r\n    event.stopPropagation();\r\n    try {\r\n      await this.cartService.addToCart(product._id, 1);\r\n      console.log('Product added to cart!');\r\n    } catch (error) {\r\n      console.error('Error adding to cart:', error);\r\n    }\r\n  }\r\n\r\n  async onAddToWishlist(product: Product, event: Event) {\r\n    event.stopPropagation();\r\n    try {\r\n      await this.wishlistService.addToWishlist(product._id);\r\n      console.log('Product added to wishlist!');\r\n    } catch (error) {\r\n      console.error('Error adding to wishlist:', error);\r\n    }\r\n  }\r\n\r\n  getDiscountPercentage(product: Product): number {\r\n    if (product.originalPrice && product.originalPrice > product.price) {\r\n      return Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100);\r\n    }\r\n    return 0;\r\n  }\r\n\r\n  formatPrice(price: number): string {\r\n    return new Intl.NumberFormat('en-IN', {\r\n      style: 'currency',\r\n      currency: 'INR',\r\n      minimumFractionDigits: 0\r\n    }).format(price);\r\n  }\r\n\r\n  getDaysAgo(createdAt: Date): number {\r\n    const now = new Date();\r\n    const created = new Date(createdAt);\r\n    const diffTime = Math.abs(now.getTime() - created.getTime());\r\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\r\n    return diffDays;\r\n  }\r\n\r\n  onRetry() {\r\n    this.loadNewArrivals();\r\n  }\r\n\r\n  onViewAll() {\r\n    this.router.navigate(['/products'], { \r\n      queryParams: { filter: 'new-arrivals' } \r\n    });\r\n  }\r\n\r\n  isProductLiked(productId: string): boolean {\r\n    return this.likedProducts.has(productId);\r\n  }\r\n\r\n  trackByProductId(index: number, product: Product): string {\r\n    return product._id;\r\n  }\r\n\r\n  // Slider methods\r\n  private initializeSlider() {\r\n    this.updateResponsiveSettings();\r\n    this.calculateMaxSlide();\r\n    window.addEventListener('resize', () => this.updateResponsiveSettings());\r\n  }\r\n\r\n  private updateResponsiveSettings() {\r\n    const containerWidth = window.innerWidth;\r\n\r\n    if (containerWidth >= 1200) {\r\n      this.visibleCards = 4;\r\n      this.cardWidth = 280;\r\n    } else if (containerWidth >= 992) {\r\n      this.visibleCards = 3;\r\n      this.cardWidth = 260;\r\n    } else if (containerWidth >= 768) {\r\n      this.visibleCards = 2;\r\n      this.cardWidth = 240;\r\n    } else {\r\n      this.visibleCards = 1;\r\n      this.cardWidth = 220;\r\n    }\r\n\r\n    this.calculateMaxSlide();\r\n    this.updateSlidePosition();\r\n  }\r\n\r\n  private calculateMaxSlide() {\r\n    this.maxSlide = Math.max(0, this.newArrivals.length - this.visibleCards);\r\n  }\r\n\r\n  private updateSlidePosition() {\r\n    this.slideOffset = this.currentSlide * (this.cardWidth + 16); // 16px gap\r\n  }\r\n\r\n  nextSlide() {\r\n    if (this.currentSlide < this.maxSlide) {\r\n      this.currentSlide++;\r\n      this.updateSlidePosition();\r\n    }\r\n  }\r\n\r\n  prevSlide() {\r\n    if (this.currentSlide > 0) {\r\n      this.currentSlide--;\r\n      this.updateSlidePosition();\r\n    }\r\n  }\r\n\r\n  private startAutoSlide() {\r\n    this.autoSlideInterval = setInterval(() => {\r\n      if (this.currentSlide >= this.maxSlide) {\r\n        this.currentSlide = 0;\r\n      } else {\r\n        this.currentSlide++;\r\n      }\r\n      this.updateSlidePosition();\r\n    }, this.autoSlideDelay);\r\n  }\r\n\r\n  private stopAutoSlide() {\r\n    if (this.autoSlideInterval) {\r\n      clearInterval(this.autoSlideInterval);\r\n      this.autoSlideInterval = null;\r\n    }\r\n  }\r\n\r\n  pauseAutoSlide() {\r\n    this.stopAutoSlide();\r\n  }\r\n\r\n  resumeAutoSlide() {\r\n    this.startAutoSlide();\r\n  }\r\n\r\n  get canGoPrev(): boolean {\r\n    return this.currentSlide > 0;\r\n  }\r\n\r\n  get canGoNext(): boolean {\r\n    return this.currentSlide < this.maxSlide;\r\n  }\r\n\r\n  // Section interaction methods\r\n  toggleSectionLike() {\r\n    this.isSectionLiked = !this.isSectionLiked;\r\n    if (this.isSectionLiked) {\r\n      this.sectionLikes++;\r\n    } else {\r\n      this.sectionLikes--;\r\n    }\r\n  }\r\n\r\n  toggleSectionBookmark() {\r\n    this.isSectionBookmarked = !this.isSectionBookmarked;\r\n  }\r\n\r\n  openComments() {\r\n    console.log('Opening comments for new arrivals section');\r\n  }\r\n\r\n  shareSection() {\r\n    if (navigator.share) {\r\n      navigator.share({\r\n        title: 'New Arrivals',\r\n        text: 'Check out these fresh new fashion arrivals!',\r\n        url: window.location.href\r\n      });\r\n    } else {\r\n      navigator.clipboard.writeText(window.location.href);\r\n      console.log('Link copied to clipboard');\r\n    }\r\n  }\r\n\r\n  openMusicPlayer() {\r\n    console.log('Opening music player for new arrivals');\r\n  }\r\n\r\n  formatCount(count: number): string {\r\n    if (count >= 1000000) {\r\n      return (count / 1000000).toFixed(1) + 'M';\r\n    } else if (count >= 1000) {\r\n      return (count / 1000).toFixed(1) + 'K';\r\n    }\r\n    return count.toString();\r\n  }\r\n\r\n  private checkMobileDevice() {\r\n    this.isMobile = window.innerWidth <= 768;\r\n  }\r\n}\r\n", "<div class=\"new-arrivals-container\">\r\n  <!-- Mobile Action Buttons (TikTok/Instagram Style) -->\r\n  <div class=\"mobile-action-buttons\" *ngIf=\"isMobile\">\r\n    <button class=\"action-btn like-btn\"\r\n            [class.active]=\"isSectionLiked\"\r\n            (click)=\"toggleSectionLike()\">\r\n      <ion-icon [name]=\"isSectionLiked ? 'heart' : 'heart-outline'\"></ion-icon>\r\n      <span class=\"action-count\">{{ formatCount(sectionLikes) }}</span>\r\n    </button>\r\n\r\n    <button class=\"action-btn comment-btn\" (click)=\"openComments()\">\r\n      <ion-icon name=\"chatbubble-outline\"></ion-icon>\r\n      <span class=\"action-count\">{{ formatCount(sectionComments) }}</span>\r\n    </button>\r\n\r\n    <button class=\"action-btn share-btn\" (click)=\"shareSection()\">\r\n      <ion-icon name=\"arrow-redo-outline\"></ion-icon>\r\n      <span class=\"action-text\">Share</span>\r\n    </button>\r\n\r\n    <button class=\"action-btn bookmark-btn\"\r\n            [class.active]=\"isSectionBookmarked\"\r\n            (click)=\"toggleSectionBookmark()\">\r\n      <ion-icon [name]=\"isSectionBookmarked ? 'bookmark' : 'bookmark-outline'\"></ion-icon>\r\n    </button>\r\n\r\n    <button class=\"action-btn music-btn\" (click)=\"openMusicPlayer()\">\r\n      <ion-icon name=\"musical-notes\"></ion-icon>\r\n      <span class=\"action-text\">Music</span>\r\n    </button>\r\n  </div>\r\n\r\n  <!-- Header -->\r\n  <div class=\"section-header\">\r\n    <div class=\"header-content\">\r\n      <h2 class=\"section-title\">\r\n        <ion-icon name=\"sparkles\" class=\"title-icon\"></ion-icon>\r\n        New Arrivals\r\n      </h2>\r\n      <p class=\"section-subtitle\">Fresh styles just landed</p>\r\n    </div>\r\n    <button class=\"view-all-btn\" (click)=\"onViewAll()\">\r\n      View All\r\n      <ion-icon name=\"chevron-forward\"></ion-icon>\r\n    </button>\r\n  </div>\r\n\r\n  <!-- Loading State -->\r\n  <div *ngIf=\"isLoading\" class=\"loading-container\">\r\n    <div class=\"loading-grid\">\r\n      <div *ngFor=\"let item of [1,2,3,4,5,6]\" class=\"loading-card\">\r\n        <div class=\"loading-image\"></div>\r\n        <div class=\"loading-content\">\r\n          <div class=\"loading-line short\"></div>\r\n          <div class=\"loading-line medium\"></div>\r\n          <div class=\"loading-line long\"></div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Error State -->\r\n  <div *ngIf=\"error && !isLoading\" class=\"error-container\">\r\n    <ion-icon name=\"alert-circle\" class=\"error-icon\"></ion-icon>\r\n    <p class=\"error-message\">{{ error }}</p>\r\n    <button class=\"retry-btn\" (click)=\"onRetry()\">\r\n      <ion-icon name=\"refresh\"></ion-icon>\r\n      Try Again\r\n    </button>\r\n  </div>\r\n\r\n  <!-- Products Slider -->\r\n  <div *ngIf=\"!isLoading && !error && newArrivals.length > 0\" class=\"products-slider-container\"\r\n       (mouseenter)=\"pauseAutoSlide()\" (mouseleave)=\"resumeAutoSlide()\">\r\n\r\n    <!-- Navigation Buttons -->\r\n    <button class=\"nav-btn prev-btn\"\r\n            [disabled]=\"!canGoPrev\"\r\n            (click)=\"prevSlide()\"\r\n            [attr.aria-label]=\"'Previous products'\">\r\n      <ion-icon name=\"chevron-back\"></ion-icon>\r\n    </button>\r\n\r\n    <button class=\"nav-btn next-btn\"\r\n            [disabled]=\"!canGoNext\"\r\n            (click)=\"nextSlide()\"\r\n            [attr.aria-label]=\"'Next products'\">\r\n      <ion-icon name=\"chevron-forward\"></ion-icon>\r\n    </button>\r\n\r\n    <div class=\"products-slider-wrapper\">\r\n      <div class=\"products-slider\"\r\n           [style.transform]=\"'translateX(-' + slideOffset + 'px)'\">\r\n        <div\r\n          *ngFor=\"let product of newArrivals; trackBy: trackByProductId\"\r\n          class=\"product-card\"\r\n          [style.width.px]=\"cardWidth\"\r\n          (click)=\"onProductClick(product)\"\r\n        >\r\n      <!-- Product Image -->\r\n      <div class=\"product-image-container\">\r\n        <img \r\n          [src]=\"product.images[0].url\"\r\n          [alt]=\"product.images[0].alt || product.name\"\r\n          class=\"product-image\"\r\n          loading=\"lazy\"\r\n        />\r\n        \r\n        <!-- New Badge -->\r\n        <div class=\"new-badge\">\r\n          <ion-icon name=\"sparkles\"></ion-icon>\r\n          New\r\n        </div>\r\n\r\n        <!-- Days Badge -->\r\n        <div class=\"days-badge\">\r\n          {{ getDaysAgo(product.createdAt) }} days ago\r\n        </div>\r\n\r\n        <!-- Discount Badge -->\r\n        <div *ngIf=\"getDiscountPercentage(product) > 0\" class=\"discount-badge\">\r\n          {{ getDiscountPercentage(product) }}% OFF\r\n        </div>\r\n\r\n        <!-- Action Buttons -->\r\n        <div class=\"action-buttons\">\r\n          <button\r\n            class=\"action-btn like-btn\"\r\n            [class.liked]=\"isProductLiked(product._id)\"\r\n            (click)=\"onLikeProduct(product, $event)\"\r\n            [attr.aria-label]=\"'Like ' + product.name\"\r\n          >\r\n            <ion-icon [name]=\"isProductLiked(product._id) ? 'heart' : 'heart-outline'\"></ion-icon>\r\n          </button>\r\n          <button \r\n            class=\"action-btn share-btn\" \r\n            (click)=\"onShareProduct(product, $event)\"\r\n            [attr.aria-label]=\"'Share ' + product.name\"\r\n          >\r\n            <ion-icon name=\"share-outline\"></ion-icon>\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Product Info -->\r\n      <div class=\"product-info\">\r\n        <div class=\"product-brand\">{{ product.brand }}</div>\r\n        <h3 class=\"product-name\">{{ product.name }}</h3>\r\n        \r\n        <!-- Price Section -->\r\n        <div class=\"price-section\">\r\n          <span class=\"current-price\">{{ formatPrice(product.price) }}</span>\r\n          <span *ngIf=\"product.originalPrice && product.originalPrice > product.price\" \r\n                class=\"original-price\">{{ formatPrice(product.originalPrice) }}</span>\r\n        </div>\r\n\r\n        <!-- Rating -->\r\n        <div class=\"rating-section\">\r\n          <div class=\"stars\">\r\n            <ion-icon \r\n              *ngFor=\"let star of [1,2,3,4,5]\" \r\n              [name]=\"star <= product.rating.average ? 'star' : 'star-outline'\"\r\n              [class.filled]=\"star <= product.rating.average\"\r\n            ></ion-icon>\r\n          </div>\r\n          <span class=\"rating-text\">({{ product.rating.count }})</span>\r\n        </div>\r\n\r\n        <!-- Action Buttons -->\r\n        <div class=\"product-actions\">\r\n          <button \r\n            class=\"cart-btn\" \r\n            (click)=\"onAddToCart(product, $event)\"\r\n          >\r\n            <ion-icon name=\"bag-add-outline\"></ion-icon>\r\n            Add to Cart\r\n          </button>\r\n          <button \r\n            class=\"wishlist-btn\" \r\n            (click)=\"onAddToWishlist(product, $event)\"\r\n          >\r\n            <ion-icon name=\"heart-outline\"></ion-icon>\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Empty State -->\r\n  <div *ngIf=\"!isLoading && !error && newArrivals.length === 0\" class=\"empty-container\">\r\n    <ion-icon name=\"sparkles-outline\" class=\"empty-icon\"></ion-icon>\r\n    <h3 class=\"empty-title\">No New Arrivals</h3>\r\n    <p class=\"empty-message\">Check back soon for fresh new styles</p>\r\n  </div>\r\n</div>\r\n"], "mappings": ";AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,YAAY,QAAQ,MAAM;AAMnC,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,cAAc,QAAQ,oBAAoB;;;;;;;;;;;;;;ICP/CC,EADF,CAAAC,cAAA,cAAoD,iBAGZ;IAA9BD,EAAA,CAAAE,UAAA,mBAAAC,4DAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,iBAAA,EAAmB;IAAA,EAAC;IACnCT,EAAA,CAAAU,SAAA,mBAAyE;IACzEV,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAW,MAAA,GAA+B;IAC5DX,EAD4D,CAAAY,YAAA,EAAO,EAC1D;IAETZ,EAAA,CAAAC,cAAA,iBAAgE;IAAzBD,EAAA,CAAAE,UAAA,mBAAAW,4DAAA;MAAAb,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAQ,YAAA,EAAc;IAAA,EAAC;IAC7Dd,EAAA,CAAAU,SAAA,mBAA+C;IAC/CV,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAW,MAAA,GAAkC;IAC/DX,EAD+D,CAAAY,YAAA,EAAO,EAC7D;IAETZ,EAAA,CAAAC,cAAA,iBAA8D;IAAzBD,EAAA,CAAAE,UAAA,mBAAAa,4DAAA;MAAAf,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAU,YAAA,EAAc;IAAA,EAAC;IAC3DhB,EAAA,CAAAU,SAAA,oBAA+C;IAC/CV,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAW,MAAA,aAAK;IACjCX,EADiC,CAAAY,YAAA,EAAO,EAC/B;IAETZ,EAAA,CAAAC,cAAA,kBAE0C;IAAlCD,EAAA,CAAAE,UAAA,mBAAAe,6DAAA;MAAAjB,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAY,qBAAA,EAAuB;IAAA,EAAC;IACvClB,EAAA,CAAAU,SAAA,oBAAoF;IACtFV,EAAA,CAAAY,YAAA,EAAS;IAETZ,EAAA,CAAAC,cAAA,kBAAiE;IAA5BD,EAAA,CAAAE,UAAA,mBAAAiB,6DAAA;MAAAnB,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAc,eAAA,EAAiB;IAAA,EAAC;IAC9DpB,EAAA,CAAAU,SAAA,oBAA0C;IAC1CV,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAW,MAAA,aAAK;IAEnCX,EAFmC,CAAAY,YAAA,EAAO,EAC/B,EACL;;;;IA1BIZ,EAAA,CAAAqB,SAAA,EAA+B;IAA/BrB,EAAA,CAAAsB,WAAA,WAAAhB,MAAA,CAAAiB,cAAA,CAA+B;IAE3BvB,EAAA,CAAAqB,SAAA,EAAmD;IAAnDrB,EAAA,CAAAwB,UAAA,SAAAlB,MAAA,CAAAiB,cAAA,6BAAmD;IAClCvB,EAAA,CAAAqB,SAAA,GAA+B;IAA/BrB,EAAA,CAAAyB,iBAAA,CAAAnB,MAAA,CAAAoB,WAAA,CAAApB,MAAA,CAAAqB,YAAA,EAA+B;IAK/B3B,EAAA,CAAAqB,SAAA,GAAkC;IAAlCrB,EAAA,CAAAyB,iBAAA,CAAAnB,MAAA,CAAAoB,WAAA,CAAApB,MAAA,CAAAsB,eAAA,EAAkC;IASvD5B,EAAA,CAAAqB,SAAA,GAAoC;IAApCrB,EAAA,CAAAsB,WAAA,WAAAhB,MAAA,CAAAuB,mBAAA,CAAoC;IAEhC7B,EAAA,CAAAqB,SAAA,EAA8D;IAA9DrB,EAAA,CAAAwB,UAAA,SAAAlB,MAAA,CAAAuB,mBAAA,mCAA8D;;;;;IA2BxE7B,EAAA,CAAAC,cAAA,cAA6D;IAC3DD,EAAA,CAAAU,SAAA,cAAiC;IACjCV,EAAA,CAAAC,cAAA,cAA6B;IAG3BD,EAFA,CAAAU,SAAA,cAAsC,cACC,cACF;IAEzCV,EADE,CAAAY,YAAA,EAAM,EACF;;;;;IARRZ,EADF,CAAAC,cAAA,cAAiD,cACrB;IACxBD,EAAA,CAAA8B,UAAA,IAAAC,0CAAA,kBAA6D;IASjE/B,EADE,CAAAY,YAAA,EAAM,EACF;;;IAToBZ,EAAA,CAAAqB,SAAA,GAAgB;IAAhBrB,EAAA,CAAAwB,UAAA,YAAAxB,EAAA,CAAAgC,eAAA,IAAAC,GAAA,EAAgB;;;;;;IAY1CjC,EAAA,CAAAC,cAAA,cAAyD;IACvDD,EAAA,CAAAU,SAAA,mBAA4D;IAC5DV,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAW,MAAA,GAAW;IAAAX,EAAA,CAAAY,YAAA,EAAI;IACxCZ,EAAA,CAAAC,cAAA,iBAA8C;IAApBD,EAAA,CAAAE,UAAA,mBAAAgC,6DAAA;MAAAlC,EAAA,CAAAI,aAAA,CAAA+B,GAAA;MAAA,MAAA7B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA8B,OAAA,EAAS;IAAA,EAAC;IAC3CpC,EAAA,CAAAU,SAAA,mBAAoC;IACpCV,EAAA,CAAAW,MAAA,kBACF;IACFX,EADE,CAAAY,YAAA,EAAS,EACL;;;;IALqBZ,EAAA,CAAAqB,SAAA,GAAW;IAAXrB,EAAA,CAAAyB,iBAAA,CAAAnB,MAAA,CAAA+B,KAAA,CAAW;;;;;IAwDhCrC,EAAA,CAAAC,cAAA,cAAuE;IACrED,EAAA,CAAAW,MAAA,GACF;IAAAX,EAAA,CAAAY,YAAA,EAAM;;;;;IADJZ,EAAA,CAAAqB,SAAA,EACF;IADErB,EAAA,CAAAsC,kBAAA,MAAAhC,MAAA,CAAAiC,qBAAA,CAAAC,UAAA,YACF;;;;;IA8BExC,EAAA,CAAAC,cAAA,eAC6B;IAAAD,EAAA,CAAAW,MAAA,GAAwC;IAAAX,EAAA,CAAAY,YAAA,EAAO;;;;;IAA/CZ,EAAA,CAAAqB,SAAA,EAAwC;IAAxCrB,EAAA,CAAAyB,iBAAA,CAAAnB,MAAA,CAAAmC,WAAA,CAAAD,UAAA,CAAAE,aAAA,EAAwC;;;;;IAMnE1C,EAAA,CAAAU,SAAA,mBAIY;;;;;IADVV,EAAA,CAAAsB,WAAA,WAAAqB,OAAA,IAAAH,UAAA,CAAAI,MAAA,CAAAC,OAAA,CAA+C;IAD/C7C,EAAA,CAAAwB,UAAA,SAAAmB,OAAA,IAAAH,UAAA,CAAAI,MAAA,CAAAC,OAAA,2BAAiE;;;;;;IApEvE7C,EAAA,CAAAC,cAAA,cAKC;IADCD,EAAA,CAAAE,UAAA,mBAAA4C,gEAAA;MAAA,MAAAN,UAAA,GAAAxC,EAAA,CAAAI,aAAA,CAAA2C,GAAA,EAAAC,SAAA;MAAA,MAAA1C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA2C,cAAA,CAAAT,UAAA,CAAuB;IAAA,EAAC;IAGrCxC,EAAA,CAAAC,cAAA,cAAqC;IACnCD,EAAA,CAAAU,SAAA,cAKE;IAGFV,EAAA,CAAAC,cAAA,cAAuB;IACrBD,EAAA,CAAAU,SAAA,mBAAqC;IACrCV,EAAA,CAAAW,MAAA,YACF;IAAAX,EAAA,CAAAY,YAAA,EAAM;IAGNZ,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAW,MAAA,GACF;IAAAX,EAAA,CAAAY,YAAA,EAAM;IAGNZ,EAAA,CAAA8B,UAAA,IAAAoB,gDAAA,kBAAuE;IAMrElD,EADF,CAAAC,cAAA,cAA4B,kBAMzB;IAFCD,EAAA,CAAAE,UAAA,mBAAAiD,oEAAAC,MAAA;MAAA,MAAAZ,UAAA,GAAAxC,EAAA,CAAAI,aAAA,CAAA2C,GAAA,EAAAC,SAAA;MAAA,MAAA1C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA+C,aAAA,CAAAb,UAAA,EAAAY,MAAA,CAA8B;IAAA,EAAC;IAGxCpD,EAAA,CAAAU,SAAA,oBAAsF;IACxFV,EAAA,CAAAY,YAAA,EAAS;IACTZ,EAAA,CAAAC,cAAA,kBAIC;IAFCD,EAAA,CAAAE,UAAA,mBAAAoD,oEAAAF,MAAA;MAAA,MAAAZ,UAAA,GAAAxC,EAAA,CAAAI,aAAA,CAAA2C,GAAA,EAAAC,SAAA;MAAA,MAAA1C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAiD,cAAA,CAAAf,UAAA,EAAAY,MAAA,CAA+B;IAAA,EAAC;IAGzCpD,EAAA,CAAAU,SAAA,oBAA0C;IAGhDV,EAFI,CAAAY,YAAA,EAAS,EACL,EACF;IAIJZ,EADF,CAAAC,cAAA,eAA0B,eACG;IAAAD,EAAA,CAAAW,MAAA,IAAmB;IAAAX,EAAA,CAAAY,YAAA,EAAM;IACpDZ,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAW,MAAA,IAAkB;IAAAX,EAAA,CAAAY,YAAA,EAAK;IAI9CZ,EADF,CAAAC,cAAA,eAA2B,gBACG;IAAAD,EAAA,CAAAW,MAAA,IAAgC;IAAAX,EAAA,CAAAY,YAAA,EAAO;IACnEZ,EAAA,CAAA8B,UAAA,KAAA0B,kDAAA,mBAC6B;IAC/BxD,EAAA,CAAAY,YAAA,EAAM;IAIJZ,EADF,CAAAC,cAAA,eAA4B,eACP;IACjBD,EAAA,CAAA8B,UAAA,KAAA2B,sDAAA,uBAIC;IACHzD,EAAA,CAAAY,YAAA,EAAM;IACNZ,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAW,MAAA,IAA4B;IACxDX,EADwD,CAAAY,YAAA,EAAO,EACzD;IAIJZ,EADF,CAAAC,cAAA,eAA6B,kBAI1B;IADCD,EAAA,CAAAE,UAAA,mBAAAwD,oEAAAN,MAAA;MAAA,MAAAZ,UAAA,GAAAxC,EAAA,CAAAI,aAAA,CAAA2C,GAAA,EAAAC,SAAA;MAAA,MAAA1C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAqD,WAAA,CAAAnB,UAAA,EAAAY,MAAA,CAA4B;IAAA,EAAC;IAEtCpD,EAAA,CAAAU,SAAA,oBAA4C;IAC5CV,EAAA,CAAAW,MAAA,qBACF;IAAAX,EAAA,CAAAY,YAAA,EAAS;IACTZ,EAAA,CAAAC,cAAA,kBAGC;IADCD,EAAA,CAAAE,UAAA,mBAAA0D,oEAAAR,MAAA;MAAA,MAAAZ,UAAA,GAAAxC,EAAA,CAAAI,aAAA,CAAA2C,GAAA,EAAAC,SAAA;MAAA,MAAA1C,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAuD,eAAA,CAAArB,UAAA,EAAAY,MAAA,CAAgC;IAAA,EAAC;IAE1CpD,EAAA,CAAAU,SAAA,oBAA0C;IAIlDV,EAHM,CAAAY,YAAA,EAAS,EACL,EACF,EACF;;;;;IAzFAZ,EAAA,CAAA8D,WAAA,UAAAxD,MAAA,CAAAyD,SAAA,OAA4B;IAM5B/D,EAAA,CAAAqB,SAAA,GAA6B;IAC7BrB,EADA,CAAAwB,UAAA,QAAAgB,UAAA,CAAAwB,MAAA,IAAAC,GAAA,EAAAjE,EAAA,CAAAkE,aAAA,CAA6B,QAAA1B,UAAA,CAAAwB,MAAA,IAAAG,GAAA,IAAA3B,UAAA,CAAA4B,IAAA,CACgB;IAa7CpE,EAAA,CAAAqB,SAAA,GACF;IADErB,EAAA,CAAAsC,kBAAA,MAAAhC,MAAA,CAAA+D,UAAA,CAAA7B,UAAA,CAAA8B,SAAA,gBACF;IAGMtE,EAAA,CAAAqB,SAAA,EAAwC;IAAxCrB,EAAA,CAAAwB,UAAA,SAAAlB,MAAA,CAAAiC,qBAAA,CAAAC,UAAA,MAAwC;IAQ1CxC,EAAA,CAAAqB,SAAA,GAA2C;IAA3CrB,EAAA,CAAAsB,WAAA,UAAAhB,MAAA,CAAAiE,cAAA,CAAA/B,UAAA,CAAAgC,GAAA,EAA2C;;IAIjCxE,EAAA,CAAAqB,SAAA,EAAgE;IAAhErB,EAAA,CAAAwB,UAAA,SAAAlB,MAAA,CAAAiE,cAAA,CAAA/B,UAAA,CAAAgC,GAAA,8BAAgE;IAK1ExE,EAAA,CAAAqB,SAAA,EAA2C;;IASpBrB,EAAA,CAAAqB,SAAA,GAAmB;IAAnBrB,EAAA,CAAAyB,iBAAA,CAAAe,UAAA,CAAAiC,KAAA,CAAmB;IACrBzE,EAAA,CAAAqB,SAAA,GAAkB;IAAlBrB,EAAA,CAAAyB,iBAAA,CAAAe,UAAA,CAAA4B,IAAA,CAAkB;IAIbpE,EAAA,CAAAqB,SAAA,GAAgC;IAAhCrB,EAAA,CAAAyB,iBAAA,CAAAnB,MAAA,CAAAmC,WAAA,CAAAD,UAAA,CAAAkC,KAAA,EAAgC;IACrD1E,EAAA,CAAAqB,SAAA,EAAoE;IAApErB,EAAA,CAAAwB,UAAA,SAAAgB,UAAA,CAAAE,aAAA,IAAAF,UAAA,CAAAE,aAAA,GAAAF,UAAA,CAAAkC,KAAA,CAAoE;IAQtD1E,EAAA,CAAAqB,SAAA,GAAc;IAAdrB,EAAA,CAAAwB,UAAA,YAAAxB,EAAA,CAAAgC,eAAA,KAAA2C,GAAA,EAAc;IAKT3E,EAAA,CAAAqB,SAAA,GAA4B;IAA5BrB,EAAA,CAAAsC,kBAAA,MAAAE,UAAA,CAAAI,MAAA,CAAAgC,KAAA,MAA4B;;;;;IAwB9D5E,EAAA,CAAAC,cAAA,cAAsF;IACpFD,EAAA,CAAAU,SAAA,mBAAgE;IAChEV,EAAA,CAAAC,cAAA,aAAwB;IAAAD,EAAA,CAAAW,MAAA,sBAAe;IAAAX,EAAA,CAAAY,YAAA,EAAK;IAC5CZ,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAW,MAAA,2CAAoC;IAC/DX,EAD+D,CAAAY,YAAA,EAAI,EAC7D;;;;;;IAzHNZ,EAAA,CAAAC,cAAA,cACsE;IAAjCD,EAAhC,CAAAE,UAAA,wBAAA2E,+DAAA;MAAA7E,EAAA,CAAAI,aAAA,CAAA0E,GAAA;MAAA,MAAAxE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAcF,MAAA,CAAAyE,cAAA,EAAgB;IAAA,EAAC,wBAAAC,+DAAA;MAAAhF,EAAA,CAAAI,aAAA,CAAA0E,GAAA;MAAA,MAAAxE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAeF,MAAA,CAAA2E,eAAA,EAAiB;IAAA,EAAC;IAGnEjF,EAAA,CAAAC,cAAA,iBAGgD;IADxCD,EAAA,CAAAE,UAAA,mBAAAgF,6DAAA;MAAAlF,EAAA,CAAAI,aAAA,CAAA0E,GAAA;MAAA,MAAAxE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA6E,SAAA,EAAW;IAAA,EAAC;IAE3BnF,EAAA,CAAAU,SAAA,mBAAyC;IAC3CV,EAAA,CAAAY,YAAA,EAAS;IAETZ,EAAA,CAAAC,cAAA,iBAG4C;IADpCD,EAAA,CAAAE,UAAA,mBAAAkF,6DAAA;MAAApF,EAAA,CAAAI,aAAA,CAAA0E,GAAA;MAAA,MAAAxE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA+E,SAAA,EAAW;IAAA,EAAC;IAE3BrF,EAAA,CAAAU,SAAA,kBAA4C;IAC9CV,EAAA,CAAAY,YAAA,EAAS;IAGPZ,EADF,CAAAC,cAAA,cAAqC,cAE2B;IAC5DD,EAAA,CAAA8B,UAAA,IAAAwD,0CAAA,oBAKC;IAwFPtF,EAAA,CAAAY,YAAA,EAAM;IAGNZ,EAAA,CAAA8B,UAAA,IAAAyD,0CAAA,kBAAsF;IAKxFvF,EAAA,CAAAY,YAAA,EAAM,EAzHkE;;;;IAI5DZ,EAAA,CAAAqB,SAAA,EAAuB;IAAvBrB,EAAA,CAAAwB,UAAA,cAAAlB,MAAA,CAAAkF,SAAA,CAAuB;;IAOvBxF,EAAA,CAAAqB,SAAA,GAAuB;IAAvBrB,EAAA,CAAAwB,UAAA,cAAAlB,MAAA,CAAAmF,SAAA,CAAuB;;IAQxBzF,EAAA,CAAAqB,SAAA,GAAwD;IAAxDrB,EAAA,CAAA8D,WAAA,+BAAAxD,MAAA,CAAAoF,WAAA,SAAwD;IAErC1F,EAAA,CAAAqB,SAAA,EAAgB;IAAArB,EAAhB,CAAAwB,UAAA,YAAAlB,MAAA,CAAAqF,WAAA,CAAgB,iBAAArF,MAAA,CAAAsF,gBAAA,CAAyB;IA+F/D5F,EAAA,CAAAqB,SAAA,EAAsD;IAAtDrB,EAAA,CAAAwB,UAAA,UAAAlB,MAAA,CAAAuF,SAAA,KAAAvF,MAAA,CAAA+B,KAAA,IAAA/B,MAAA,CAAAqF,WAAA,CAAAG,MAAA,OAAsD;;;AD1K9D,OAAM,MAAOC,oBAAoB;EAuB/BC,YACUC,eAAgC,EAChCC,aAAwC,EACxCC,WAAwB,EACxBC,eAAgC,EAChCC,MAAc;IAJd,KAAAJ,eAAe,GAAfA,eAAe;IACf,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,MAAM,GAANA,MAAM;IA3BhB,KAAAV,WAAW,GAAc,EAAE;IAC3B,KAAAE,SAAS,GAAG,IAAI;IAChB,KAAAxD,KAAK,GAAkB,IAAI;IAC3B,KAAAiE,aAAa,GAAG,IAAIC,GAAG,EAAU;IACzB,KAAAC,YAAY,GAAiB,IAAI3G,YAAY,EAAE;IAEvD;IACA,KAAA4G,YAAY,GAAG,CAAC;IAChB,KAAAf,WAAW,GAAG,CAAC;IACf,KAAA3B,SAAS,GAAG,GAAG;IACf,KAAA2C,YAAY,GAAG,CAAC;IAChB,KAAAC,QAAQ,GAAG,CAAC;IAEZ,KAAAC,cAAc,GAAG,IAAI,CAAC,CAAC;IAEvB;IACA,KAAArF,cAAc,GAAG,KAAK;IACtB,KAAAM,mBAAmB,GAAG,KAAK;IAC3B,KAAAF,YAAY,GAAG,GAAG;IAClB,KAAAC,eAAe,GAAG,GAAG;IACrB,KAAAiF,QAAQ,GAAG,KAAK;EAQb;EAEHC,QAAQA,CAAA;IACNC,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;IAC/C,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,sBAAsB,EAAE;IAC7B,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,cAAc,EAAE;IACrB,IAAI,CAACC,iBAAiB,EAAE;EAC1B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACf,YAAY,CAACgB,WAAW,EAAE;IAC/B,IAAI,CAACC,aAAa,EAAE;EACtB;EAEQP,oBAAoBA,CAAA;IAC1B,IAAI,CAACV,YAAY,CAACkB,GAAG,CACnB,IAAI,CAACzB,eAAe,CAAC0B,YAAY,CAACC,SAAS,CAACC,QAAQ,IAAG;MACrD,IAAI,CAAClC,WAAW,GAAGkC,QAAQ;MAC3B,IAAI,CAAChC,SAAS,GAAG,KAAK;MACtB,IAAI,CAACiC,iBAAiB,EAAE;MACxB,IAAI,CAACrB,YAAY,GAAG,CAAC;MACrB,IAAI,CAACsB,mBAAmB,EAAE;IAC5B,CAAC,CAAC,CACH;EACH;EAEQZ,sBAAsBA,CAAA;IAC5B,IAAI,CAACX,YAAY,CAACkB,GAAG,CACnB,IAAI,CAACxB,aAAa,CAAC8B,cAAc,CAACJ,SAAS,CAACtB,aAAa,IAAG;MAC1D,IAAI,CAACA,aAAa,GAAGA,aAAa;IACpC,CAAC,CAAC,CACH;EACH;EAEcW,eAAeA,CAAA;IAAA,IAAAgB,KAAA;IAAA,OAAAC,iBAAA;MAC3B,IAAI;QACFD,KAAI,CAACpC,SAAS,GAAG,IAAI;QACrBoC,KAAI,CAAC5F,KAAK,GAAG,IAAI;QACjB,MAAM4F,KAAI,CAAChC,eAAe,CAACgB,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC;OACjD,CAAC,OAAO5E,KAAK,EAAE;QACd0E,OAAO,CAAC1E,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnD4F,KAAI,CAAC5F,KAAK,GAAG,6BAA6B;QAC1C4F,KAAI,CAACpC,SAAS,GAAG,KAAK;;IACvB;EACH;EAEA5C,cAAcA,CAACkF,OAAgB;IAC7B,IAAI,CAAC9B,MAAM,CAAC+B,QAAQ,CAAC,CAAC,UAAU,EAAED,OAAO,CAAC3D,GAAG,CAAC,CAAC;EACjD;EAEMnB,aAAaA,CAAC8E,OAAgB,EAAEE,KAAY;IAAA,IAAAC,MAAA;IAAA,OAAAJ,iBAAA;MAChDG,KAAK,CAACE,eAAe,EAAE;MACvB,IAAI;QACF,MAAMC,MAAM,SAASF,MAAI,CAACpC,aAAa,CAACuC,WAAW,CAACN,OAAO,CAAC3D,GAAG,CAAC;QAChE,IAAIgE,MAAM,CAACE,OAAO,EAAE;UAClB3B,OAAO,CAACC,GAAG,CAACwB,MAAM,CAACG,OAAO,CAAC;SAC5B,MAAM;UACL5B,OAAO,CAAC1E,KAAK,CAAC,yBAAyB,EAAEmG,MAAM,CAACG,OAAO,CAAC;;OAE3D,CAAC,OAAOtG,KAAK,EAAE;QACd0E,OAAO,CAAC1E,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;;IAC9C;EACH;EAEMkB,cAAcA,CAAC4E,OAAgB,EAAEE,KAAY;IAAA,IAAAO,MAAA;IAAA,OAAAV,iBAAA;MACjDG,KAAK,CAACE,eAAe,EAAE;MACvB,IAAI;QACF,MAAMM,UAAU,GAAG,GAAGC,MAAM,CAACC,QAAQ,CAACC,MAAM,YAAYb,OAAO,CAAC3D,GAAG,EAAE;QACrE,MAAMyE,SAAS,CAACC,SAAS,CAACC,SAAS,CAACN,UAAU,CAAC;QAE/C,MAAMD,MAAI,CAAC1C,aAAa,CAACkD,YAAY,CAACjB,OAAO,CAAC3D,GAAG,EAAE;UACjD6E,QAAQ,EAAE,WAAW;UACrBV,OAAO,EAAE,iCAAiCR,OAAO,CAAC/D,IAAI,SAAS+D,OAAO,CAAC1D,KAAK;SAC7E,CAAC;QAEFsC,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;OACjD,CAAC,OAAO3E,KAAK,EAAE;QACd0E,OAAO,CAAC1E,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;;IAC/C;EACH;EAEMsB,WAAWA,CAACwE,OAAgB,EAAEE,KAAY;IAAA,IAAAiB,MAAA;IAAA,OAAApB,iBAAA;MAC9CG,KAAK,CAACE,eAAe,EAAE;MACvB,IAAI;QACF,MAAMe,MAAI,CAACnD,WAAW,CAACoD,SAAS,CAACpB,OAAO,CAAC3D,GAAG,EAAE,CAAC,CAAC;QAChDuC,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;OACtC,CAAC,OAAO3E,KAAK,EAAE;QACd0E,OAAO,CAAC1E,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;;IAC9C;EACH;EAEMwB,eAAeA,CAACsE,OAAgB,EAAEE,KAAY;IAAA,IAAAmB,MAAA;IAAA,OAAAtB,iBAAA;MAClDG,KAAK,CAACE,eAAe,EAAE;MACvB,IAAI;QACF,MAAMiB,MAAI,CAACpD,eAAe,CAACqD,aAAa,CAACtB,OAAO,CAAC3D,GAAG,CAAC;QACrDuC,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;OAC1C,CAAC,OAAO3E,KAAK,EAAE;QACd0E,OAAO,CAAC1E,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;;IAClD;EACH;EAEAE,qBAAqBA,CAAC4F,OAAgB;IACpC,IAAIA,OAAO,CAACzF,aAAa,IAAIyF,OAAO,CAACzF,aAAa,GAAGyF,OAAO,CAACzD,KAAK,EAAE;MAClE,OAAOgF,IAAI,CAACC,KAAK,CAAE,CAACxB,OAAO,CAACzF,aAAa,GAAGyF,OAAO,CAACzD,KAAK,IAAIyD,OAAO,CAACzF,aAAa,GAAI,GAAG,CAAC;;IAE5F,OAAO,CAAC;EACV;EAEAD,WAAWA,CAACiC,KAAa;IACvB,OAAO,IAAIkF,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE;KACxB,CAAC,CAACC,MAAM,CAACvF,KAAK,CAAC;EAClB;EAEAL,UAAUA,CAACC,SAAe;IACxB,MAAM4F,GAAG,GAAG,IAAIC,IAAI,EAAE;IACtB,MAAMC,OAAO,GAAG,IAAID,IAAI,CAAC7F,SAAS,CAAC;IACnC,MAAM+F,QAAQ,GAAGX,IAAI,CAACY,GAAG,CAACJ,GAAG,CAACK,OAAO,EAAE,GAAGH,OAAO,CAACG,OAAO,EAAE,CAAC;IAC5D,MAAMC,QAAQ,GAAGd,IAAI,CAACe,IAAI,CAACJ,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAC5D,OAAOG,QAAQ;EACjB;EAEApI,OAAOA,CAAA;IACL,IAAI,CAAC6E,eAAe,EAAE;EACxB;EAEAyD,SAASA,CAAA;IACP,IAAI,CAACrE,MAAM,CAAC+B,QAAQ,CAAC,CAAC,WAAW,CAAC,EAAE;MAClCuC,WAAW,EAAE;QAAEC,MAAM,EAAE;MAAc;KACtC,CAAC;EACJ;EAEArG,cAAcA,CAACsG,SAAiB;IAC9B,OAAO,IAAI,CAACvE,aAAa,CAACwE,GAAG,CAACD,SAAS,CAAC;EAC1C;EAEAjF,gBAAgBA,CAACmF,KAAa,EAAE5C,OAAgB;IAC9C,OAAOA,OAAO,CAAC3D,GAAG;EACpB;EAEA;EACQ4C,gBAAgBA,CAAA;IACtB,IAAI,CAAC4D,wBAAwB,EAAE;IAC/B,IAAI,CAAClD,iBAAiB,EAAE;IACxBgB,MAAM,CAACmC,gBAAgB,CAAC,QAAQ,EAAE,MAAM,IAAI,CAACD,wBAAwB,EAAE,CAAC;EAC1E;EAEQA,wBAAwBA,CAAA;IAC9B,MAAME,cAAc,GAAGpC,MAAM,CAACqC,UAAU;IAExC,IAAID,cAAc,IAAI,IAAI,EAAE;MAC1B,IAAI,CAACxE,YAAY,GAAG,CAAC;MACrB,IAAI,CAAC3C,SAAS,GAAG,GAAG;KACrB,MAAM,IAAImH,cAAc,IAAI,GAAG,EAAE;MAChC,IAAI,CAACxE,YAAY,GAAG,CAAC;MACrB,IAAI,CAAC3C,SAAS,GAAG,GAAG;KACrB,MAAM,IAAImH,cAAc,IAAI,GAAG,EAAE;MAChC,IAAI,CAACxE,YAAY,GAAG,CAAC;MACrB,IAAI,CAAC3C,SAAS,GAAG,GAAG;KACrB,MAAM;MACL,IAAI,CAAC2C,YAAY,GAAG,CAAC;MACrB,IAAI,CAAC3C,SAAS,GAAG,GAAG;;IAGtB,IAAI,CAAC+D,iBAAiB,EAAE;IACxB,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEQD,iBAAiBA,CAAA;IACvB,IAAI,CAACnB,QAAQ,GAAG+C,IAAI,CAAC0B,GAAG,CAAC,CAAC,EAAE,IAAI,CAACzF,WAAW,CAACG,MAAM,GAAG,IAAI,CAACY,YAAY,CAAC;EAC1E;EAEQqB,mBAAmBA,CAAA;IACzB,IAAI,CAACrC,WAAW,GAAG,IAAI,CAACe,YAAY,IAAI,IAAI,CAAC1C,SAAS,GAAG,EAAE,CAAC,CAAC,CAAC;EAChE;EAEAsB,SAASA,CAAA;IACP,IAAI,IAAI,CAACoB,YAAY,GAAG,IAAI,CAACE,QAAQ,EAAE;MACrC,IAAI,CAACF,YAAY,EAAE;MACnB,IAAI,CAACsB,mBAAmB,EAAE;;EAE9B;EAEA5C,SAASA,CAAA;IACP,IAAI,IAAI,CAACsB,YAAY,GAAG,CAAC,EAAE;MACzB,IAAI,CAACA,YAAY,EAAE;MACnB,IAAI,CAACsB,mBAAmB,EAAE;;EAE9B;EAEQV,cAAcA,CAAA;IACpB,IAAI,CAACgE,iBAAiB,GAAGC,WAAW,CAAC,MAAK;MACxC,IAAI,IAAI,CAAC7E,YAAY,IAAI,IAAI,CAACE,QAAQ,EAAE;QACtC,IAAI,CAACF,YAAY,GAAG,CAAC;OACtB,MAAM;QACL,IAAI,CAACA,YAAY,EAAE;;MAErB,IAAI,CAACsB,mBAAmB,EAAE;IAC5B,CAAC,EAAE,IAAI,CAACnB,cAAc,CAAC;EACzB;EAEQa,aAAaA,CAAA;IACnB,IAAI,IAAI,CAAC4D,iBAAiB,EAAE;MAC1BE,aAAa,CAAC,IAAI,CAACF,iBAAiB,CAAC;MACrC,IAAI,CAACA,iBAAiB,GAAG,IAAI;;EAEjC;EAEAtG,cAAcA,CAAA;IACZ,IAAI,CAAC0C,aAAa,EAAE;EACtB;EAEAxC,eAAeA,CAAA;IACb,IAAI,CAACoC,cAAc,EAAE;EACvB;EAEA,IAAI7B,SAASA,CAAA;IACX,OAAO,IAAI,CAACiB,YAAY,GAAG,CAAC;EAC9B;EAEA,IAAIhB,SAASA,CAAA;IACX,OAAO,IAAI,CAACgB,YAAY,GAAG,IAAI,CAACE,QAAQ;EAC1C;EAEA;EACAlG,iBAAiBA,CAAA;IACf,IAAI,CAACc,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAC1C,IAAI,IAAI,CAACA,cAAc,EAAE;MACvB,IAAI,CAACI,YAAY,EAAE;KACpB,MAAM;MACL,IAAI,CAACA,YAAY,EAAE;;EAEvB;EAEAT,qBAAqBA,CAAA;IACnB,IAAI,CAACW,mBAAmB,GAAG,CAAC,IAAI,CAACA,mBAAmB;EACtD;EAEAf,YAAYA,CAAA;IACViG,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;EAC1D;EAEAhG,YAAYA,CAAA;IACV,IAAIiI,SAAS,CAACuC,KAAK,EAAE;MACnBvC,SAAS,CAACuC,KAAK,CAAC;QACdC,KAAK,EAAE,cAAc;QACrBC,IAAI,EAAE,6CAA6C;QACnDzH,GAAG,EAAE6E,MAAM,CAACC,QAAQ,CAAC4C;OACtB,CAAC;KACH,MAAM;MACL1C,SAAS,CAACC,SAAS,CAACC,SAAS,CAACL,MAAM,CAACC,QAAQ,CAAC4C,IAAI,CAAC;MACnD5E,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;;EAE3C;EAEA5F,eAAeA,CAAA;IACb2F,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;EACtD;EAEAtF,WAAWA,CAACkD,KAAa;IACvB,IAAIA,KAAK,IAAI,OAAO,EAAE;MACpB,OAAO,CAACA,KAAK,GAAG,OAAO,EAAEgH,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;KAC1C,MAAM,IAAIhH,KAAK,IAAI,IAAI,EAAE;MACxB,OAAO,CAACA,KAAK,GAAG,IAAI,EAAEgH,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAExC,OAAOhH,KAAK,CAACiH,QAAQ,EAAE;EACzB;EAEQvE,iBAAiBA,CAAA;IACvB,IAAI,CAACT,QAAQ,GAAGiC,MAAM,CAACqC,UAAU,IAAI,GAAG;EAC1C;;;uBAhTWpF,oBAAoB,EAAA/F,EAAA,CAAA8L,iBAAA,CAAAC,EAAA,CAAAC,eAAA,GAAAhM,EAAA,CAAA8L,iBAAA,CAAAG,EAAA,CAAAC,yBAAA,GAAAlM,EAAA,CAAA8L,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAApM,EAAA,CAAA8L,iBAAA,CAAAO,EAAA,CAAAC,eAAA,GAAAtM,EAAA,CAAA8L,iBAAA,CAAAS,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAApBzG,oBAAoB;MAAA0G,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA3M,EAAA,CAAA4M,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCnBjClN,EAAA,CAAAC,cAAA,aAAoC;UAElCD,EAAA,CAAA8B,UAAA,IAAAsL,mCAAA,kBAAoD;UAiChDpN,EAFJ,CAAAC,cAAA,aAA4B,aACE,YACA;UACxBD,EAAA,CAAAU,SAAA,kBAAwD;UACxDV,EAAA,CAAAW,MAAA,qBACF;UAAAX,EAAA,CAAAY,YAAA,EAAK;UACLZ,EAAA,CAAAC,cAAA,WAA4B;UAAAD,EAAA,CAAAW,MAAA,+BAAwB;UACtDX,EADsD,CAAAY,YAAA,EAAI,EACpD;UACNZ,EAAA,CAAAC,cAAA,gBAAmD;UAAtBD,EAAA,CAAAE,UAAA,mBAAAmN,sDAAA;YAAA,OAASF,GAAA,CAAAzC,SAAA,EAAW;UAAA,EAAC;UAChD1K,EAAA,CAAAW,MAAA,kBACA;UAAAX,EAAA,CAAAU,SAAA,mBAA4C;UAEhDV,EADE,CAAAY,YAAA,EAAS,EACL;UA2BNZ,EAxBA,CAAA8B,UAAA,KAAAwL,oCAAA,iBAAiD,KAAAC,oCAAA,kBAcQ,KAAAC,oCAAA,kBAWa;UAzExExN,EAAA,CAAAY,YAAA,EAAoC;;;UAEEZ,EAAA,CAAAqB,SAAA,EAAc;UAAdrB,EAAA,CAAAwB,UAAA,SAAA2L,GAAA,CAAAtG,QAAA,CAAc;UA8C5C7G,EAAA,CAAAqB,SAAA,IAAe;UAAfrB,EAAA,CAAAwB,UAAA,SAAA2L,GAAA,CAAAtH,SAAA,CAAe;UAcf7F,EAAA,CAAAqB,SAAA,EAAyB;UAAzBrB,EAAA,CAAAwB,UAAA,SAAA2L,GAAA,CAAA9K,KAAA,KAAA8K,GAAA,CAAAtH,SAAA,CAAyB;UAUzB7F,EAAA,CAAAqB,SAAA,EAAoD;UAApDrB,EAAA,CAAAwB,UAAA,UAAA2L,GAAA,CAAAtH,SAAA,KAAAsH,GAAA,CAAA9K,KAAA,IAAA8K,GAAA,CAAAxH,WAAA,CAAAG,MAAA,KAAoD;;;qBDzDhDlG,YAAY,EAAA6N,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAE7N,WAAW,EAAA8N,EAAA,CAAAC,OAAA,EAAE9N,cAAc;MAAA+N,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}