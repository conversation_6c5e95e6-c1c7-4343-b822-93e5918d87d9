{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashionFrontend/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CommonModule } from '@angular/common';\nimport { Subscription } from 'rxjs';\nimport { IonicModule } from '@ionic/angular';\nimport { CarouselModule } from 'ngx-owl-carousel-o';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@ionic/angular\";\nconst _c0 = () => [1, 2, 3];\nfunction TopFashionInfluencersComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function TopFashionInfluencersComponent_div_1_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleSectionLike());\n    });\n    i0.ɵɵelement(2, \"ion-icon\", 13);\n    i0.ɵɵelementStart(3, \"span\", 14);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function TopFashionInfluencersComponent_div_1_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openComments());\n    });\n    i0.ɵɵelement(6, \"ion-icon\", 16);\n    i0.ɵɵelementStart(7, \"span\", 14);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function TopFashionInfluencersComponent_div_1_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.shareSection());\n    });\n    i0.ɵɵelement(10, \"ion-icon\", 18);\n    i0.ɵɵelementStart(11, \"span\", 19);\n    i0.ɵɵtext(12, \"Share\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function TopFashionInfluencersComponent_div_1_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleSectionBookmark());\n    });\n    i0.ɵɵelement(14, \"ion-icon\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function TopFashionInfluencersComponent_div_1_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openMusicPlayer());\n    });\n    i0.ɵɵelement(16, \"ion-icon\", 22);\n    i0.ɵɵelementStart(17, \"span\", 19);\n    i0.ɵɵtext(18, \"Music\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"active\", ctx_r1.isSectionLiked);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", ctx_r1.isSectionLiked ? \"heart\" : \"heart-outline\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.formatCount(ctx_r1.sectionLikes));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.formatCount(ctx_r1.sectionComments));\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"active\", ctx_r1.isSectionBookmarked);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", ctx_r1.isSectionBookmarked ? \"bookmark\" : \"bookmark-outline\");\n  }\n}\nfunction TopFashionInfluencersComponent_div_9_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵelement(1, \"div\", 27);\n    i0.ɵɵelementStart(2, \"div\", 28);\n    i0.ɵɵelement(3, \"div\", 29)(4, \"div\", 30)(5, \"div\", 31);\n    i0.ɵɵelementStart(6, \"div\", 32);\n    i0.ɵɵelement(7, \"div\", 33)(8, \"div\", 33);\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction TopFashionInfluencersComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"div\", 24);\n    i0.ɵɵtemplate(2, TopFashionInfluencersComponent_div_9_div_2_Template, 9, 0, \"div\", 25);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c0));\n  }\n}\nfunction TopFashionInfluencersComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵelement(1, \"ion-icon\", 35);\n    i0.ɵɵelementStart(2, \"p\", 36);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function TopFashionInfluencersComponent_div_10_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onRetry());\n    });\n    i0.ɵɵelement(5, \"ion-icon\", 38);\n    i0.ɵɵtext(6, \" Try Again \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction TopFashionInfluencersComponent_div_11_div_7_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65);\n    i0.ɵɵelement(1, \"ion-icon\", 66);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TopFashionInfluencersComponent_div_11_div_7_span_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 67);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const brand_r7 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", brand_r7, \" \");\n  }\n}\nfunction TopFashionInfluencersComponent_div_11_div_7_span_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 68);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const influencer_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" +\", influencer_r6.topBrands.length - 2, \" \");\n  }\n}\nfunction TopFashionInfluencersComponent_div_11_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵlistener(\"click\", function TopFashionInfluencersComponent_div_11_div_7_Template_div_click_0_listener() {\n      const influencer_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onInfluencerClick(influencer_r6));\n    });\n    i0.ɵɵelementStart(1, \"div\", 48);\n    i0.ɵɵelement(2, \"img\", 49);\n    i0.ɵɵtemplate(3, TopFashionInfluencersComponent_div_11_div_7_div_3_Template, 2, 0, \"div\", 50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 51)(5, \"h3\", 52);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 53);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\", 54);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 55)(12, \"div\", 56)(13, \"span\", 57);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\", 58);\n    i0.ɵɵtext(16, \"Followers\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 56)(18, \"span\", 57);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"span\", 58);\n    i0.ɵɵtext(21, \"Engagement\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(22, \"div\", 59)(23, \"span\", 60);\n    i0.ɵɵtext(24, \"Works with:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"div\", 61);\n    i0.ɵɵtemplate(26, TopFashionInfluencersComponent_div_11_div_7_span_26_Template, 2, 1, \"span\", 62)(27, TopFashionInfluencersComponent_div_11_div_7_span_27_Template, 2, 1, \"span\", 63);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(28, \"button\", 64);\n    i0.ɵɵlistener(\"click\", function TopFashionInfluencersComponent_div_11_div_7_Template_button_click_28_listener($event) {\n      const influencer_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onFollowInfluencer(influencer_r6, $event));\n    });\n    i0.ɵɵelementStart(29, \"span\");\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(31, \"ion-icon\", 13);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const influencer_r6 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", influencer_r6.avatar, i0.ɵɵsanitizeUrl)(\"alt\", influencer_r6.fullName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", influencer_r6.isVerified);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(influencer_r6.fullName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"@\", influencer_r6.username, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(influencer_r6.category);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.formatFollowerCount(influencer_r6.followerCount));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", influencer_r6.engagementRate, \"%\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", influencer_r6.topBrands.slice(0, 2));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", influencer_r6.topBrands.length > 2);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"following\", influencer_r6.isFollowing);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(influencer_r6.isFollowing ? \"Following\" : \"Follow\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", influencer_r6.isFollowing ? \"checkmark\" : \"add\");\n  }\n}\nfunction TopFashionInfluencersComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function TopFashionInfluencersComponent_div_11_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.slidePrev());\n    });\n    i0.ɵɵelement(2, \"ion-icon\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function TopFashionInfluencersComponent_div_11_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.slideNext());\n    });\n    i0.ɵɵelement(4, \"ion-icon\", 43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 44);\n    i0.ɵɵlistener(\"mouseenter\", function TopFashionInfluencersComponent_div_11_Template_div_mouseenter_5_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.pauseAutoSlide());\n    })(\"mouseleave\", function TopFashionInfluencersComponent_div_11_Template_div_mouseleave_5_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.resumeAutoSlide());\n    });\n    i0.ɵɵelementStart(6, \"div\", 45);\n    i0.ɵɵtemplate(7, TopFashionInfluencersComponent_div_11_div_7_Template, 32, 14, \"div\", 46);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentSlide === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentSlide >= ctx_r1.maxSlide);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"transform\", \"translateX(\" + ctx_r1.slideOffset + \"px)\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.topInfluencers)(\"ngForTrackBy\", ctx_r1.trackByInfluencerId);\n  }\n}\nfunction TopFashionInfluencersComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 69);\n    i0.ɵɵelement(1, \"ion-icon\", 70);\n    i0.ɵɵelementStart(2, \"h3\", 71);\n    i0.ɵɵtext(3, \"No Influencers\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 72);\n    i0.ɵɵtext(5, \"Check back later for top fashion influencers\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let TopFashionInfluencersComponent = /*#__PURE__*/(() => {\n  class TopFashionInfluencersComponent {\n    constructor(router) {\n      this.router = router;\n      this.topInfluencers = [];\n      this.isLoading = true;\n      this.error = null;\n      this.subscription = new Subscription();\n      // Slider properties\n      this.currentSlide = 0;\n      this.slideOffset = 0;\n      this.cardWidth = 240; // Width of each influencer card including margin\n      this.visibleCards = 3; // Number of cards visible at once\n      this.maxSlide = 0;\n      this.autoSlideDelay = 6000; // 6 seconds for influencers\n      this.isAutoSliding = true;\n      this.isPaused = false;\n      // Section interaction properties\n      this.isSectionLiked = false;\n      this.isSectionBookmarked = false;\n      this.sectionLikes = 512;\n      this.sectionComments = 234;\n      this.isMobile = false;\n    }\n    ngOnInit() {\n      this.loadTopInfluencers();\n      this.updateResponsiveSettings();\n      this.setupResizeListener();\n      this.checkMobileDevice();\n    }\n    ngOnDestroy() {\n      this.subscription.unsubscribe();\n      this.stopAutoSlide();\n    }\n    loadTopInfluencers() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        try {\n          _this.isLoading = true;\n          _this.error = null;\n          // Mock data for top fashion influencers\n          _this.topInfluencers = [{\n            id: '1',\n            username: 'fashionista_queen',\n            fullName: 'Priya Sharma',\n            avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=150&h=150&fit=crop&crop=face',\n            followerCount: 2500000,\n            category: 'High Fashion',\n            isVerified: true,\n            isFollowing: false,\n            engagementRate: 8.5,\n            recentPosts: 24,\n            topBrands: ['Gucci', 'Prada', 'Versace']\n          }, {\n            id: '2',\n            username: 'street_style_king',\n            fullName: 'Arjun Kapoor',\n            avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',\n            followerCount: 1800000,\n            category: 'Streetwear',\n            isVerified: true,\n            isFollowing: false,\n            engagementRate: 12.3,\n            recentPosts: 18,\n            topBrands: ['Nike', 'Adidas', 'Supreme']\n          }, {\n            id: '3',\n            username: 'boho_goddess',\n            fullName: 'Ananya Singh',\n            avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',\n            followerCount: 1200000,\n            category: 'Boho Chic',\n            isVerified: true,\n            isFollowing: false,\n            engagementRate: 9.7,\n            recentPosts: 32,\n            topBrands: ['Free People', 'Anthropologie', 'Zara']\n          }, {\n            id: '4',\n            username: 'luxury_lifestyle',\n            fullName: 'Kavya Reddy',\n            avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face',\n            followerCount: 3200000,\n            category: 'Luxury',\n            isVerified: true,\n            isFollowing: false,\n            engagementRate: 6.8,\n            recentPosts: 15,\n            topBrands: ['Chanel', 'Dior', 'Louis Vuitton']\n          }, {\n            id: '5',\n            username: 'minimalist_maven',\n            fullName: 'Ravi Kumar',\n            avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',\n            followerCount: 950000,\n            category: 'Minimalist',\n            isVerified: true,\n            isFollowing: false,\n            engagementRate: 11.2,\n            recentPosts: 21,\n            topBrands: ['COS', 'Uniqlo', 'Everlane']\n          }, {\n            id: '6',\n            username: 'vintage_vibes',\n            fullName: 'Meera Patel',\n            avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face',\n            followerCount: 780000,\n            category: 'Vintage',\n            isVerified: true,\n            isFollowing: false,\n            engagementRate: 13.5,\n            recentPosts: 28,\n            topBrands: ['Vintage Stores', 'Thrift Finds', 'Custom']\n          }];\n          _this.isLoading = false;\n          _this.updateSliderOnInfluencersLoad();\n        } catch (error) {\n          console.error('Error loading top influencers:', error);\n          _this.error = 'Failed to load top influencers';\n          _this.isLoading = false;\n        }\n      })();\n    }\n    onInfluencerClick(influencer) {\n      this.router.navigate(['/profile', influencer.username]);\n    }\n    onFollowInfluencer(influencer, event) {\n      event.stopPropagation();\n      influencer.isFollowing = !influencer.isFollowing;\n      if (influencer.isFollowing) {\n        influencer.followerCount++;\n      } else {\n        influencer.followerCount--;\n      }\n    }\n    formatFollowerCount(count) {\n      if (count >= 1000000) {\n        return (count / 1000000).toFixed(1) + 'M';\n      } else if (count >= 1000) {\n        return (count / 1000).toFixed(1) + 'K';\n      }\n      return count.toString();\n    }\n    onRetry() {\n      this.loadTopInfluencers();\n    }\n    trackByInfluencerId(index, influencer) {\n      return influencer.id;\n    }\n    // Auto-sliding methods\n    startAutoSlide() {\n      if (!this.isAutoSliding || this.isPaused) return;\n      this.stopAutoSlide();\n      this.autoSlideInterval = setInterval(() => {\n        if (!this.isPaused && this.topInfluencers.length > this.visibleCards) {\n          this.autoSlideNext();\n        }\n      }, this.autoSlideDelay);\n    }\n    stopAutoSlide() {\n      if (this.autoSlideInterval) {\n        clearInterval(this.autoSlideInterval);\n        this.autoSlideInterval = null;\n      }\n    }\n    autoSlideNext() {\n      if (this.currentSlide >= this.maxSlide) {\n        this.currentSlide = 0;\n      } else {\n        this.currentSlide++;\n      }\n      this.updateSlideOffset();\n    }\n    pauseAutoSlide() {\n      this.isPaused = true;\n      this.stopAutoSlide();\n    }\n    resumeAutoSlide() {\n      this.isPaused = false;\n      this.startAutoSlide();\n    }\n    // Responsive methods\n    updateResponsiveSettings() {\n      const width = window.innerWidth;\n      if (width <= 480) {\n        this.cardWidth = 200;\n        this.visibleCards = 1;\n      } else if (width <= 768) {\n        this.cardWidth = 220;\n        this.visibleCards = 2;\n      } else if (width <= 1200) {\n        this.cardWidth = 240;\n        this.visibleCards = 2;\n      } else {\n        this.cardWidth = 260;\n        this.visibleCards = 3;\n      }\n      this.updateSliderLimits();\n      this.updateSlideOffset();\n    }\n    setupResizeListener() {\n      window.addEventListener('resize', () => {\n        this.updateResponsiveSettings();\n      });\n    }\n    // Slider methods\n    updateSliderLimits() {\n      this.maxSlide = Math.max(0, this.topInfluencers.length - this.visibleCards);\n    }\n    slidePrev() {\n      if (this.currentSlide > 0) {\n        this.currentSlide--;\n        this.updateSlideOffset();\n        this.restartAutoSlideAfterInteraction();\n      }\n    }\n    slideNext() {\n      if (this.currentSlide < this.maxSlide) {\n        this.currentSlide++;\n        this.updateSlideOffset();\n        this.restartAutoSlideAfterInteraction();\n      }\n    }\n    updateSlideOffset() {\n      this.slideOffset = -this.currentSlide * this.cardWidth;\n    }\n    restartAutoSlideAfterInteraction() {\n      this.stopAutoSlide();\n      setTimeout(() => {\n        this.startAutoSlide();\n      }, 2000);\n    }\n    // Update slider when influencers load\n    updateSliderOnInfluencersLoad() {\n      setTimeout(() => {\n        this.updateSliderLimits();\n        this.currentSlide = 0;\n        this.slideOffset = 0;\n        this.startAutoSlide();\n      }, 100);\n    }\n    // Section interaction methods\n    toggleSectionLike() {\n      this.isSectionLiked = !this.isSectionLiked;\n      if (this.isSectionLiked) {\n        this.sectionLikes++;\n      } else {\n        this.sectionLikes--;\n      }\n    }\n    toggleSectionBookmark() {\n      this.isSectionBookmarked = !this.isSectionBookmarked;\n    }\n    openComments() {\n      console.log('Opening comments for top fashion influencers section');\n    }\n    shareSection() {\n      if (navigator.share) {\n        navigator.share({\n          title: 'Top Fashion Influencers',\n          text: 'Follow the top fashion trendsetters!',\n          url: window.location.href\n        });\n      } else {\n        navigator.clipboard.writeText(window.location.href);\n        console.log('Link copied to clipboard');\n      }\n    }\n    openMusicPlayer() {\n      console.log('Opening music player for top fashion influencers');\n    }\n    formatCount(count) {\n      if (count >= 1000000) {\n        return (count / 1000000).toFixed(1) + 'M';\n      } else if (count >= 1000) {\n        return (count / 1000).toFixed(1) + 'K';\n      }\n      return count.toString();\n    }\n    checkMobileDevice() {\n      this.isMobile = window.innerWidth <= 768;\n    }\n    static {\n      this.ɵfac = function TopFashionInfluencersComponent_Factory(t) {\n        return new (t || TopFashionInfluencersComponent)(i0.ɵɵdirectiveInject(i1.Router));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: TopFashionInfluencersComponent,\n        selectors: [[\"app-top-fashion-influencers\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 13,\n        vars: 5,\n        consts: [[1, \"top-influencers-container\"], [\"class\", \"mobile-action-buttons\", 4, \"ngIf\"], [1, \"section-header\"], [1, \"header-content\"], [1, \"section-title\"], [\"name\", \"star\", 1, \"title-icon\"], [1, \"section-subtitle\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"influencers-slider-container\", 4, \"ngIf\"], [\"class\", \"empty-container\", 4, \"ngIf\"], [1, \"mobile-action-buttons\"], [1, \"action-btn\", \"like-btn\", 3, \"click\"], [3, \"name\"], [1, \"action-count\"], [1, \"action-btn\", \"comment-btn\", 3, \"click\"], [\"name\", \"chatbubble-outline\"], [1, \"action-btn\", \"share-btn\", 3, \"click\"], [\"name\", \"arrow-redo-outline\"], [1, \"action-text\"], [1, \"action-btn\", \"bookmark-btn\", 3, \"click\"], [1, \"action-btn\", \"music-btn\", 3, \"click\"], [\"name\", \"musical-notes\"], [1, \"loading-container\"], [1, \"loading-grid\"], [\"class\", \"loading-influencer-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"loading-influencer-card\"], [1, \"loading-avatar\"], [1, \"loading-content\"], [1, \"loading-line\", \"short\"], [1, \"loading-line\", \"medium\"], [1, \"loading-line\", \"long\"], [1, \"loading-stats\"], [1, \"loading-stat\"], [1, \"error-container\"], [\"name\", \"alert-circle\", 1, \"error-icon\"], [1, \"error-message\"], [1, \"retry-btn\", 3, \"click\"], [\"name\", \"refresh\"], [1, \"influencers-slider-container\"], [1, \"slider-nav\", \"prev-btn\", 3, \"click\", \"disabled\"], [\"name\", \"chevron-back\"], [1, \"slider-nav\", \"next-btn\", 3, \"click\", \"disabled\"], [\"name\", \"chevron-forward\"], [1, \"influencers-slider-wrapper\", 3, \"mouseenter\", \"mouseleave\"], [1, \"influencers-slider\"], [\"class\", \"influencer-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"influencer-card\", 3, \"click\"], [1, \"influencer-avatar-container\"], [\"loading\", \"lazy\", 1, \"influencer-avatar\", 3, \"src\", \"alt\"], [\"class\", \"verified-badge\", 4, \"ngIf\"], [1, \"influencer-info\"], [1, \"influencer-name\"], [1, \"username\"], [1, \"category\"], [1, \"stats-container\"], [1, \"stat\"], [1, \"stat-value\"], [1, \"stat-label\"], [1, \"top-brands\"], [1, \"brands-label\"], [1, \"brands-list\"], [\"class\", \"brand-tag\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"more-brands\", 4, \"ngIf\"], [1, \"follow-btn\", 3, \"click\"], [1, \"verified-badge\"], [\"name\", \"checkmark\"], [1, \"brand-tag\"], [1, \"more-brands\"], [1, \"empty-container\"], [\"name\", \"star-outline\", 1, \"empty-icon\"], [1, \"empty-title\"], [1, \"empty-message\"]],\n        template: function TopFashionInfluencersComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0);\n            i0.ɵɵtemplate(1, TopFashionInfluencersComponent_div_1_Template, 19, 8, \"div\", 1);\n            i0.ɵɵelementStart(2, \"div\", 2)(3, \"div\", 3)(4, \"h2\", 4);\n            i0.ɵɵelement(5, \"ion-icon\", 5);\n            i0.ɵɵtext(6, \" Top Fashion Influencers \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(7, \"p\", 6);\n            i0.ɵɵtext(8, \"Follow the trendsetters\");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(9, TopFashionInfluencersComponent_div_9_Template, 3, 2, \"div\", 7)(10, TopFashionInfluencersComponent_div_10_Template, 7, 1, \"div\", 8)(11, TopFashionInfluencersComponent_div_11_Template, 8, 6, \"div\", 9)(12, TopFashionInfluencersComponent_div_12_Template, 6, 0, \"div\", 10);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.isMobile);\n            i0.ɵɵadvance(8);\n            i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", ctx.error && !ctx.isLoading);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.topInfluencers.length > 0);\n            i0.ɵɵadvance();\n            i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.topInfluencers.length === 0);\n          }\n        },\n        dependencies: [CommonModule, i2.NgForOf, i2.NgIf, IonicModule, i3.IonIcon, CarouselModule],\n        styles: [\".top-influencers-container[_ngcontent-%COMP%]{padding:20px;background:linear-gradient(135deg,#f8f9fa,#e9ecef);border-radius:16px;margin-bottom:24px;position:relative;max-width:675px}.mobile-action-buttons[_ngcontent-%COMP%]{position:absolute;right:15px;top:50%;transform:translateY(-50%);display:flex;flex-direction:column;gap:20px;z-index:10}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]{width:48px;height:48px;border-radius:50%;border:none;background:#ffd70033;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);color:gold;display:flex;flex-direction:column;align-items:center;justify-content:center;cursor:pointer;transition:all .3s ease;position:relative}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:24px;margin-bottom:2px}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   .action-count[_ngcontent-%COMP%], .mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   .action-text[_ngcontent-%COMP%]{font-size:10px;font-weight:600;line-height:1}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:hover{transform:scale(1.1);background:#ffd7004d}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active[_ngcontent-%COMP%]{background:#ffd700e6;color:#fff}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active.like-btn[_ngcontent-%COMP%]{background:#ff3040e6;color:#fff}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active.like-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_heartBeat .6s ease-in-out}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active.bookmark-btn[_ngcontent-%COMP%]{background:#ffd700e6;color:#fff}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.like-btn.active[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:#fff}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.music-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,#ff3040,gold);color:#fff}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.music-btn[_ngcontent-%COMP%]:hover{transform:scale(1.1) rotate(15deg)}@keyframes _ngcontent-%COMP%_heartBeat{0%{transform:scale(1)}25%{transform:scale(1.3)}50%{transform:scale(1.1)}75%{transform:scale(1.25)}to{transform:scale(1)}}@media (min-width: 769px){.mobile-action-buttons[_ngcontent-%COMP%]{display:none}}.section-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:24px}.section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]{flex:1}.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{font-size:24px;font-weight:700;color:#1a1a1a;margin:0 0 8px;display:flex;align-items:center;gap:12px}.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   .title-icon[_ngcontent-%COMP%]{font-size:28px;color:gold}.section-header[_ngcontent-%COMP%]   .section-subtitle[_ngcontent-%COMP%]{font-size:14px;color:#666;margin:0}.loading-container[_ngcontent-%COMP%]   .loading-grid[_ngcontent-%COMP%]{display:flex;gap:20px;overflow-x:auto;padding-bottom:8px}.loading-container[_ngcontent-%COMP%]   .loading-influencer-card[_ngcontent-%COMP%]{flex:0 0 220px;background:#ffffffb3;border-radius:16px;padding:20px}.loading-container[_ngcontent-%COMP%]   .loading-influencer-card[_ngcontent-%COMP%]   .loading-avatar[_ngcontent-%COMP%]{width:100px;height:100px;border-radius:50%;background:linear-gradient(90deg,#f0f0f0 25%,#e0e0e0,#f0f0f0 75%);background-size:200% 100%;animation:_ngcontent-%COMP%_loading 1.5s infinite;margin:0 auto 16px}.loading-container[_ngcontent-%COMP%]   .loading-influencer-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line[_ngcontent-%COMP%]{height:12px;border-radius:6px;background:linear-gradient(90deg,#f0f0f0 25%,#e0e0e0,#f0f0f0 75%);background-size:200% 100%;animation:_ngcontent-%COMP%_loading 1.5s infinite;margin-bottom:8px}.loading-container[_ngcontent-%COMP%]   .loading-influencer-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.short[_ngcontent-%COMP%]{width:60%}.loading-container[_ngcontent-%COMP%]   .loading-influencer-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.medium[_ngcontent-%COMP%]{width:80%}.loading-container[_ngcontent-%COMP%]   .loading-influencer-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.long[_ngcontent-%COMP%]{width:100%}.loading-container[_ngcontent-%COMP%]   .loading-influencer-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-stats[_ngcontent-%COMP%]{display:flex;gap:10px;margin-top:12px}.loading-container[_ngcontent-%COMP%]   .loading-influencer-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-stats[_ngcontent-%COMP%]   .loading-stat[_ngcontent-%COMP%]{flex:1;height:20px;border-radius:10px;background:linear-gradient(90deg,#f0f0f0 25%,#e0e0e0,#f0f0f0 75%);background-size:200% 100%;animation:_ngcontent-%COMP%_loading 1.5s infinite}@keyframes _ngcontent-%COMP%_loading{0%{background-position:200% 0}to{background-position:-200% 0}}.error-container[_ngcontent-%COMP%]{text-align:center;padding:40px 20px}.error-container[_ngcontent-%COMP%]   .error-icon[_ngcontent-%COMP%]{font-size:48px;color:#e74c3c;margin-bottom:16px}.error-container[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%]{color:#666;margin-bottom:20px;font-size:16px}.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,#e74c3c,#c0392b);color:#fff;border:none;padding:12px 24px;border-radius:25px;font-weight:600;display:flex;align-items:center;gap:8px;margin:0 auto;cursor:pointer;transition:all .3s ease}.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 8px 25px #e74c3c4d}.influencers-slider-container[_ngcontent-%COMP%]{position:relative;margin:0 -20px}.influencers-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]{position:absolute;top:50%;transform:translateY(-50%);z-index:10;background:#000000b3;color:#fff;border:none;width:40px;height:40px;border-radius:50%;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all .3s ease}.influencers-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]:hover:not(:disabled){background:#000000e6;transform:translateY(-50%) scale(1.1)}.influencers-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]:disabled{opacity:.3;cursor:not-allowed}.influencers-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px}.influencers-slider-container[_ngcontent-%COMP%]   .slider-nav.prev-btn[_ngcontent-%COMP%]{left:-20px}.influencers-slider-container[_ngcontent-%COMP%]   .slider-nav.next-btn[_ngcontent-%COMP%]{right:-20px}.influencers-slider-wrapper[_ngcontent-%COMP%]{overflow:hidden;padding:0 20px}.influencers-slider[_ngcontent-%COMP%]{display:flex;transition:transform .4s cubic-bezier(.25,.46,.45,.94);gap:20px}.influencers-slider[_ngcontent-%COMP%]   .influencer-card[_ngcontent-%COMP%]{flex:0 0 220px;width:220px}.influencer-card[_ngcontent-%COMP%]{background:#fff;border-radius:16px;padding:24px;text-align:center;box-shadow:0 4px 20px #00000014;transition:all .3s ease;cursor:pointer}.influencer-card[_ngcontent-%COMP%]:hover{transform:translateY(-8px);box-shadow:0 12px 40px #00000026}.influencer-avatar-container[_ngcontent-%COMP%]{position:relative;margin-bottom:20px}.influencer-avatar-container[_ngcontent-%COMP%]   .influencer-avatar[_ngcontent-%COMP%]{width:100px;height:100px;border-radius:50%;object-fit:cover;border:4px solid #ffd700;margin:0 auto;display:block}.influencer-avatar-container[_ngcontent-%COMP%]   .verified-badge[_ngcontent-%COMP%]{position:absolute;top:-5px;right:calc(50% - 55px);background:linear-gradient(135deg,#00b894,#00a085);color:#fff;width:28px;height:28px;border-radius:50%;display:flex;align-items:center;justify-content:center;font-size:14px;border:3px solid white}.influencer-avatar-container[_ngcontent-%COMP%]   .verified-badge[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:14px}.influencer-info[_ngcontent-%COMP%]{margin-bottom:20px}.influencer-info[_ngcontent-%COMP%]   .influencer-name[_ngcontent-%COMP%]{font-size:18px;font-weight:700;color:#1a1a1a;margin:0 0 4px}.influencer-info[_ngcontent-%COMP%]   .username[_ngcontent-%COMP%]{font-size:14px;color:#6c5ce7;margin:0 0 8px;font-weight:500}.influencer-info[_ngcontent-%COMP%]   .category[_ngcontent-%COMP%]{font-size:12px;color:gold;background:#ffd7001a;padding:4px 12px;border-radius:12px;display:inline-block;margin:0 0 16px;font-weight:600}.stats-container[_ngcontent-%COMP%]{display:flex;gap:16px;margin-bottom:16px}.stats-container[_ngcontent-%COMP%]   .stat[_ngcontent-%COMP%]{flex:1;text-align:center}.stats-container[_ngcontent-%COMP%]   .stat[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%]{display:block;font-size:16px;font-weight:700;color:#1a1a1a;margin-bottom:2px}.stats-container[_ngcontent-%COMP%]   .stat[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%]{font-size:11px;color:#666;text-transform:uppercase;letter-spacing:.5px}.top-brands[_ngcontent-%COMP%]   .brands-label[_ngcontent-%COMP%]{font-size:11px;color:#666;display:block;margin-bottom:6px}.top-brands[_ngcontent-%COMP%]   .brands-list[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:4px;justify-content:center}.top-brands[_ngcontent-%COMP%]   .brands-list[_ngcontent-%COMP%]   .brand-tag[_ngcontent-%COMP%]{font-size:10px;background:#6c5ce71a;color:#6c5ce7;padding:2px 6px;border-radius:8px;font-weight:500}.top-brands[_ngcontent-%COMP%]   .brands-list[_ngcontent-%COMP%]   .more-brands[_ngcontent-%COMP%]{font-size:10px;color:#999;font-weight:500}.follow-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,gold,#ffb347);color:#1a1a1a;border:none;padding:10px 20px;border-radius:25px;font-size:13px;font-weight:700;display:flex;align-items:center;gap:6px;margin:0 auto;cursor:pointer;transition:all .3s ease}.follow-btn[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 8px 25px #ffd70066}.follow-btn.following[_ngcontent-%COMP%]{background:linear-gradient(135deg,#00b894,#00a085);color:#fff}.follow-btn.following[_ngcontent-%COMP%]:hover{box-shadow:0 8px 25px #00b89466}.follow-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:16px}.empty-container[_ngcontent-%COMP%]{text-align:center;padding:60px 20px}.empty-container[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%]{font-size:64px;color:#ddd;margin-bottom:20px}.empty-container[_ngcontent-%COMP%]   .empty-title[_ngcontent-%COMP%]{font-size:20px;font-weight:600;color:#666;margin:0 0 8px}.empty-container[_ngcontent-%COMP%]   .empty-message[_ngcontent-%COMP%]{color:#999;margin:0}@media (max-width: 1200px){.influencers-slider[_ngcontent-%COMP%]   .influencer-card[_ngcontent-%COMP%]{flex:0 0 200px;width:200px;padding:20px}}@media (max-width: 768px){.influencers-slider-container[_ngcontent-%COMP%]{margin:0 -10px}.influencers-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]{width:35px;height:35px}.influencers-slider-container[_ngcontent-%COMP%]   .slider-nav.prev-btn[_ngcontent-%COMP%]{left:-15px}.influencers-slider-container[_ngcontent-%COMP%]   .slider-nav.next-btn[_ngcontent-%COMP%]{right:-15px}.influencers-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:16px}.influencers-slider-wrapper[_ngcontent-%COMP%]{padding:0 10px}.influencers-slider[_ngcontent-%COMP%]{gap:15px}.influencers-slider[_ngcontent-%COMP%]   .influencer-card[_ngcontent-%COMP%]{flex:0 0 180px;width:180px;padding:16px}.influencer-avatar-container[_ngcontent-%COMP%]   .influencer-avatar[_ngcontent-%COMP%]{width:80px;height:80px}}@media (max-width: 480px){.influencers-slider[_ngcontent-%COMP%]   .influencer-card[_ngcontent-%COMP%]{flex:0 0 170px;width:170px}}\"]\n      });\n    }\n  }\n  return TopFashionInfluencersComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}