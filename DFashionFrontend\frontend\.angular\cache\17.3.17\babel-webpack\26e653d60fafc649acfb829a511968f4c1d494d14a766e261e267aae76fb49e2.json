{"ast": null, "code": "import _asyncToGenerator from \"E:/Fashion/DFashionFrontend/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CommonModule } from '@angular/common';\nimport { Subscription } from 'rxjs';\nimport { IonicModule } from '@ionic/angular';\nimport { CarouselModule } from 'ngx-owl-carousel-o';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@ionic/angular\";\nconst _c0 = () => [1, 2, 3, 4];\nfunction SuggestedForYouComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function SuggestedForYouComponent_div_1_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleSectionLike());\n    });\n    i0.ɵɵelement(2, \"ion-icon\", 13);\n    i0.ɵɵelementStart(3, \"span\", 14);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function SuggestedForYouComponent_div_1_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openComments());\n    });\n    i0.ɵɵelement(6, \"ion-icon\", 16);\n    i0.ɵɵelementStart(7, \"span\", 14);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function SuggestedForYouComponent_div_1_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.shareSection());\n    });\n    i0.ɵɵelement(10, \"ion-icon\", 18);\n    i0.ɵɵelementStart(11, \"span\", 19);\n    i0.ɵɵtext(12, \"Share\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function SuggestedForYouComponent_div_1_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleSectionBookmark());\n    });\n    i0.ɵɵelement(14, \"ion-icon\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function SuggestedForYouComponent_div_1_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openMusicPlayer());\n    });\n    i0.ɵɵelement(16, \"ion-icon\", 22);\n    i0.ɵɵelementStart(17, \"span\", 19);\n    i0.ɵɵtext(18, \"Music\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"active\", ctx_r1.isSectionLiked);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", ctx_r1.isSectionLiked ? \"heart\" : \"heart-outline\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.formatCount(ctx_r1.sectionLikes));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.formatCount(ctx_r1.sectionComments));\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"active\", ctx_r1.isSectionBookmarked);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", ctx_r1.isSectionBookmarked ? \"bookmark\" : \"bookmark-outline\");\n  }\n}\nfunction SuggestedForYouComponent_div_9_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵelement(1, \"div\", 27);\n    i0.ɵɵelementStart(2, \"div\", 28);\n    i0.ɵɵelement(3, \"div\", 29)(4, \"div\", 30)(5, \"div\", 31);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SuggestedForYouComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"div\", 24);\n    i0.ɵɵtemplate(2, SuggestedForYouComponent_div_9_div_2_Template, 6, 0, \"div\", 25);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c0));\n  }\n}\nfunction SuggestedForYouComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵelement(1, \"ion-icon\", 33);\n    i0.ɵɵelementStart(2, \"p\", 34);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function SuggestedForYouComponent_div_10_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onRetry());\n    });\n    i0.ɵɵelement(5, \"ion-icon\", 36);\n    i0.ɵɵtext(6, \" Try Again \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction SuggestedForYouComponent_div_11_div_7_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 56);\n    i0.ɵɵelement(1, \"ion-icon\", 57);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SuggestedForYouComponent_div_11_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵlistener(\"click\", function SuggestedForYouComponent_div_11_div_7_Template_div_click_0_listener() {\n      const user_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onUserClick(user_r6));\n    });\n    i0.ɵɵelementStart(1, \"div\", 46);\n    i0.ɵɵelement(2, \"img\", 47);\n    i0.ɵɵtemplate(3, SuggestedForYouComponent_div_11_div_7_div_3_Template, 2, 0, \"div\", 48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 49)(5, \"h3\", 50);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 51);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\", 52);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"p\", 53);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"p\", 54);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"button\", 55);\n    i0.ɵɵlistener(\"click\", function SuggestedForYouComponent_div_11_div_7_Template_button_click_15_listener($event) {\n      const user_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onFollowUser(user_r6, $event));\n    });\n    i0.ɵɵelementStart(16, \"span\");\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(18, \"ion-icon\", 13);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const user_r6 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", user_r6.avatar, i0.ɵɵsanitizeUrl)(\"alt\", user_r6.fullName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", user_r6.isInfluencer);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(user_r6.fullName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"@\", user_r6.username, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.formatFollowerCount(user_r6.followerCount), \" followers\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(user_r6.category);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(user_r6.followedBy);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"following\", user_r6.isFollowing);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(user_r6.isFollowing ? \"Following\" : \"Follow\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", user_r6.isFollowing ? \"checkmark\" : \"add\");\n  }\n}\nfunction SuggestedForYouComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function SuggestedForYouComponent_div_11_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.slidePrev());\n    });\n    i0.ɵɵelement(2, \"ion-icon\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function SuggestedForYouComponent_div_11_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.slideNext());\n    });\n    i0.ɵɵelement(4, \"ion-icon\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 42);\n    i0.ɵɵlistener(\"mouseenter\", function SuggestedForYouComponent_div_11_Template_div_mouseenter_5_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.pauseAutoSlide());\n    })(\"mouseleave\", function SuggestedForYouComponent_div_11_Template_div_mouseleave_5_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.resumeAutoSlide());\n    });\n    i0.ɵɵelementStart(6, \"div\", 43);\n    i0.ɵɵtemplate(7, SuggestedForYouComponent_div_11_div_7_Template, 19, 12, \"div\", 44);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentSlide === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.currentSlide >= ctx_r1.maxSlide);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"transform\", \"translateX(\" + ctx_r1.slideOffset + \"px)\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.suggestedUsers)(\"ngForTrackBy\", ctx_r1.trackByUserId);\n  }\n}\nfunction SuggestedForYouComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58);\n    i0.ɵɵelement(1, \"ion-icon\", 59);\n    i0.ɵɵelementStart(2, \"h3\", 60);\n    i0.ɵɵtext(3, \"No Suggestions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 61);\n    i0.ɵɵtext(5, \"Check back later for user suggestions\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class SuggestedForYouComponent {\n  constructor(router) {\n    this.router = router;\n    this.suggestedUsers = [];\n    this.isLoading = true;\n    this.error = null;\n    this.subscription = new Subscription();\n    // Slider properties\n    this.currentSlide = 0;\n    this.slideOffset = 0;\n    this.cardWidth = 200; // Width of each user card including margin\n    this.visibleCards = 4; // Number of cards visible at once\n    this.maxSlide = 0;\n    this.autoSlideDelay = 5000; // 5 seconds for users\n    this.isAutoSliding = true;\n    this.isPaused = false;\n    // Section interaction properties\n    this.isSectionLiked = false;\n    this.isSectionBookmarked = false;\n    this.sectionLikes = 198;\n    this.sectionComments = 67;\n    this.isMobile = false;\n  }\n  ngOnInit() {\n    this.loadSuggestedUsers();\n    this.updateResponsiveSettings();\n    this.setupResizeListener();\n    this.checkMobileDevice();\n  }\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n    this.stopAutoSlide();\n  }\n  loadSuggestedUsers() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this.isLoading = true;\n        _this.error = null;\n        // Mock data for suggested users\n        _this.suggestedUsers = [{\n          id: '1',\n          username: 'fashionista_maya',\n          fullName: 'Maya Patel',\n          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=150&h=150&fit=crop&crop=face',\n          followedBy: 'Followed by john_doe and 12 others',\n          isFollowing: false,\n          isInfluencer: true,\n          followerCount: 45000,\n          category: 'Fashion'\n        }, {\n          id: '2',\n          username: 'style_guru_raj',\n          fullName: 'Raj Kumar',\n          avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',\n          followedBy: 'Followed by sarah_k and 8 others',\n          isFollowing: false,\n          isInfluencer: true,\n          followerCount: 32000,\n          category: 'Menswear'\n        }, {\n          id: '3',\n          username: 'trendy_sara',\n          fullName: 'Sara Johnson',\n          avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',\n          followedBy: 'Followed by alex_m and 15 others',\n          isFollowing: false,\n          isInfluencer: false,\n          followerCount: 8500,\n          category: 'Casual'\n        }, {\n          id: '4',\n          username: 'luxury_lover',\n          fullName: 'Emma Wilson',\n          avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face',\n          followedBy: 'Followed by mike_t and 20 others',\n          isFollowing: false,\n          isInfluencer: true,\n          followerCount: 67000,\n          category: 'Luxury'\n        }, {\n          id: '5',\n          username: 'street_style_alex',\n          fullName: 'Alex Chen',\n          avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',\n          followedBy: 'Followed by lisa_p and 5 others',\n          isFollowing: false,\n          isInfluencer: false,\n          followerCount: 12000,\n          category: 'Streetwear'\n        }, {\n          id: '6',\n          username: 'boho_bella',\n          fullName: 'Isabella Rodriguez',\n          avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face',\n          followedBy: 'Followed by tom_h and 18 others',\n          isFollowing: false,\n          isInfluencer: true,\n          followerCount: 28000,\n          category: 'Boho'\n        }];\n        _this.isLoading = false;\n        _this.updateSliderOnUsersLoad();\n      } catch (error) {\n        console.error('Error loading suggested users:', error);\n        _this.error = 'Failed to load suggested users';\n        _this.isLoading = false;\n      }\n    })();\n  }\n  onUserClick(user) {\n    this.router.navigate(['/profile', user.username]);\n  }\n  onFollowUser(user, event) {\n    event.stopPropagation();\n    user.isFollowing = !user.isFollowing;\n    if (user.isFollowing) {\n      user.followerCount++;\n    } else {\n      user.followerCount--;\n    }\n  }\n  formatFollowerCount(count) {\n    if (count >= 1000000) {\n      return (count / 1000000).toFixed(1) + 'M';\n    } else if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'K';\n    }\n    return count.toString();\n  }\n  onRetry() {\n    this.loadSuggestedUsers();\n  }\n  trackByUserId(index, user) {\n    return user.id;\n  }\n  // Auto-sliding methods\n  startAutoSlide() {\n    if (!this.isAutoSliding || this.isPaused) return;\n    this.stopAutoSlide();\n    this.autoSlideInterval = setInterval(() => {\n      if (!this.isPaused && this.suggestedUsers.length > this.visibleCards) {\n        this.autoSlideNext();\n      }\n    }, this.autoSlideDelay);\n  }\n  stopAutoSlide() {\n    if (this.autoSlideInterval) {\n      clearInterval(this.autoSlideInterval);\n      this.autoSlideInterval = null;\n    }\n  }\n  autoSlideNext() {\n    if (this.currentSlide >= this.maxSlide) {\n      this.currentSlide = 0;\n    } else {\n      this.currentSlide++;\n    }\n    this.updateSlideOffset();\n  }\n  pauseAutoSlide() {\n    this.isPaused = true;\n    this.stopAutoSlide();\n  }\n  resumeAutoSlide() {\n    this.isPaused = false;\n    this.startAutoSlide();\n  }\n  // Responsive methods\n  updateResponsiveSettings() {\n    const width = window.innerWidth;\n    if (width <= 480) {\n      this.cardWidth = 180;\n      this.visibleCards = 1;\n    } else if (width <= 768) {\n      this.cardWidth = 200;\n      this.visibleCards = 2;\n    } else if (width <= 1200) {\n      this.cardWidth = 220;\n      this.visibleCards = 3;\n    } else {\n      this.cardWidth = 220;\n      this.visibleCards = 4;\n    }\n    this.updateSliderLimits();\n    this.updateSlideOffset();\n  }\n  setupResizeListener() {\n    window.addEventListener('resize', () => {\n      this.updateResponsiveSettings();\n    });\n  }\n  // Slider methods\n  updateSliderLimits() {\n    this.maxSlide = Math.max(0, this.suggestedUsers.length - this.visibleCards);\n  }\n  slidePrev() {\n    if (this.currentSlide > 0) {\n      this.currentSlide--;\n      this.updateSlideOffset();\n      this.restartAutoSlideAfterInteraction();\n    }\n  }\n  slideNext() {\n    if (this.currentSlide < this.maxSlide) {\n      this.currentSlide++;\n      this.updateSlideOffset();\n      this.restartAutoSlideAfterInteraction();\n    }\n  }\n  updateSlideOffset() {\n    this.slideOffset = -this.currentSlide * this.cardWidth;\n  }\n  restartAutoSlideAfterInteraction() {\n    this.stopAutoSlide();\n    setTimeout(() => {\n      this.startAutoSlide();\n    }, 2000);\n  }\n  // Update slider when users load\n  updateSliderOnUsersLoad() {\n    setTimeout(() => {\n      this.updateSliderLimits();\n      this.currentSlide = 0;\n      this.slideOffset = 0;\n      this.startAutoSlide();\n    }, 100);\n  }\n  // Section interaction methods\n  toggleSectionLike() {\n    this.isSectionLiked = !this.isSectionLiked;\n    if (this.isSectionLiked) {\n      this.sectionLikes++;\n    } else {\n      this.sectionLikes--;\n    }\n  }\n  toggleSectionBookmark() {\n    this.isSectionBookmarked = !this.isSectionBookmarked;\n  }\n  openComments() {\n    console.log('Opening comments for suggested users section');\n  }\n  shareSection() {\n    if (navigator.share) {\n      navigator.share({\n        title: 'Suggested for You',\n        text: 'Discover amazing fashion creators!',\n        url: window.location.href\n      });\n    } else {\n      navigator.clipboard.writeText(window.location.href);\n      console.log('Link copied to clipboard');\n    }\n  }\n  openMusicPlayer() {\n    console.log('Opening music player for suggested users');\n  }\n  formatCount(count) {\n    if (count >= 1000000) {\n      return (count / 1000000).toFixed(1) + 'M';\n    } else if (count >= 1000) {\n      return (count / 1000).toFixed(1) + 'K';\n    }\n    return count.toString();\n  }\n  checkMobileDevice() {\n    this.isMobile = window.innerWidth <= 768;\n  }\n  static {\n    this.ɵfac = function SuggestedForYouComponent_Factory(t) {\n      return new (t || SuggestedForYouComponent)(i0.ɵɵdirectiveInject(i1.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SuggestedForYouComponent,\n      selectors: [[\"app-suggested-for-you\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 13,\n      vars: 5,\n      consts: [[1, \"suggested-users-container\"], [\"class\", \"mobile-action-buttons\", 4, \"ngIf\"], [1, \"section-header\"], [1, \"header-content\"], [1, \"section-title\"], [\"name\", \"people\", 1, \"title-icon\"], [1, \"section-subtitle\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"error-container\", 4, \"ngIf\"], [\"class\", \"users-slider-container\", 4, \"ngIf\"], [\"class\", \"empty-container\", 4, \"ngIf\"], [1, \"mobile-action-buttons\"], [1, \"action-btn\", \"like-btn\", 3, \"click\"], [3, \"name\"], [1, \"action-count\"], [1, \"action-btn\", \"comment-btn\", 3, \"click\"], [\"name\", \"chatbubble-outline\"], [1, \"action-btn\", \"share-btn\", 3, \"click\"], [\"name\", \"arrow-redo-outline\"], [1, \"action-text\"], [1, \"action-btn\", \"bookmark-btn\", 3, \"click\"], [1, \"action-btn\", \"music-btn\", 3, \"click\"], [\"name\", \"musical-notes\"], [1, \"loading-container\"], [1, \"loading-grid\"], [\"class\", \"loading-user-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"loading-user-card\"], [1, \"loading-avatar\"], [1, \"loading-content\"], [1, \"loading-line\", \"short\"], [1, \"loading-line\", \"medium\"], [1, \"loading-line\", \"long\"], [1, \"error-container\"], [\"name\", \"alert-circle\", 1, \"error-icon\"], [1, \"error-message\"], [1, \"retry-btn\", 3, \"click\"], [\"name\", \"refresh\"], [1, \"users-slider-container\"], [1, \"slider-nav\", \"prev-btn\", 3, \"click\", \"disabled\"], [\"name\", \"chevron-back\"], [1, \"slider-nav\", \"next-btn\", 3, \"click\", \"disabled\"], [\"name\", \"chevron-forward\"], [1, \"users-slider-wrapper\", 3, \"mouseenter\", \"mouseleave\"], [1, \"users-slider\"], [\"class\", \"user-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"user-card\", 3, \"click\"], [1, \"user-avatar-container\"], [\"loading\", \"lazy\", 1, \"user-avatar\", 3, \"src\", \"alt\"], [\"class\", \"influencer-badge\", 4, \"ngIf\"], [1, \"user-info\"], [1, \"user-name\"], [1, \"username\"], [1, \"follower-count\"], [1, \"category-tag\"], [1, \"followed-by\"], [1, \"follow-btn\", 3, \"click\"], [1, \"influencer-badge\"], [\"name\", \"star\"], [1, \"empty-container\"], [\"name\", \"people-outline\", 1, \"empty-icon\"], [1, \"empty-title\"], [1, \"empty-message\"]],\n      template: function SuggestedForYouComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, SuggestedForYouComponent_div_1_Template, 19, 8, \"div\", 1);\n          i0.ɵɵelementStart(2, \"div\", 2)(3, \"div\", 3)(4, \"h2\", 4);\n          i0.ɵɵelement(5, \"ion-icon\", 5);\n          i0.ɵɵtext(6, \" Suggested for you \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"p\", 6);\n          i0.ɵɵtext(8, \"Discover amazing fashion creators\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(9, SuggestedForYouComponent_div_9_Template, 3, 2, \"div\", 7)(10, SuggestedForYouComponent_div_10_Template, 7, 1, \"div\", 8)(11, SuggestedForYouComponent_div_11_Template, 8, 6, \"div\", 9)(12, SuggestedForYouComponent_div_12_Template, 6, 0, \"div\", 10);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isMobile);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.error && !ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.suggestedUsers.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.suggestedUsers.length === 0);\n        }\n      },\n      dependencies: [CommonModule, i2.NgForOf, i2.NgIf, IonicModule, i3.IonIcon, CarouselModule],\n      styles: [\".suggested-users-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n  border-radius: 16px;\\n  margin-bottom: 24px;\\n  position: relative;\\n  max-width: 675px;\\n}\\n\\n.mobile-action-buttons[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 15px;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  display: flex;\\n  flex-direction: column;\\n  gap: 20px;\\n  z-index: 10;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%] {\\n  width: 48px;\\n  height: 48px;\\n  border-radius: 50%;\\n  border: none;\\n  background: rgba(108, 92, 231, 0.2);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  color: #6c5ce7;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  position: relative;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  margin-bottom: 2px;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   .action-count[_ngcontent-%COMP%], .mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   .action-text[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n  font-weight: 600;\\n  line-height: 1;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1);\\n  background: rgba(108, 92, 231, 0.3);\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active[_ngcontent-%COMP%] {\\n  background: rgba(108, 92, 231, 0.9);\\n  color: white;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active.like-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 48, 64, 0.9);\\n  color: white;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active.like-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_heartBeat 0.6s ease-in-out;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active.bookmark-btn[_ngcontent-%COMP%] {\\n  background: rgba(255, 215, 0, 0.9);\\n  color: white;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.like-btn.active[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  color: white;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.music-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ff3040 0%, #6c5ce7 100%);\\n  color: white;\\n}\\n.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.music-btn[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1) rotate(15deg);\\n}\\n\\n@keyframes _ngcontent-%COMP%_heartBeat {\\n  0% {\\n    transform: scale(1);\\n  }\\n  25% {\\n    transform: scale(1.3);\\n  }\\n  50% {\\n    transform: scale(1.1);\\n  }\\n  75% {\\n    transform: scale(1.25);\\n  }\\n  100% {\\n    transform: scale(1);\\n  }\\n}\\n@media (min-width: 769px) {\\n  .mobile-action-buttons[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n.section-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 24px;\\n}\\n.section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 700;\\n  color: #1a1a1a;\\n  margin: 0 0 8px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   .title-icon[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  color: #6c5ce7;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-subtitle[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #666;\\n  margin: 0;\\n}\\n\\n.loading-container[_ngcontent-%COMP%]   .loading-grid[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 20px;\\n  overflow-x: auto;\\n  padding-bottom: 8px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-user-card[_ngcontent-%COMP%] {\\n  flex: 0 0 180px;\\n  background: rgba(255, 255, 255, 0.7);\\n  border-radius: 16px;\\n  padding: 20px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-user-card[_ngcontent-%COMP%]   .loading-avatar[_ngcontent-%COMP%] {\\n  width: 80px;\\n  height: 80px;\\n  border-radius: 50%;\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n  margin: 0 auto 16px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-user-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line[_ngcontent-%COMP%] {\\n  height: 12px;\\n  border-radius: 6px;\\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\\n  background-size: 200% 100%;\\n  animation: _ngcontent-%COMP%_loading 1.5s infinite;\\n  margin-bottom: 8px;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-user-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.short[_ngcontent-%COMP%] {\\n  width: 60%;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-user-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.medium[_ngcontent-%COMP%] {\\n  width: 80%;\\n}\\n.loading-container[_ngcontent-%COMP%]   .loading-user-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.long[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n@keyframes _ngcontent-%COMP%_loading {\\n  0% {\\n    background-position: 200% 0;\\n  }\\n  100% {\\n    background-position: -200% 0;\\n  }\\n}\\n.error-container[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 40px 20px;\\n}\\n.error-container[_ngcontent-%COMP%]   .error-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  color: #e74c3c;\\n  margin-bottom: 16px;\\n}\\n.error-container[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%] {\\n  color: #666;\\n  margin-bottom: 20px;\\n  font-size: 16px;\\n}\\n.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);\\n  color: white;\\n  border: none;\\n  padding: 12px 24px;\\n  border-radius: 25px;\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin: 0 auto;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 8px 25px rgba(231, 76, 60, 0.3);\\n}\\n\\n.users-slider-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  margin: 0 -20px;\\n}\\n.users-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  z-index: 10;\\n  background: rgba(0, 0, 0, 0.7);\\n  color: white;\\n  border: none;\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.users-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: rgba(0, 0, 0, 0.9);\\n  transform: translateY(-50%) scale(1.1);\\n}\\n.users-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.3;\\n  cursor: not-allowed;\\n}\\n.users-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n}\\n.users-slider-container[_ngcontent-%COMP%]   .slider-nav.prev-btn[_ngcontent-%COMP%] {\\n  left: -20px;\\n}\\n.users-slider-container[_ngcontent-%COMP%]   .slider-nav.next-btn[_ngcontent-%COMP%] {\\n  right: -20px;\\n}\\n\\n.users-slider-wrapper[_ngcontent-%COMP%] {\\n  overflow: hidden;\\n  padding: 0 20px;\\n}\\n\\n.users-slider[_ngcontent-%COMP%] {\\n  display: flex;\\n  transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);\\n  gap: 20px;\\n}\\n.users-slider[_ngcontent-%COMP%]   .user-card[_ngcontent-%COMP%] {\\n  flex: 0 0 180px;\\n  width: 180px;\\n}\\n\\n.user-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 16px;\\n  padding: 20px;\\n  text-align: center;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n}\\n.user-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-8px);\\n  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);\\n}\\n\\n.user-avatar-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  margin-bottom: 16px;\\n}\\n.user-avatar-container[_ngcontent-%COMP%]   .user-avatar[_ngcontent-%COMP%] {\\n  width: 80px;\\n  height: 80px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n  border: 3px solid #6c5ce7;\\n  margin: 0 auto;\\n  display: block;\\n}\\n.user-avatar-container[_ngcontent-%COMP%]   .influencer-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -5px;\\n  right: calc(50% - 45px);\\n  background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%);\\n  color: white;\\n  width: 24px;\\n  height: 24px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 12px;\\n  border: 2px solid white;\\n}\\n.user-avatar-container[_ngcontent-%COMP%]   .influencer-badge[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n}\\n\\n.user-info[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n.user-info[_ngcontent-%COMP%]   .user-name[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 600;\\n  color: #1a1a1a;\\n  margin: 0 0 4px 0;\\n}\\n.user-info[_ngcontent-%COMP%]   .username[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #6c5ce7;\\n  margin: 0 0 8px 0;\\n  font-weight: 500;\\n}\\n.user-info[_ngcontent-%COMP%]   .follower-count[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #666;\\n  margin: 0 0 4px 0;\\n  font-weight: 600;\\n}\\n.user-info[_ngcontent-%COMP%]   .category-tag[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: #6c5ce7;\\n  background: rgba(108, 92, 231, 0.1);\\n  padding: 2px 8px;\\n  border-radius: 12px;\\n  display: inline-block;\\n  margin: 0 0 8px 0;\\n}\\n.user-info[_ngcontent-%COMP%]   .followed-by[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n  color: #999;\\n  margin: 0;\\n  line-height: 1.3;\\n}\\n\\n.follow-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #6c5ce7 0%, #5a4fcf 100%);\\n  color: white;\\n  border: none;\\n  padding: 8px 16px;\\n  border-radius: 20px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n  margin: 0 auto;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.follow-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 6px 20px rgba(108, 92, 231, 0.3);\\n}\\n.follow-btn.following[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #00b894 0%, #00a085 100%);\\n}\\n.follow-btn.following[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 6px 20px rgba(0, 184, 148, 0.3);\\n}\\n.follow-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n}\\n\\n.empty-container[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 60px 20px;\\n}\\n.empty-container[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%] {\\n  font-size: 64px;\\n  color: #ddd;\\n  margin-bottom: 20px;\\n}\\n.empty-container[_ngcontent-%COMP%]   .empty-title[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: #666;\\n  margin: 0 0 8px 0;\\n}\\n.empty-container[_ngcontent-%COMP%]   .empty-message[_ngcontent-%COMP%] {\\n  color: #999;\\n  margin: 0;\\n}\\n\\n@media (max-width: 1200px) {\\n  .users-slider[_ngcontent-%COMP%]   .user-card[_ngcontent-%COMP%] {\\n    flex: 0 0 170px;\\n    width: 170px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .users-slider-container[_ngcontent-%COMP%] {\\n    margin: 0 -10px;\\n  }\\n  .users-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%] {\\n    width: 35px;\\n    height: 35px;\\n  }\\n  .users-slider-container[_ngcontent-%COMP%]   .slider-nav.prev-btn[_ngcontent-%COMP%] {\\n    left: -15px;\\n  }\\n  .users-slider-container[_ngcontent-%COMP%]   .slider-nav.next-btn[_ngcontent-%COMP%] {\\n    right: -15px;\\n  }\\n  .users-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n  .users-slider-wrapper[_ngcontent-%COMP%] {\\n    padding: 0 10px;\\n  }\\n  .users-slider[_ngcontent-%COMP%] {\\n    gap: 15px;\\n  }\\n  .users-slider[_ngcontent-%COMP%]   .user-card[_ngcontent-%COMP%] {\\n    flex: 0 0 160px;\\n    width: 160px;\\n    padding: 16px;\\n  }\\n  .user-avatar-container[_ngcontent-%COMP%]   .user-avatar[_ngcontent-%COMP%] {\\n    width: 70px;\\n    height: 70px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .users-slider[_ngcontent-%COMP%]   .user-card[_ngcontent-%COMP%] {\\n    flex: 0 0 150px;\\n    width: 150px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "Subscription", "IonicModule", "CarouselModule", "i0", "ɵɵelementStart", "ɵɵlistener", "SuggestedForYouComponent_div_1_Template_button_click_1_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "toggleSectionLike", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "SuggestedForYouComponent_div_1_Template_button_click_5_listener", "openComments", "SuggestedForYouComponent_div_1_Template_button_click_9_listener", "shareSection", "SuggestedForYouComponent_div_1_Template_button_click_13_listener", "toggleSectionBookmark", "SuggestedForYouComponent_div_1_Template_button_click_15_listener", "openMusicPlayer", "ɵɵadvance", "ɵɵclassProp", "isSectionLiked", "ɵɵproperty", "ɵɵtextInterpolate", "formatCount", "sectionLikes", "sectionComments", "isSectionBookmarked", "ɵɵtemplate", "SuggestedForYouComponent_div_9_div_2_Template", "ɵɵpureFunction0", "_c0", "SuggestedForYouComponent_div_10_Template_button_click_4_listener", "_r3", "onRetry", "error", "SuggestedForYouComponent_div_11_div_7_Template_div_click_0_listener", "user_r6", "_r5", "$implicit", "onUserClick", "SuggestedForYouComponent_div_11_div_7_div_3_Template", "SuggestedForYouComponent_div_11_div_7_Template_button_click_15_listener", "$event", "onFollowUser", "avatar", "ɵɵsanitizeUrl", "fullName", "isInfluencer", "ɵɵtextInterpolate1", "username", "formatFollowerCount", "followerCount", "category", "<PERSON><PERSON><PERSON>", "isFollowing", "SuggestedForYouComponent_div_11_Template_button_click_1_listener", "_r4", "slidePrev", "SuggestedForYouComponent_div_11_Template_button_click_3_listener", "slideNext", "SuggestedForYouComponent_div_11_Template_div_mouseenter_5_listener", "pauseAutoSlide", "SuggestedForYouComponent_div_11_Template_div_mouseleave_5_listener", "resumeAutoSlide", "SuggestedForYouComponent_div_11_div_7_Template", "currentSlide", "maxSlide", "ɵɵstyleProp", "slideOffset", "suggestedUsers", "trackByUserId", "SuggestedForYouComponent", "constructor", "router", "isLoading", "subscription", "<PERSON><PERSON><PERSON><PERSON>", "visibleCards", "autoSlideDelay", "isAutoSliding", "isPaused", "isMobile", "ngOnInit", "loadSuggestedUsers", "updateResponsiveSettings", "setupResizeListener", "checkMobileDevice", "ngOnDestroy", "unsubscribe", "stopAutoSlide", "_this", "_asyncToGenerator", "id", "updateSliderOnUsersLoad", "console", "user", "navigate", "event", "stopPropagation", "count", "toFixed", "toString", "index", "startAutoSlide", "autoSlideInterval", "setInterval", "length", "autoSlideNext", "clearInterval", "updateSlideOffset", "width", "window", "innerWidth", "updateSliderLimits", "addEventListener", "Math", "max", "restartAutoSlideAfterInteraction", "setTimeout", "log", "navigator", "share", "title", "text", "url", "location", "href", "clipboard", "writeText", "ɵɵdirectiveInject", "i1", "Router", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SuggestedForYouComponent_Template", "rf", "ctx", "SuggestedForYouComponent_div_1_Template", "SuggestedForYouComponent_div_9_Template", "SuggestedForYouComponent_div_10_Template", "SuggestedForYouComponent_div_11_Template", "SuggestedForYouComponent_div_12_Template", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i3", "IonIcon", "styles"], "sources": ["E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\features\\home\\components\\suggested-for-you\\suggested-for-you.component.ts", "E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\features\\home\\components\\suggested-for-you\\suggested-for-you.component.html"], "sourcesContent": ["import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { Router } from '@angular/router';\r\nimport { Subscription } from 'rxjs';\r\nimport { IonicModule } from '@ionic/angular';\r\nimport { CarouselModule } from 'ngx-owl-carousel-o';\r\n\r\ninterface SuggestedUser {\r\n  id: string;\r\n  username: string;\r\n  fullName: string;\r\n  avatar: string;\r\n  followedBy: string;\r\n  isFollowing: boolean;\r\n  isInfluencer: boolean;\r\n  followerCount: number;\r\n  category: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-suggested-for-you',\r\n  standalone: true,\r\n  imports: [CommonModule, IonicModule, CarouselModule],\r\n  templateUrl: './suggested-for-you.component.html',\r\n  styleUrls: ['./suggested-for-you.component.scss']\r\n})\r\nexport class SuggestedForYouComponent implements OnInit, OnDestroy {\r\n  suggestedUsers: SuggestedUser[] = [];\r\n  isLoading = true;\r\n  error: string | null = null;\r\n  private subscription: Subscription = new Subscription();\r\n\r\n  // Slider properties\r\n  currentSlide = 0;\r\n  slideOffset = 0;\r\n  cardWidth = 200; // Width of each user card including margin\r\n  visibleCards = 4; // Number of cards visible at once\r\n  maxSlide = 0;\r\n  \r\n  // Auto-sliding properties\r\n  autoSlideInterval: any;\r\n  autoSlideDelay = 5000; // 5 seconds for users\r\n  isAutoSliding = true;\r\n  isPaused = false;\r\n\r\n  // Section interaction properties\r\n  isSectionLiked = false;\r\n  isSectionBookmarked = false;\r\n  sectionLikes = 198;\r\n  sectionComments = 67;\r\n  isMobile = false;\r\n\r\n  constructor(private router: Router) {}\r\n\r\n  ngOnInit() {\r\n    this.loadSuggestedUsers();\r\n    this.updateResponsiveSettings();\r\n    this.setupResizeListener();\r\n    this.checkMobileDevice();\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.subscription.unsubscribe();\r\n    this.stopAutoSlide();\r\n  }\r\n\r\n  private async loadSuggestedUsers() {\r\n    try {\r\n      this.isLoading = true;\r\n      this.error = null;\r\n      \r\n      // Mock data for suggested users\r\n      this.suggestedUsers = [\r\n        {\r\n          id: '1',\r\n          username: 'fashionista_maya',\r\n          fullName: 'Maya Patel',\r\n          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=150&h=150&fit=crop&crop=face',\r\n          followedBy: 'Followed by john_doe and 12 others',\r\n          isFollowing: false,\r\n          isInfluencer: true,\r\n          followerCount: 45000,\r\n          category: 'Fashion'\r\n        },\r\n        {\r\n          id: '2',\r\n          username: 'style_guru_raj',\r\n          fullName: 'Raj Kumar',\r\n          avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',\r\n          followedBy: 'Followed by sarah_k and 8 others',\r\n          isFollowing: false,\r\n          isInfluencer: true,\r\n          followerCount: 32000,\r\n          category: 'Menswear'\r\n        },\r\n        {\r\n          id: '3',\r\n          username: 'trendy_sara',\r\n          fullName: 'Sara Johnson',\r\n          avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',\r\n          followedBy: 'Followed by alex_m and 15 others',\r\n          isFollowing: false,\r\n          isInfluencer: false,\r\n          followerCount: 8500,\r\n          category: 'Casual'\r\n        },\r\n        {\r\n          id: '4',\r\n          username: 'luxury_lover',\r\n          fullName: 'Emma Wilson',\r\n          avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face',\r\n          followedBy: 'Followed by mike_t and 20 others',\r\n          isFollowing: false,\r\n          isInfluencer: true,\r\n          followerCount: 67000,\r\n          category: 'Luxury'\r\n        },\r\n        {\r\n          id: '5',\r\n          username: 'street_style_alex',\r\n          fullName: 'Alex Chen',\r\n          avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',\r\n          followedBy: 'Followed by lisa_p and 5 others',\r\n          isFollowing: false,\r\n          isInfluencer: false,\r\n          followerCount: 12000,\r\n          category: 'Streetwear'\r\n        },\r\n        {\r\n          id: '6',\r\n          username: 'boho_bella',\r\n          fullName: 'Isabella Rodriguez',\r\n          avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face',\r\n          followedBy: 'Followed by tom_h and 18 others',\r\n          isFollowing: false,\r\n          isInfluencer: true,\r\n          followerCount: 28000,\r\n          category: 'Boho'\r\n        }\r\n      ];\r\n      \r\n      this.isLoading = false;\r\n      this.updateSliderOnUsersLoad();\r\n    } catch (error) {\r\n      console.error('Error loading suggested users:', error);\r\n      this.error = 'Failed to load suggested users';\r\n      this.isLoading = false;\r\n    }\r\n  }\r\n\r\n  onUserClick(user: SuggestedUser) {\r\n    this.router.navigate(['/profile', user.username]);\r\n  }\r\n\r\n  onFollowUser(user: SuggestedUser, event: Event) {\r\n    event.stopPropagation();\r\n    user.isFollowing = !user.isFollowing;\r\n    \r\n    if (user.isFollowing) {\r\n      user.followerCount++;\r\n    } else {\r\n      user.followerCount--;\r\n    }\r\n  }\r\n\r\n  formatFollowerCount(count: number): string {\r\n    if (count >= 1000000) {\r\n      return (count / 1000000).toFixed(1) + 'M';\r\n    } else if (count >= 1000) {\r\n      return (count / 1000).toFixed(1) + 'K';\r\n    }\r\n    return count.toString();\r\n  }\r\n\r\n  onRetry() {\r\n    this.loadSuggestedUsers();\r\n  }\r\n\r\n  trackByUserId(index: number, user: SuggestedUser): string {\r\n    return user.id;\r\n  }\r\n\r\n  // Auto-sliding methods\r\n  private startAutoSlide() {\r\n    if (!this.isAutoSliding || this.isPaused) return;\r\n    \r\n    this.stopAutoSlide();\r\n    this.autoSlideInterval = setInterval(() => {\r\n      if (!this.isPaused && this.suggestedUsers.length > this.visibleCards) {\r\n        this.autoSlideNext();\r\n      }\r\n    }, this.autoSlideDelay);\r\n  }\r\n\r\n  private stopAutoSlide() {\r\n    if (this.autoSlideInterval) {\r\n      clearInterval(this.autoSlideInterval);\r\n      this.autoSlideInterval = null;\r\n    }\r\n  }\r\n\r\n  private autoSlideNext() {\r\n    if (this.currentSlide >= this.maxSlide) {\r\n      this.currentSlide = 0;\r\n    } else {\r\n      this.currentSlide++;\r\n    }\r\n    this.updateSlideOffset();\r\n  }\r\n\r\n  pauseAutoSlide() {\r\n    this.isPaused = true;\r\n    this.stopAutoSlide();\r\n  }\r\n\r\n  resumeAutoSlide() {\r\n    this.isPaused = false;\r\n    this.startAutoSlide();\r\n  }\r\n\r\n  // Responsive methods\r\n  private updateResponsiveSettings() {\r\n    const width = window.innerWidth;\r\n    if (width <= 480) {\r\n      this.cardWidth = 180;\r\n      this.visibleCards = 1;\r\n    } else if (width <= 768) {\r\n      this.cardWidth = 200;\r\n      this.visibleCards = 2;\r\n    } else if (width <= 1200) {\r\n      this.cardWidth = 220;\r\n      this.visibleCards = 3;\r\n    } else {\r\n      this.cardWidth = 220;\r\n      this.visibleCards = 4;\r\n    }\r\n    this.updateSliderLimits();\r\n    this.updateSlideOffset();\r\n  }\r\n\r\n  private setupResizeListener() {\r\n    window.addEventListener('resize', () => {\r\n      this.updateResponsiveSettings();\r\n    });\r\n  }\r\n\r\n  // Slider methods\r\n  updateSliderLimits() {\r\n    this.maxSlide = Math.max(0, this.suggestedUsers.length - this.visibleCards);\r\n  }\r\n\r\n  slidePrev() {\r\n    if (this.currentSlide > 0) {\r\n      this.currentSlide--;\r\n      this.updateSlideOffset();\r\n      this.restartAutoSlideAfterInteraction();\r\n    }\r\n  }\r\n\r\n  slideNext() {\r\n    if (this.currentSlide < this.maxSlide) {\r\n      this.currentSlide++;\r\n      this.updateSlideOffset();\r\n      this.restartAutoSlideAfterInteraction();\r\n    }\r\n  }\r\n\r\n  private updateSlideOffset() {\r\n    this.slideOffset = -this.currentSlide * this.cardWidth;\r\n  }\r\n\r\n  private restartAutoSlideAfterInteraction() {\r\n    this.stopAutoSlide();\r\n    setTimeout(() => {\r\n      this.startAutoSlide();\r\n    }, 2000);\r\n  }\r\n\r\n  // Update slider when users load\r\n  private updateSliderOnUsersLoad() {\r\n    setTimeout(() => {\r\n      this.updateSliderLimits();\r\n      this.currentSlide = 0;\r\n      this.slideOffset = 0;\r\n      this.startAutoSlide();\r\n    }, 100);\r\n  }\r\n\r\n  // Section interaction methods\r\n  toggleSectionLike() {\r\n    this.isSectionLiked = !this.isSectionLiked;\r\n    if (this.isSectionLiked) {\r\n      this.sectionLikes++;\r\n    } else {\r\n      this.sectionLikes--;\r\n    }\r\n  }\r\n\r\n  toggleSectionBookmark() {\r\n    this.isSectionBookmarked = !this.isSectionBookmarked;\r\n  }\r\n\r\n  openComments() {\r\n    console.log('Opening comments for suggested users section');\r\n  }\r\n\r\n  shareSection() {\r\n    if (navigator.share) {\r\n      navigator.share({\r\n        title: 'Suggested for You',\r\n        text: 'Discover amazing fashion creators!',\r\n        url: window.location.href\r\n      });\r\n    } else {\r\n      navigator.clipboard.writeText(window.location.href);\r\n      console.log('Link copied to clipboard');\r\n    }\r\n  }\r\n\r\n  openMusicPlayer() {\r\n    console.log('Opening music player for suggested users');\r\n  }\r\n\r\n  formatCount(count: number): string {\r\n    if (count >= 1000000) {\r\n      return (count / 1000000).toFixed(1) + 'M';\r\n    } else if (count >= 1000) {\r\n      return (count / 1000).toFixed(1) + 'K';\r\n    }\r\n    return count.toString();\r\n  }\r\n\r\n  private checkMobileDevice() {\r\n    this.isMobile = window.innerWidth <= 768;\r\n  }\r\n}\r\n", "<div class=\"suggested-users-container\">\r\n  <!-- Mobile Action Buttons (TikTok/Instagram Style) -->\r\n  <div class=\"mobile-action-buttons\" *ngIf=\"isMobile\">\r\n    <button class=\"action-btn like-btn\"\r\n            [class.active]=\"isSectionLiked\"\r\n            (click)=\"toggleSectionLike()\">\r\n      <ion-icon [name]=\"isSectionLiked ? 'heart' : 'heart-outline'\"></ion-icon>\r\n      <span class=\"action-count\">{{ formatCount(sectionLikes) }}</span>\r\n    </button>\r\n\r\n    <button class=\"action-btn comment-btn\" (click)=\"openComments()\">\r\n      <ion-icon name=\"chatbubble-outline\"></ion-icon>\r\n      <span class=\"action-count\">{{ formatCount(sectionComments) }}</span>\r\n    </button>\r\n\r\n    <button class=\"action-btn share-btn\" (click)=\"shareSection()\">\r\n      <ion-icon name=\"arrow-redo-outline\"></ion-icon>\r\n      <span class=\"action-text\">Share</span>\r\n    </button>\r\n\r\n    <button class=\"action-btn bookmark-btn\"\r\n            [class.active]=\"isSectionBookmarked\"\r\n            (click)=\"toggleSectionBookmark()\">\r\n      <ion-icon [name]=\"isSectionBookmarked ? 'bookmark' : 'bookmark-outline'\"></ion-icon>\r\n    </button>\r\n\r\n    <button class=\"action-btn music-btn\" (click)=\"openMusicPlayer()\">\r\n      <ion-icon name=\"musical-notes\"></ion-icon>\r\n      <span class=\"action-text\">Music</span>\r\n    </button>\r\n  </div>\r\n\r\n  <!-- Header -->\r\n  <div class=\"section-header\">\r\n    <div class=\"header-content\">\r\n      <h2 class=\"section-title\">\r\n        <ion-icon name=\"people\" class=\"title-icon\"></ion-icon>\r\n        Suggested for you\r\n      </h2>\r\n      <p class=\"section-subtitle\">Discover amazing fashion creators</p>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Loading State -->\r\n  <div *ngIf=\"isLoading\" class=\"loading-container\">\r\n    <div class=\"loading-grid\">\r\n      <div *ngFor=\"let item of [1,2,3,4]\" class=\"loading-user-card\">\r\n        <div class=\"loading-avatar\"></div>\r\n        <div class=\"loading-content\">\r\n          <div class=\"loading-line short\"></div>\r\n          <div class=\"loading-line medium\"></div>\r\n          <div class=\"loading-line long\"></div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Error State -->\r\n  <div *ngIf=\"error && !isLoading\" class=\"error-container\">\r\n    <ion-icon name=\"alert-circle\" class=\"error-icon\"></ion-icon>\r\n    <p class=\"error-message\">{{ error }}</p>\r\n    <button class=\"retry-btn\" (click)=\"onRetry()\">\r\n      <ion-icon name=\"refresh\"></ion-icon>\r\n      Try Again\r\n    </button>\r\n  </div>\r\n\r\n  <!-- Users Slider -->\r\n  <div *ngIf=\"!isLoading && !error && suggestedUsers.length > 0\" class=\"users-slider-container\">\r\n    <!-- Navigation Buttons -->\r\n    <button class=\"slider-nav prev-btn\" (click)=\"slidePrev()\" [disabled]=\"currentSlide === 0\">\r\n      <ion-icon name=\"chevron-back\"></ion-icon>\r\n    </button>\r\n    <button class=\"slider-nav next-btn\" (click)=\"slideNext()\" [disabled]=\"currentSlide >= maxSlide\">\r\n      <ion-icon name=\"chevron-forward\"></ion-icon>\r\n    </button>\r\n    \r\n    <!-- Slider Wrapper -->\r\n    <div class=\"users-slider-wrapper\" (mouseenter)=\"pauseAutoSlide()\" (mouseleave)=\"resumeAutoSlide()\">\r\n      <div class=\"users-slider\" [style.transform]=\"'translateX(' + slideOffset + 'px)'\">\r\n        <div \r\n          *ngFor=\"let user of suggestedUsers; trackBy: trackByUserId\" \r\n          class=\"user-card\"\r\n          (click)=\"onUserClick(user)\"\r\n        >\r\n          <!-- User Avatar -->\r\n          <div class=\"user-avatar-container\">\r\n            <img \r\n              [src]=\"user.avatar\"\r\n              [alt]=\"user.fullName\"\r\n              class=\"user-avatar\"\r\n              loading=\"lazy\"\r\n            />\r\n            <div *ngIf=\"user.isInfluencer\" class=\"influencer-badge\">\r\n              <ion-icon name=\"star\"></ion-icon>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- User Info -->\r\n          <div class=\"user-info\">\r\n            <h3 class=\"user-name\">{{ user.fullName }}</h3>\r\n            <p class=\"username\">&#64;{{ user.username }}</p>\r\n            <p class=\"follower-count\">{{ formatFollowerCount(user.followerCount) }} followers</p>\r\n            <p class=\"category-tag\">{{ user.category }}</p>\r\n            <p class=\"followed-by\">{{ user.followedBy }}</p>\r\n          </div>\r\n\r\n          <!-- Follow Button -->\r\n          <button \r\n            class=\"follow-btn\"\r\n            [class.following]=\"user.isFollowing\"\r\n            (click)=\"onFollowUser(user, $event)\"\r\n          >\r\n            <span>{{ user.isFollowing ? 'Following' : 'Follow' }}</span>\r\n            <ion-icon [name]=\"user.isFollowing ? 'checkmark' : 'add'\"></ion-icon>\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div> <!-- End users-slider-wrapper -->\r\n  </div> <!-- End users-slider-container -->\r\n\r\n  <!-- Empty State -->\r\n  <div *ngIf=\"!isLoading && !error && suggestedUsers.length === 0\" class=\"empty-container\">\r\n    <ion-icon name=\"people-outline\" class=\"empty-icon\"></ion-icon>\r\n    <h3 class=\"empty-title\">No Suggestions</h3>\r\n    <p class=\"empty-message\">Check back later for user suggestions</p>\r\n  </div>\r\n</div>\r\n"], "mappings": ";AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,YAAY,QAAQ,MAAM;AACnC,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,cAAc,QAAQ,oBAAoB;;;;;;;;;ICF/CC,EADF,CAAAC,cAAA,cAAoD,iBAGZ;IAA9BD,EAAA,CAAAE,UAAA,mBAAAC,gEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,iBAAA,EAAmB;IAAA,EAAC;IACnCT,EAAA,CAAAU,SAAA,mBAAyE;IACzEV,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAW,MAAA,GAA+B;IAC5DX,EAD4D,CAAAY,YAAA,EAAO,EAC1D;IAETZ,EAAA,CAAAC,cAAA,iBAAgE;IAAzBD,EAAA,CAAAE,UAAA,mBAAAW,gEAAA;MAAAb,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAQ,YAAA,EAAc;IAAA,EAAC;IAC7Dd,EAAA,CAAAU,SAAA,mBAA+C;IAC/CV,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAW,MAAA,GAAkC;IAC/DX,EAD+D,CAAAY,YAAA,EAAO,EAC7D;IAETZ,EAAA,CAAAC,cAAA,iBAA8D;IAAzBD,EAAA,CAAAE,UAAA,mBAAAa,gEAAA;MAAAf,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAU,YAAA,EAAc;IAAA,EAAC;IAC3DhB,EAAA,CAAAU,SAAA,oBAA+C;IAC/CV,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAW,MAAA,aAAK;IACjCX,EADiC,CAAAY,YAAA,EAAO,EAC/B;IAETZ,EAAA,CAAAC,cAAA,kBAE0C;IAAlCD,EAAA,CAAAE,UAAA,mBAAAe,iEAAA;MAAAjB,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAY,qBAAA,EAAuB;IAAA,EAAC;IACvClB,EAAA,CAAAU,SAAA,oBAAoF;IACtFV,EAAA,CAAAY,YAAA,EAAS;IAETZ,EAAA,CAAAC,cAAA,kBAAiE;IAA5BD,EAAA,CAAAE,UAAA,mBAAAiB,iEAAA;MAAAnB,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAc,eAAA,EAAiB;IAAA,EAAC;IAC9DpB,EAAA,CAAAU,SAAA,oBAA0C;IAC1CV,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAW,MAAA,aAAK;IAEnCX,EAFmC,CAAAY,YAAA,EAAO,EAC/B,EACL;;;;IA1BIZ,EAAA,CAAAqB,SAAA,EAA+B;IAA/BrB,EAAA,CAAAsB,WAAA,WAAAhB,MAAA,CAAAiB,cAAA,CAA+B;IAE3BvB,EAAA,CAAAqB,SAAA,EAAmD;IAAnDrB,EAAA,CAAAwB,UAAA,SAAAlB,MAAA,CAAAiB,cAAA,6BAAmD;IAClCvB,EAAA,CAAAqB,SAAA,GAA+B;IAA/BrB,EAAA,CAAAyB,iBAAA,CAAAnB,MAAA,CAAAoB,WAAA,CAAApB,MAAA,CAAAqB,YAAA,EAA+B;IAK/B3B,EAAA,CAAAqB,SAAA,GAAkC;IAAlCrB,EAAA,CAAAyB,iBAAA,CAAAnB,MAAA,CAAAoB,WAAA,CAAApB,MAAA,CAAAsB,eAAA,EAAkC;IASvD5B,EAAA,CAAAqB,SAAA,GAAoC;IAApCrB,EAAA,CAAAsB,WAAA,WAAAhB,MAAA,CAAAuB,mBAAA,CAAoC;IAEhC7B,EAAA,CAAAqB,SAAA,EAA8D;IAA9DrB,EAAA,CAAAwB,UAAA,SAAAlB,MAAA,CAAAuB,mBAAA,mCAA8D;;;;;IAuBxE7B,EAAA,CAAAC,cAAA,cAA8D;IAC5DD,EAAA,CAAAU,SAAA,cAAkC;IAClCV,EAAA,CAAAC,cAAA,cAA6B;IAG3BD,EAFA,CAAAU,SAAA,cAAsC,cACC,cACF;IAEzCV,EADE,CAAAY,YAAA,EAAM,EACF;;;;;IARRZ,EADF,CAAAC,cAAA,cAAiD,cACrB;IACxBD,EAAA,CAAA8B,UAAA,IAAAC,6CAAA,kBAA8D;IASlE/B,EADE,CAAAY,YAAA,EAAM,EACF;;;IAToBZ,EAAA,CAAAqB,SAAA,GAAY;IAAZrB,EAAA,CAAAwB,UAAA,YAAAxB,EAAA,CAAAgC,eAAA,IAAAC,GAAA,EAAY;;;;;;IAYtCjC,EAAA,CAAAC,cAAA,cAAyD;IACvDD,EAAA,CAAAU,SAAA,mBAA4D;IAC5DV,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAW,MAAA,GAAW;IAAAX,EAAA,CAAAY,YAAA,EAAI;IACxCZ,EAAA,CAAAC,cAAA,iBAA8C;IAApBD,EAAA,CAAAE,UAAA,mBAAAgC,iEAAA;MAAAlC,EAAA,CAAAI,aAAA,CAAA+B,GAAA;MAAA,MAAA7B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA8B,OAAA,EAAS;IAAA,EAAC;IAC3CpC,EAAA,CAAAU,SAAA,mBAAoC;IACpCV,EAAA,CAAAW,MAAA,kBACF;IACFX,EADE,CAAAY,YAAA,EAAS,EACL;;;;IALqBZ,EAAA,CAAAqB,SAAA,GAAW;IAAXrB,EAAA,CAAAyB,iBAAA,CAAAnB,MAAA,CAAA+B,KAAA,CAAW;;;;;IAiC5BrC,EAAA,CAAAC,cAAA,cAAwD;IACtDD,EAAA,CAAAU,SAAA,mBAAiC;IACnCV,EAAA,CAAAY,YAAA,EAAM;;;;;;IAfVZ,EAAA,CAAAC,cAAA,cAIC;IADCD,EAAA,CAAAE,UAAA,mBAAAoC,oEAAA;MAAA,MAAAC,OAAA,GAAAvC,EAAA,CAAAI,aAAA,CAAAoC,GAAA,EAAAC,SAAA;MAAA,MAAAnC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAoC,WAAA,CAAAH,OAAA,CAAiB;IAAA,EAAC;IAG3BvC,EAAA,CAAAC,cAAA,cAAmC;IACjCD,EAAA,CAAAU,SAAA,cAKE;IACFV,EAAA,CAAA8B,UAAA,IAAAa,oDAAA,kBAAwD;IAG1D3C,EAAA,CAAAY,YAAA,EAAM;IAIJZ,EADF,CAAAC,cAAA,cAAuB,aACC;IAAAD,EAAA,CAAAW,MAAA,GAAmB;IAAAX,EAAA,CAAAY,YAAA,EAAK;IAC9CZ,EAAA,CAAAC,cAAA,YAAoB;IAAAD,EAAA,CAAAW,MAAA,GAAwB;IAAAX,EAAA,CAAAY,YAAA,EAAI;IAChDZ,EAAA,CAAAC,cAAA,YAA0B;IAAAD,EAAA,CAAAW,MAAA,IAAuD;IAAAX,EAAA,CAAAY,YAAA,EAAI;IACrFZ,EAAA,CAAAC,cAAA,aAAwB;IAAAD,EAAA,CAAAW,MAAA,IAAmB;IAAAX,EAAA,CAAAY,YAAA,EAAI;IAC/CZ,EAAA,CAAAC,cAAA,aAAuB;IAAAD,EAAA,CAAAW,MAAA,IAAqB;IAC9CX,EAD8C,CAAAY,YAAA,EAAI,EAC5C;IAGNZ,EAAA,CAAAC,cAAA,kBAIC;IADCD,EAAA,CAAAE,UAAA,mBAAA0C,wEAAAC,MAAA;MAAA,MAAAN,OAAA,GAAAvC,EAAA,CAAAI,aAAA,CAAAoC,GAAA,EAAAC,SAAA;MAAA,MAAAnC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAwC,YAAA,CAAAP,OAAA,EAAAM,MAAA,CAA0B;IAAA,EAAC;IAEpC7C,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAW,MAAA,IAA+C;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAC5DZ,EAAA,CAAAU,SAAA,oBAAqE;IAEzEV,EADE,CAAAY,YAAA,EAAS,EACL;;;;;IA5BAZ,EAAA,CAAAqB,SAAA,GAAmB;IACnBrB,EADA,CAAAwB,UAAA,QAAAe,OAAA,CAAAQ,MAAA,EAAA/C,EAAA,CAAAgD,aAAA,CAAmB,QAAAT,OAAA,CAAAU,QAAA,CACE;IAIjBjD,EAAA,CAAAqB,SAAA,EAAuB;IAAvBrB,EAAA,CAAAwB,UAAA,SAAAe,OAAA,CAAAW,YAAA,CAAuB;IAOPlD,EAAA,CAAAqB,SAAA,GAAmB;IAAnBrB,EAAA,CAAAyB,iBAAA,CAAAc,OAAA,CAAAU,QAAA,CAAmB;IACrBjD,EAAA,CAAAqB,SAAA,GAAwB;IAAxBrB,EAAA,CAAAmD,kBAAA,MAAAZ,OAAA,CAAAa,QAAA,KAAwB;IAClBpD,EAAA,CAAAqB,SAAA,GAAuD;IAAvDrB,EAAA,CAAAmD,kBAAA,KAAA7C,MAAA,CAAA+C,mBAAA,CAAAd,OAAA,CAAAe,aAAA,gBAAuD;IACzDtD,EAAA,CAAAqB,SAAA,GAAmB;IAAnBrB,EAAA,CAAAyB,iBAAA,CAAAc,OAAA,CAAAgB,QAAA,CAAmB;IACpBvD,EAAA,CAAAqB,SAAA,GAAqB;IAArBrB,EAAA,CAAAyB,iBAAA,CAAAc,OAAA,CAAAiB,UAAA,CAAqB;IAM5CxD,EAAA,CAAAqB,SAAA,EAAoC;IAApCrB,EAAA,CAAAsB,WAAA,cAAAiB,OAAA,CAAAkB,WAAA,CAAoC;IAG9BzD,EAAA,CAAAqB,SAAA,GAA+C;IAA/CrB,EAAA,CAAAyB,iBAAA,CAAAc,OAAA,CAAAkB,WAAA,0BAA+C;IAC3CzD,EAAA,CAAAqB,SAAA,EAA+C;IAA/CrB,EAAA,CAAAwB,UAAA,SAAAe,OAAA,CAAAkB,WAAA,uBAA+C;;;;;;IA5CjEzD,EAFF,CAAAC,cAAA,cAA8F,iBAEF;IAAtDD,EAAA,CAAAE,UAAA,mBAAAwD,iEAAA;MAAA1D,EAAA,CAAAI,aAAA,CAAAuD,GAAA;MAAA,MAAArD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAsD,SAAA,EAAW;IAAA,EAAC;IACvD5D,EAAA,CAAAU,SAAA,mBAAyC;IAC3CV,EAAA,CAAAY,YAAA,EAAS;IACTZ,EAAA,CAAAC,cAAA,iBAAgG;IAA5DD,EAAA,CAAAE,UAAA,mBAAA2D,iEAAA;MAAA7D,EAAA,CAAAI,aAAA,CAAAuD,GAAA;MAAA,MAAArD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAwD,SAAA,EAAW;IAAA,EAAC;IACvD9D,EAAA,CAAAU,SAAA,mBAA4C;IAC9CV,EAAA,CAAAY,YAAA,EAAS;IAGTZ,EAAA,CAAAC,cAAA,cAAmG;IAAjCD,EAAhC,CAAAE,UAAA,wBAAA6D,mEAAA;MAAA/D,EAAA,CAAAI,aAAA,CAAAuD,GAAA;MAAA,MAAArD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAcF,MAAA,CAAA0D,cAAA,EAAgB;IAAA,EAAC,wBAAAC,mEAAA;MAAAjE,EAAA,CAAAI,aAAA,CAAAuD,GAAA;MAAA,MAAArD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAeF,MAAA,CAAA4D,eAAA,EAAiB;IAAA,EAAC;IAChGlE,EAAA,CAAAC,cAAA,cAAkF;IAChFD,EAAA,CAAA8B,UAAA,IAAAqC,8CAAA,oBAIC;IAmCPnE,EAFI,CAAAY,YAAA,EAAM,EACF,EACF;;;;IAjDsDZ,EAAA,CAAAqB,SAAA,EAA+B;IAA/BrB,EAAA,CAAAwB,UAAA,aAAAlB,MAAA,CAAA8D,YAAA,OAA+B;IAG/BpE,EAAA,CAAAqB,SAAA,GAAqC;IAArCrB,EAAA,CAAAwB,UAAA,aAAAlB,MAAA,CAAA8D,YAAA,IAAA9D,MAAA,CAAA+D,QAAA,CAAqC;IAMnErE,EAAA,CAAAqB,SAAA,GAAuD;IAAvDrB,EAAA,CAAAsE,WAAA,8BAAAhE,MAAA,CAAAiE,WAAA,SAAuD;IAE5DvE,EAAA,CAAAqB,SAAA,EAAmB;IAAArB,EAAnB,CAAAwB,UAAA,YAAAlB,MAAA,CAAAkE,cAAA,CAAmB,iBAAAlE,MAAA,CAAAmE,aAAA,CAAsB;;;;;IAyClEzE,EAAA,CAAAC,cAAA,cAAyF;IACvFD,EAAA,CAAAU,SAAA,mBAA8D;IAC9DV,EAAA,CAAAC,cAAA,aAAwB;IAAAD,EAAA,CAAAW,MAAA,qBAAc;IAAAX,EAAA,CAAAY,YAAA,EAAK;IAC3CZ,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAW,MAAA,4CAAqC;IAChEX,EADgE,CAAAY,YAAA,EAAI,EAC9D;;;ADpGR,OAAM,MAAO8D,wBAAwB;EA0BnCC,YAAoBC,MAAc;IAAd,KAAAA,MAAM,GAANA,MAAM;IAzB1B,KAAAJ,cAAc,GAAoB,EAAE;IACpC,KAAAK,SAAS,GAAG,IAAI;IAChB,KAAAxC,KAAK,GAAkB,IAAI;IACnB,KAAAyC,YAAY,GAAiB,IAAIjF,YAAY,EAAE;IAEvD;IACA,KAAAuE,YAAY,GAAG,CAAC;IAChB,KAAAG,WAAW,GAAG,CAAC;IACf,KAAAQ,SAAS,GAAG,GAAG,CAAC,CAAC;IACjB,KAAAC,YAAY,GAAG,CAAC,CAAC,CAAC;IAClB,KAAAX,QAAQ,GAAG,CAAC;IAIZ,KAAAY,cAAc,GAAG,IAAI,CAAC,CAAC;IACvB,KAAAC,aAAa,GAAG,IAAI;IACpB,KAAAC,QAAQ,GAAG,KAAK;IAEhB;IACA,KAAA5D,cAAc,GAAG,KAAK;IACtB,KAAAM,mBAAmB,GAAG,KAAK;IAC3B,KAAAF,YAAY,GAAG,GAAG;IAClB,KAAAC,eAAe,GAAG,EAAE;IACpB,KAAAwD,QAAQ,GAAG,KAAK;EAEqB;EAErCC,QAAQA,CAAA;IACN,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAACC,wBAAwB,EAAE;IAC/B,IAAI,CAACC,mBAAmB,EAAE;IAC1B,IAAI,CAACC,iBAAiB,EAAE;EAC1B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACZ,YAAY,CAACa,WAAW,EAAE;IAC/B,IAAI,CAACC,aAAa,EAAE;EACtB;EAEcN,kBAAkBA,CAAA;IAAA,IAAAO,KAAA;IAAA,OAAAC,iBAAA;MAC9B,IAAI;QACFD,KAAI,CAAChB,SAAS,GAAG,IAAI;QACrBgB,KAAI,CAACxD,KAAK,GAAG,IAAI;QAEjB;QACAwD,KAAI,CAACrB,cAAc,GAAG,CACpB;UACEuB,EAAE,EAAE,GAAG;UACP3C,QAAQ,EAAE,kBAAkB;UAC5BH,QAAQ,EAAE,YAAY;UACtBF,MAAM,EAAE,6FAA6F;UACrGS,UAAU,EAAE,oCAAoC;UAChDC,WAAW,EAAE,KAAK;UAClBP,YAAY,EAAE,IAAI;UAClBI,aAAa,EAAE,KAAK;UACpBC,QAAQ,EAAE;SACX,EACD;UACEwC,EAAE,EAAE,GAAG;UACP3C,QAAQ,EAAE,gBAAgB;UAC1BH,QAAQ,EAAE,WAAW;UACrBF,MAAM,EAAE,6FAA6F;UACrGS,UAAU,EAAE,kCAAkC;UAC9CC,WAAW,EAAE,KAAK;UAClBP,YAAY,EAAE,IAAI;UAClBI,aAAa,EAAE,KAAK;UACpBC,QAAQ,EAAE;SACX,EACD;UACEwC,EAAE,EAAE,GAAG;UACP3C,QAAQ,EAAE,aAAa;UACvBH,QAAQ,EAAE,cAAc;UACxBF,MAAM,EAAE,6FAA6F;UACrGS,UAAU,EAAE,kCAAkC;UAC9CC,WAAW,EAAE,KAAK;UAClBP,YAAY,EAAE,KAAK;UACnBI,aAAa,EAAE,IAAI;UACnBC,QAAQ,EAAE;SACX,EACD;UACEwC,EAAE,EAAE,GAAG;UACP3C,QAAQ,EAAE,cAAc;UACxBH,QAAQ,EAAE,aAAa;UACvBF,MAAM,EAAE,0FAA0F;UAClGS,UAAU,EAAE,kCAAkC;UAC9CC,WAAW,EAAE,KAAK;UAClBP,YAAY,EAAE,IAAI;UAClBI,aAAa,EAAE,KAAK;UACpBC,QAAQ,EAAE;SACX,EACD;UACEwC,EAAE,EAAE,GAAG;UACP3C,QAAQ,EAAE,mBAAmB;UAC7BH,QAAQ,EAAE,WAAW;UACrBF,MAAM,EAAE,6FAA6F;UACrGS,UAAU,EAAE,iCAAiC;UAC7CC,WAAW,EAAE,KAAK;UAClBP,YAAY,EAAE,KAAK;UACnBI,aAAa,EAAE,KAAK;UACpBC,QAAQ,EAAE;SACX,EACD;UACEwC,EAAE,EAAE,GAAG;UACP3C,QAAQ,EAAE,YAAY;UACtBH,QAAQ,EAAE,oBAAoB;UAC9BF,MAAM,EAAE,6FAA6F;UACrGS,UAAU,EAAE,iCAAiC;UAC7CC,WAAW,EAAE,KAAK;UAClBP,YAAY,EAAE,IAAI;UAClBI,aAAa,EAAE,KAAK;UACpBC,QAAQ,EAAE;SACX,CACF;QAEDsC,KAAI,CAAChB,SAAS,GAAG,KAAK;QACtBgB,KAAI,CAACG,uBAAuB,EAAE;OAC/B,CAAC,OAAO3D,KAAK,EAAE;QACd4D,OAAO,CAAC5D,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtDwD,KAAI,CAACxD,KAAK,GAAG,gCAAgC;QAC7CwD,KAAI,CAAChB,SAAS,GAAG,KAAK;;IACvB;EACH;EAEAnC,WAAWA,CAACwD,IAAmB;IAC7B,IAAI,CAACtB,MAAM,CAACuB,QAAQ,CAAC,CAAC,UAAU,EAAED,IAAI,CAAC9C,QAAQ,CAAC,CAAC;EACnD;EAEAN,YAAYA,CAACoD,IAAmB,EAAEE,KAAY;IAC5CA,KAAK,CAACC,eAAe,EAAE;IACvBH,IAAI,CAACzC,WAAW,GAAG,CAACyC,IAAI,CAACzC,WAAW;IAEpC,IAAIyC,IAAI,CAACzC,WAAW,EAAE;MACpByC,IAAI,CAAC5C,aAAa,EAAE;KACrB,MAAM;MACL4C,IAAI,CAAC5C,aAAa,EAAE;;EAExB;EAEAD,mBAAmBA,CAACiD,KAAa;IAC/B,IAAIA,KAAK,IAAI,OAAO,EAAE;MACpB,OAAO,CAACA,KAAK,GAAG,OAAO,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;KAC1C,MAAM,IAAID,KAAK,IAAI,IAAI,EAAE;MACxB,OAAO,CAACA,KAAK,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAExC,OAAOD,KAAK,CAACE,QAAQ,EAAE;EACzB;EAEApE,OAAOA,CAAA;IACL,IAAI,CAACkD,kBAAkB,EAAE;EAC3B;EAEAb,aAAaA,CAACgC,KAAa,EAAEP,IAAmB;IAC9C,OAAOA,IAAI,CAACH,EAAE;EAChB;EAEA;EACQW,cAAcA,CAAA;IACpB,IAAI,CAAC,IAAI,CAACxB,aAAa,IAAI,IAAI,CAACC,QAAQ,EAAE;IAE1C,IAAI,CAACS,aAAa,EAAE;IACpB,IAAI,CAACe,iBAAiB,GAAGC,WAAW,CAAC,MAAK;MACxC,IAAI,CAAC,IAAI,CAACzB,QAAQ,IAAI,IAAI,CAACX,cAAc,CAACqC,MAAM,GAAG,IAAI,CAAC7B,YAAY,EAAE;QACpE,IAAI,CAAC8B,aAAa,EAAE;;IAExB,CAAC,EAAE,IAAI,CAAC7B,cAAc,CAAC;EACzB;EAEQW,aAAaA,CAAA;IACnB,IAAI,IAAI,CAACe,iBAAiB,EAAE;MAC1BI,aAAa,CAAC,IAAI,CAACJ,iBAAiB,CAAC;MACrC,IAAI,CAACA,iBAAiB,GAAG,IAAI;;EAEjC;EAEQG,aAAaA,CAAA;IACnB,IAAI,IAAI,CAAC1C,YAAY,IAAI,IAAI,CAACC,QAAQ,EAAE;MACtC,IAAI,CAACD,YAAY,GAAG,CAAC;KACtB,MAAM;MACL,IAAI,CAACA,YAAY,EAAE;;IAErB,IAAI,CAAC4C,iBAAiB,EAAE;EAC1B;EAEAhD,cAAcA,CAAA;IACZ,IAAI,CAACmB,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACS,aAAa,EAAE;EACtB;EAEA1B,eAAeA,CAAA;IACb,IAAI,CAACiB,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACuB,cAAc,EAAE;EACvB;EAEA;EACQnB,wBAAwBA,CAAA;IAC9B,MAAM0B,KAAK,GAAGC,MAAM,CAACC,UAAU;IAC/B,IAAIF,KAAK,IAAI,GAAG,EAAE;MAChB,IAAI,CAAClC,SAAS,GAAG,GAAG;MACpB,IAAI,CAACC,YAAY,GAAG,CAAC;KACtB,MAAM,IAAIiC,KAAK,IAAI,GAAG,EAAE;MACvB,IAAI,CAAClC,SAAS,GAAG,GAAG;MACpB,IAAI,CAACC,YAAY,GAAG,CAAC;KACtB,MAAM,IAAIiC,KAAK,IAAI,IAAI,EAAE;MACxB,IAAI,CAAClC,SAAS,GAAG,GAAG;MACpB,IAAI,CAACC,YAAY,GAAG,CAAC;KACtB,MAAM;MACL,IAAI,CAACD,SAAS,GAAG,GAAG;MACpB,IAAI,CAACC,YAAY,GAAG,CAAC;;IAEvB,IAAI,CAACoC,kBAAkB,EAAE;IACzB,IAAI,CAACJ,iBAAiB,EAAE;EAC1B;EAEQxB,mBAAmBA,CAAA;IACzB0B,MAAM,CAACG,gBAAgB,CAAC,QAAQ,EAAE,MAAK;MACrC,IAAI,CAAC9B,wBAAwB,EAAE;IACjC,CAAC,CAAC;EACJ;EAEA;EACA6B,kBAAkBA,CAAA;IAChB,IAAI,CAAC/C,QAAQ,GAAGiD,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC/C,cAAc,CAACqC,MAAM,GAAG,IAAI,CAAC7B,YAAY,CAAC;EAC7E;EAEApB,SAASA,CAAA;IACP,IAAI,IAAI,CAACQ,YAAY,GAAG,CAAC,EAAE;MACzB,IAAI,CAACA,YAAY,EAAE;MACnB,IAAI,CAAC4C,iBAAiB,EAAE;MACxB,IAAI,CAACQ,gCAAgC,EAAE;;EAE3C;EAEA1D,SAASA,CAAA;IACP,IAAI,IAAI,CAACM,YAAY,GAAG,IAAI,CAACC,QAAQ,EAAE;MACrC,IAAI,CAACD,YAAY,EAAE;MACnB,IAAI,CAAC4C,iBAAiB,EAAE;MACxB,IAAI,CAACQ,gCAAgC,EAAE;;EAE3C;EAEQR,iBAAiBA,CAAA;IACvB,IAAI,CAACzC,WAAW,GAAG,CAAC,IAAI,CAACH,YAAY,GAAG,IAAI,CAACW,SAAS;EACxD;EAEQyC,gCAAgCA,CAAA;IACtC,IAAI,CAAC5B,aAAa,EAAE;IACpB6B,UAAU,CAAC,MAAK;MACd,IAAI,CAACf,cAAc,EAAE;IACvB,CAAC,EAAE,IAAI,CAAC;EACV;EAEA;EACQV,uBAAuBA,CAAA;IAC7ByB,UAAU,CAAC,MAAK;MACd,IAAI,CAACL,kBAAkB,EAAE;MACzB,IAAI,CAAChD,YAAY,GAAG,CAAC;MACrB,IAAI,CAACG,WAAW,GAAG,CAAC;MACpB,IAAI,CAACmC,cAAc,EAAE;IACvB,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;EACAjG,iBAAiBA,CAAA;IACf,IAAI,CAACc,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAC1C,IAAI,IAAI,CAACA,cAAc,EAAE;MACvB,IAAI,CAACI,YAAY,EAAE;KACpB,MAAM;MACL,IAAI,CAACA,YAAY,EAAE;;EAEvB;EAEAT,qBAAqBA,CAAA;IACnB,IAAI,CAACW,mBAAmB,GAAG,CAAC,IAAI,CAACA,mBAAmB;EACtD;EAEAf,YAAYA,CAAA;IACVmF,OAAO,CAACyB,GAAG,CAAC,8CAA8C,CAAC;EAC7D;EAEA1G,YAAYA,CAAA;IACV,IAAI2G,SAAS,CAACC,KAAK,EAAE;MACnBD,SAAS,CAACC,KAAK,CAAC;QACdC,KAAK,EAAE,mBAAmB;QAC1BC,IAAI,EAAE,oCAAoC;QAC1CC,GAAG,EAAEb,MAAM,CAACc,QAAQ,CAACC;OACtB,CAAC;KACH,MAAM;MACLN,SAAS,CAACO,SAAS,CAACC,SAAS,CAACjB,MAAM,CAACc,QAAQ,CAACC,IAAI,CAAC;MACnDhC,OAAO,CAACyB,GAAG,CAAC,0BAA0B,CAAC;;EAE3C;EAEAtG,eAAeA,CAAA;IACb6E,OAAO,CAACyB,GAAG,CAAC,0CAA0C,CAAC;EACzD;EAEAhG,WAAWA,CAAC4E,KAAa;IACvB,IAAIA,KAAK,IAAI,OAAO,EAAE;MACpB,OAAO,CAACA,KAAK,GAAG,OAAO,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;KAC1C,MAAM,IAAID,KAAK,IAAI,IAAI,EAAE;MACxB,OAAO,CAACA,KAAK,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAExC,OAAOD,KAAK,CAACE,QAAQ,EAAE;EACzB;EAEQf,iBAAiBA,CAAA;IACvB,IAAI,CAACL,QAAQ,GAAG8B,MAAM,CAACC,UAAU,IAAI,GAAG;EAC1C;;;uBApTWzC,wBAAwB,EAAA1E,EAAA,CAAAoI,iBAAA,CAAAC,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAxB5D,wBAAwB;MAAA6D,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAzI,EAAA,CAAA0I,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC1BrChJ,EAAA,CAAAC,cAAA,aAAuC;UAErCD,EAAA,CAAA8B,UAAA,IAAAoH,uCAAA,kBAAoD;UAiChDlJ,EAFJ,CAAAC,cAAA,aAA4B,aACE,YACA;UACxBD,EAAA,CAAAU,SAAA,kBAAsD;UACtDV,EAAA,CAAAW,MAAA,0BACF;UAAAX,EAAA,CAAAY,YAAA,EAAK;UACLZ,EAAA,CAAAC,cAAA,WAA4B;UAAAD,EAAA,CAAAW,MAAA,wCAAiC;UAEjEX,EAFiE,CAAAY,YAAA,EAAI,EAC7D,EACF;UAiFNZ,EA9EA,CAAA8B,UAAA,IAAAqH,uCAAA,iBAAiD,KAAAC,wCAAA,iBAcQ,KAAAC,wCAAA,iBAUqC,KAAAC,wCAAA,kBAsDL;UAK3FtJ,EAAA,CAAAY,YAAA,EAAM;;;UA7HgCZ,EAAA,CAAAqB,SAAA,EAAc;UAAdrB,EAAA,CAAAwB,UAAA,SAAAyH,GAAA,CAAA7D,QAAA,CAAc;UA0C5CpF,EAAA,CAAAqB,SAAA,GAAe;UAAfrB,EAAA,CAAAwB,UAAA,SAAAyH,GAAA,CAAApE,SAAA,CAAe;UAcf7E,EAAA,CAAAqB,SAAA,EAAyB;UAAzBrB,EAAA,CAAAwB,UAAA,SAAAyH,GAAA,CAAA5G,KAAA,KAAA4G,GAAA,CAAApE,SAAA,CAAyB;UAUzB7E,EAAA,CAAAqB,SAAA,EAAuD;UAAvDrB,EAAA,CAAAwB,UAAA,UAAAyH,GAAA,CAAApE,SAAA,KAAAoE,GAAA,CAAA5G,KAAA,IAAA4G,GAAA,CAAAzE,cAAA,CAAAqC,MAAA,KAAuD;UAsDvD7G,EAAA,CAAAqB,SAAA,EAAyD;UAAzDrB,EAAA,CAAAwB,UAAA,UAAAyH,GAAA,CAAApE,SAAA,KAAAoE,GAAA,CAAA5G,KAAA,IAAA4G,GAAA,CAAAzE,cAAA,CAAAqC,MAAA,OAAyD;;;qBDpGrDjH,YAAY,EAAA2J,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAE3J,WAAW,EAAA4J,EAAA,CAAAC,OAAA,EAAE5J,cAAc;MAAA6J,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}