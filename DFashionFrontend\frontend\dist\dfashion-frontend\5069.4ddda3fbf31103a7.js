"use strict";(self.webpackChunkdfashion_frontend=self.webpackChunkdfashion_frontend||[]).push([[5069],{5069:(zt,O,l)=>{l.r(O),l.d(O,{HomeComponent:()=>Rt});var d=l(177),g=l(2276),F=l(1341),h=l(9417),n=l(4438),m=l(2168),b=l(5494),x=l(7935);const j=()=>[1,2,3];function I(i,a){1&i&&(n.j41(0,"div",8)(1,"div",9),n.nrm(2,"div",10),n.j41(3,"div",11),n.nrm(4,"div",12)(5,"div",13),n.k0s()(),n.nrm(6,"div",14)(7,"div",15),n.k0s())}function T(i,a){1&i&&(n.j41(0,"div",6),n.DNE(1,I,8,0,"div",7),n.k0s()),2&i&&(n.R7$(),n.Y8G("ngForOf",n.lJ4(1,j)))}function R(i,a){1&i&&(n.j41(0,"div",19)(1,"p"),n.EFF(2,"No posts available. Loading posts..."),n.k0s()())}function z(i,a){if(1&i&&(n.j41(0,"span",52),n.EFF(1),n.k0s()),2&i){const t=n.XpG().$implicit;n.R7$(),n.JRh(t.location)}}function $(i,a){if(1&i&&(n.j41(0,"div",53),n.nrm(1,"img",54),n.k0s()),2&i){const t=n.XpG().$implicit;n.R7$(),n.Y8G("src",t.mediaUrl,n.B4B)("alt",t.content)}}function B(i,a){1&i&&(n.j41(0,"div",61),n.nrm(1,"i",62),n.j41(2,"span"),n.EFF(3,"Reel"),n.k0s()())}function A(i,a){if(1&i){const t=n.RV6();n.j41(0,"div",55)(1,"video",56),n.bIt("click",function(e){n.eBV(t);const r=n.XpG(3);return n.Njj(r.toggleVideoPlay(e))}),n.k0s(),n.j41(2,"div",57)(3,"button",58),n.bIt("click",function(e){n.eBV(t);const r=n.XpG(3);return n.Njj(r.toggleVideoPlay(e))}),n.nrm(4,"i",59),n.k0s()(),n.DNE(5,B,4,0,"div",60),n.k0s()}if(2&i){const t=n.XpG().$implicit;n.R7$(),n.Y8G("src",t.mediaUrl,n.B4B)("muted",!0)("loop",!0),n.R7$(4),n.Y8G("ngIf",t.isReel)}}function G(i,a){if(1&i){const t=n.RV6();n.j41(0,"button",65),n.bIt("click",function(){const e=n.eBV(t).$implicit,r=n.XpG(4);return n.Njj(r.showProductDetails(e))}),n.nrm(1,"i",66),n.k0s()}}function N(i,a){if(1&i&&(n.j41(0,"div",63),n.DNE(1,G,2,0,"button",64),n.k0s()),2&i){const t=n.XpG().$implicit;n.R7$(),n.Y8G("ngForOf",t.products)}}function E(i,a){if(1&i&&(n.j41(0,"div",67)(1,"span",68),n.EFF(2),n.k0s()()),2&i){const t=n.XpG().$implicit,o=n.XpG(2);n.R7$(2),n.JRh(o.formatLikesCount(t.likes))}}function L(i,a){if(1&i&&(n.j41(0,"span",71),n.EFF(1),n.k0s()),2&i){const t=a.$implicit;n.R7$(),n.SpI("#",t,"")}}function Y(i,a){if(1&i&&(n.j41(0,"div",69),n.DNE(1,L,2,1,"span",70),n.k0s()),2&i){const t=n.XpG().$implicit;n.R7$(),n.Y8G("ngForOf",t.hashtags)}}function V(i,a){if(1&i){const t=n.RV6();n.j41(0,"div",72)(1,"button",73),n.bIt("click",function(){n.eBV(t);const e=n.XpG().$implicit,r=n.XpG(2);return n.Njj(r.toggleComments(e))}),n.EFF(2),n.k0s()()}if(2&i){const t=n.XpG().$implicit;n.R7$(2),n.SpI(" View all ",t.comments," comments ")}}function X(i,a){if(1&i){const t=n.RV6();n.j41(0,"div",77),n.nrm(1,"img",78),n.j41(2,"div",79)(3,"span",80),n.EFF(4),n.k0s(),n.j41(5,"span",81),n.EFF(6),n.k0s()(),n.j41(7,"div",82),n.nrm(8,"img",78),n.j41(9,"div",79)(10,"span",80),n.EFF(11),n.k0s(),n.j41(12,"span",81),n.EFF(13),n.k0s()()(),n.j41(14,"div",83)(15,"button",84),n.bIt("click",function(){const e=n.eBV(t).$implicit,r=n.XpG(4);return n.Njj(r.viewProduct(e))}),n.EFF(16,"Shop"),n.k0s(),n.j41(17,"button",85),n.bIt("click",function(){const e=n.eBV(t).$implicit,r=n.XpG(4);return n.Njj(r.addToCart(e))}),n.nrm(18,"i",86),n.k0s(),n.j41(19,"button",87),n.bIt("click",function(){const e=n.eBV(t).$implicit,r=n.XpG(4);return n.Njj(r.addToWishlist(e))}),n.nrm(20,"i",88),n.k0s(),n.j41(21,"button",89),n.bIt("click",function(){const e=n.eBV(t).$implicit,r=n.XpG(4);return n.Njj(r.buyNow(e))}),n.nrm(22,"i",90),n.k0s()()()}if(2&i){const t=a.$implicit,o=n.XpG(4);n.R7$(),n.Y8G("src",t.image,n.B4B)("alt",t.name),n.R7$(3),n.JRh(t.name),n.R7$(2),n.JRh(o.formatPrice(t.price)),n.R7$(2),n.Y8G("src",t.image,n.B4B)("alt",t.name),n.R7$(3),n.JRh(t.name),n.R7$(2),n.JRh(o.formatPrice(t.price))}}function D(i,a){if(1&i&&(n.j41(0,"div",74)(1,"div",75),n.DNE(2,X,23,8,"div",76),n.k0s()()),2&i){const t=n.XpG().$implicit;n.R7$(2),n.Y8G("ngForOf",t.products.slice(0,3))}}function U(i,a){if(1&i){const t=n.RV6();n.j41(0,"article",20)(1,"header",21)(2,"div",22)(3,"div",23),n.nrm(4,"img",24),n.k0s(),n.j41(5,"div",25)(6,"h3",26),n.EFF(7),n.k0s(),n.DNE(8,z,2,1,"span",27),n.k0s()(),n.j41(9,"button",28),n.nrm(10,"i",29),n.k0s()(),n.j41(11,"div",30),n.DNE(12,$,2,2,"div",31)(13,A,6,4,"div",32)(14,N,2,1,"div",33),n.k0s(),n.j41(15,"div",34)(16,"div",35)(17,"button",36),n.bIt("click",function(){const e=n.eBV(t).$implicit,r=n.XpG(2);return n.Njj(r.toggleLike(e))}),n.nrm(18,"i"),n.k0s(),n.j41(19,"button",37),n.bIt("click",function(){const e=n.eBV(t).$implicit,r=n.XpG(2);return n.Njj(r.focusCommentInput(e))}),n.nrm(20,"i",38),n.k0s(),n.j41(21,"button",39),n.bIt("click",function(){const e=n.eBV(t).$implicit,r=n.XpG(2);return n.Njj(r.sharePost(e))}),n.nrm(22,"i",40),n.k0s()(),n.j41(23,"button",41),n.bIt("click",function(){const e=n.eBV(t).$implicit,r=n.XpG(2);return n.Njj(r.toggleSave(e))}),n.nrm(24,"i"),n.k0s()(),n.DNE(25,E,3,1,"div",42),n.j41(26,"div",43)(27,"span",26),n.EFF(28),n.k0s(),n.j41(29,"span",44),n.EFF(30),n.k0s(),n.DNE(31,Y,2,1,"div",45),n.k0s(),n.DNE(32,V,3,1,"div",46),n.j41(33,"div",47),n.EFF(34),n.k0s(),n.j41(35,"div",48)(36,"input",49,0),n.mxI("ngModelChange",function(e){n.eBV(t);const r=n.XpG(2);return n.DH7(r.newComment,e)||(r.newComment=e),n.Njj(e)}),n.bIt("keyup.enter",function(){const e=n.eBV(t).$implicit,r=n.XpG(2);return n.Njj(r.addComment(e))}),n.k0s(),n.j41(38,"button",50),n.bIt("click",function(){const e=n.eBV(t).$implicit,r=n.XpG(2);return n.Njj(r.addComment(e))}),n.EFF(39," Post "),n.k0s()(),n.DNE(40,D,3,1,"div",51),n.k0s()}if(2&i){const t=a.$implicit,o=n.XpG(2);n.R7$(4),n.Y8G("src",(null==t.user?null:t.user.avatar)||"assets/images/default-avatar.png",n.B4B)("alt",(null==t.user?null:t.user.fullName)||"User"),n.R7$(3),n.JRh((null==t.user?null:t.user.username)||"Unknown User"),n.R7$(),n.Y8G("ngIf",t.location),n.R7$(4),n.Y8G("ngIf","image"===t.mediaType),n.R7$(),n.Y8G("ngIf","video"===t.mediaType),n.R7$(),n.Y8G("ngIf",t.products&&t.products.length>0),n.R7$(3),n.AVh("liked",t.isLiked),n.R7$(),n.HbH(t.isLiked?"fas fa-heart":"far fa-heart"),n.R7$(5),n.AVh("saved",t.isSaved),n.R7$(),n.HbH(t.isSaved?"fas fa-bookmark":"far fa-bookmark"),n.R7$(),n.Y8G("ngIf",t.likes>0),n.R7$(3),n.JRh((null==t.user?null:t.user.username)||"Unknown User"),n.R7$(2),n.JRh(t.content),n.R7$(),n.Y8G("ngIf",t.hashtags&&t.hashtags.length>0),n.R7$(),n.Y8G("ngIf",t.comments>0),n.R7$(2),n.SpI(" ",o.getTimeAgo(t.createdAt)," "),n.R7$(2),n.R50("ngModel",o.newComment),n.R7$(2),n.Y8G("disabled",!o.newComment||!o.newComment.trim()),n.R7$(2),n.Y8G("ngIf",t.products&&t.products.length>0)}}function W(i,a){if(1&i&&(n.j41(0,"div",16),n.DNE(1,R,3,0,"div",17)(2,U,41,24,"article",18),n.k0s()),2&i){const t=n.XpG();n.R7$(),n.Y8G("ngIf",0===t.posts.length),n.R7$(),n.Y8G("ngForOf",t.posts)("ngForTrackBy",t.trackByPostId)}}function J(i,a){if(1&i){const t=n.RV6();n.j41(0,"div",91)(1,"button",92),n.bIt("click",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.loadMorePosts())}),n.EFF(2," Load More Posts "),n.k0s()()}}function H(i,a){1&i&&(n.j41(0,"div",93)(1,"div",94),n.nrm(2,"i",95),n.j41(3,"h3"),n.EFF(4,"Welcome to DFashion"),n.k0s(),n.j41(5,"p"),n.EFF(6,"Follow fashion influencers to see their latest posts and discover trending styles!"),n.k0s()()())}let K=(()=>{class i{constructor(t,o,e){this.router=t,this.cartService=o,this.wishlistService=e,this.posts=[],this.loading=!0,this.hasMore=!0,this.currentPage=1,this.newComment=""}ngOnInit(){this.loadPosts()}loadPosts(){this.loading=!0,setTimeout(()=>{this.posts=this.getFallbackPosts(),this.loading=!1},1e3)}getFallbackPosts(){return[{_id:"post-1",user:{_id:"user-1",username:"ai_fashionista_maya",fullName:"Maya Chen",avatar:"assets/images/default-avatar.svg"},content:"Sustainable fashion is the future! \u{1f331}\u2728 This eco-friendly dress is made from recycled materials and looks absolutely stunning. #SustainableFashion #EcoFriendly #OOTD",mediaUrl:"https://images.unsplash.com/photo-1445205170230-053b83016050?w=400&h=600&fit=crop",mediaType:"image",location:"Mumbai, India",likes:1247,comments:89,shares:34,isLiked:!1,isSaved:!1,isReel:!1,hashtags:["SustainableFashion","EcoFriendly","OOTD"],createdAt:new Date(Date.now()-72e5).toISOString(),products:[{_id:"prod-1",name:"Eco-Friendly Summer Dress",price:2499,image:"https://images.unsplash.com/photo-1445205170230-053b83016050?w=400&h=600&fit=crop"}]},{_id:"post-3",user:{_id:"user-3",username:"ai_trendsetter_zara",fullName:"Zara Patel",avatar:"assets/images/default-avatar.svg"},content:"Ethnic fusion at its finest! Traditional meets modern \u2728 This kurti is perfect for any occasion.",mediaUrl:"https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400&h=600&fit=crop",mediaType:"image",location:"Bangalore, India",likes:2156,comments:134,shares:67,isLiked:!1,isSaved:!0,isReel:!1,hashtags:["EthnicWear","Fusion","Traditional"],createdAt:new Date(Date.now()-288e5).toISOString(),products:[{_id:"prod-3",name:"Designer Ethnic Kurti",price:1899,image:"https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400&h=600&fit=crop"}]}]}loadMorePosts(){this.currentPage++,this.loadPosts()}trackByPostId(t,o){return o._id}toggleLike(t){t.isLiked=!t.isLiked,t.likes+=t.isLiked?1:-1}toggleSave(t){t.isSaved=!t.isSaved}toggleComments(t){console.log("Toggle comments for post:",t._id)}sharePost(t){console.log("Share post:",t._id)}addComment(t){this.newComment.trim()&&(t.comments+=1,console.log("Add comment:",this.newComment,"to post:",t._id),this.newComment="")}focusCommentInput(t){console.log("Focus comment input for post:",t._id)}toggleVideoPlay(t){const o=t.target;o.paused?o.play():o.pause()}showProductDetails(t){console.log("Show product details:",t)}viewProduct(t){this.router.navigate(["/product",t._id])}formatLikesCount(t){return 1===t?"1 like":t<1e3?`${t} likes`:t<1e6?`${(t/1e3).toFixed(1)}K likes`:`${(t/1e6).toFixed(1)}M likes`}formatPrice(t){return new Intl.NumberFormat("en-IN",{style:"currency",currency:"INR",minimumFractionDigits:0}).format(t)}getTimeAgo(t){const o=new Date,e=new Date(t),r=Math.floor((o.getTime()-e.getTime())/6e4);if(r<1)return"now";if(r<60)return`${r}m`;const c=Math.floor(r/60);if(c<24)return`${c}h`;const p=Math.floor(c/24);return p<7?`${p}d`:`${Math.floor(p/7)}w`}addToCart(t){console.log("Adding to cart:",t),this.cartService.addToCart(t._id,1,void 0,void 0).subscribe({next:o=>{o.success?alert("Product added to cart!"):alert("Failed to add product to cart")},error:o=>{console.error("Error adding to cart:",o),alert("Error adding product to cart")}})}addToWishlist(t){console.log("Adding to wishlist:",t),this.wishlistService.addToWishlist(t._id).subscribe({next:o=>{o.success?alert("Product added to wishlist!"):alert("Failed to add product to wishlist")},error:o=>{console.error("Error adding to wishlist:",o),alert("Error adding product to wishlist")}})}buyNow(t){console.log("Buying product:",t),this.router.navigate(["/checkout"],{queryParams:{productId:t._id,source:"feed"}})}static{this.\u0275fac=function(o){return new(o||i)(n.rXU(m.Ix),n.rXU(b.CartService),n.rXU(x.WishlistService))}}static{this.\u0275cmp=n.VBU({type:i,selectors:[["app-feed"]],standalone:!0,features:[n.aNF],decls:5,vars:4,consts:[["commentInput",""],[1,"instagram-feed"],["class","loading-container",4,"ngIf"],["class","feed-posts",4,"ngIf"],["class","load-more",4,"ngIf"],["class","empty-feed",4,"ngIf"],[1,"loading-container"],["class","post-skeleton",4,"ngFor","ngForOf"],[1,"post-skeleton"],[1,"skeleton-header"],[1,"skeleton-avatar"],[1,"skeleton-user-info"],[1,"skeleton-username"],[1,"skeleton-time"],[1,"skeleton-image"],[1,"skeleton-actions"],[1,"feed-posts"],["class","no-posts-message",4,"ngIf"],["class","instagram-post",4,"ngFor","ngForOf","ngForTrackBy"],[1,"no-posts-message"],[1,"instagram-post"],[1,"post-header"],[1,"user-info"],[1,"user-avatar-container"],[1,"user-avatar",3,"src","alt"],[1,"user-details"],[1,"username"],["class","post-location",4,"ngIf"],[1,"post-options"],[1,"fas","fa-ellipsis-h"],[1,"post-media-container"],["class","post-image-container",4,"ngIf"],["class","post-video-container",4,"ngIf"],["class","product-tags-overlay",4,"ngIf"],[1,"post-actions"],[1,"primary-actions"],[1,"action-btn","like-btn",3,"click"],[1,"action-btn","comment-btn",3,"click"],[1,"far","fa-comment"],[1,"action-btn","share-btn",3,"click"],[1,"far","fa-paper-plane"],[1,"action-btn","save-btn",3,"click"],["class","likes-section",4,"ngIf"],[1,"post-caption"],[1,"caption-text"],["class","hashtags",4,"ngIf"],["class","comments-preview",4,"ngIf"],[1,"post-time"],[1,"add-comment-section"],["type","text","placeholder","Add a comment...",1,"comment-input",3,"ngModelChange","keyup.enter","ngModel"],[1,"post-comment-btn",3,"click","disabled"],["class","ecommerce-section",4,"ngIf"],[1,"post-location"],[1,"post-image-container"],[1,"post-image",3,"src","alt"],[1,"post-video-container"],[1,"post-video",3,"click","src","muted","loop"],[1,"video-overlay"],[1,"play-pause-btn",3,"click"],[1,"fas","fa-play"],["class","reel-indicator",4,"ngIf"],[1,"reel-indicator"],[1,"fas","fa-video"],[1,"product-tags-overlay"],["class","product-tag-btn",3,"click",4,"ngFor","ngForOf"],[1,"product-tag-btn",3,"click"],[1,"fas","fa-shopping-bag"],[1,"likes-section"],[1,"likes-count"],[1,"hashtags"],["class","hashtag",4,"ngFor","ngForOf"],[1,"hashtag"],[1,"comments-preview"],[1,"view-comments-btn",3,"click"],[1,"ecommerce-section"],[1,"product-showcase"],["class","featured-product",4,"ngFor","ngForOf"],[1,"featured-product"],[1,"product-thumbnail",3,"src","alt"],[1,"product-details"],[1,"product-name"],[1,"product-price"],[1,"product-header"],[1,"product-actions"],[1,"shop-btn",3,"click"],[1,"cart-btn",3,"click"],[1,"fas","fa-shopping-cart"],[1,"wishlist-btn",3,"click"],[1,"fas","fa-heart"],[1,"buy-btn",3,"click"],[1,"fas","fa-bolt"],[1,"load-more"],[1,"load-more-btn",3,"click"],[1,"empty-feed"],[1,"empty-content"],[1,"fas","fa-camera"]],template:function(o,e){1&o&&(n.j41(0,"div",1),n.DNE(1,T,2,2,"div",2)(2,W,3,3,"div",3)(3,J,3,0,"div",4)(4,H,7,0,"div",5),n.k0s()),2&o&&(n.R7$(),n.Y8G("ngIf",e.loading),n.R7$(),n.Y8G("ngIf",!e.loading),n.R7$(),n.Y8G("ngIf",e.hasMore&&!e.loading),n.R7$(),n.Y8G("ngIf",!e.loading&&0===e.posts.length))},dependencies:[d.MD,d.Sq,d.bT,h.YN,h.me,h.BC,h.vS],styles:['.instagram-feed[_ngcontent-%COMP%]{max-width:470px;margin:0 auto;background:#fafafa;min-height:100vh}@media (max-width: 768px){.instagram-feed[_ngcontent-%COMP%]{max-width:100%;background:#fff;padding:0;margin:0}}.loading-container[_ngcontent-%COMP%]{padding:0}.no-posts-message[_ngcontent-%COMP%]{text-align:center;padding:40px 20px;color:#666;background:#fff;border-radius:8px;margin:20px 0}.no-posts-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:16px}.post-skeleton[_ngcontent-%COMP%]{background:#fff;margin-bottom:12px;border:1px solid #dbdbdb;border-radius:8px;overflow:hidden}.skeleton-header[_ngcontent-%COMP%]{display:flex;align-items:center;padding:16px;gap:12px}.skeleton-avatar[_ngcontent-%COMP%]{width:32px;height:32px;border-radius:50%;background:linear-gradient(90deg,#f0f0f0 25%,#e0e0e0,#f0f0f0 75%);background-size:200% 100%;animation:_ngcontent-%COMP%_shimmer 1.5s infinite}.skeleton-user-info[_ngcontent-%COMP%]{flex:1}.skeleton-username[_ngcontent-%COMP%]{width:100px;height:12px;background:linear-gradient(90deg,#f0f0f0 25%,#e0e0e0,#f0f0f0 75%);background-size:200% 100%;animation:_ngcontent-%COMP%_shimmer 1.5s infinite;border-radius:4px;margin-bottom:6px}.skeleton-time[_ngcontent-%COMP%]{width:60px;height:10px;background:linear-gradient(90deg,#f0f0f0 25%,#e0e0e0,#f0f0f0 75%);background-size:200% 100%;animation:_ngcontent-%COMP%_shimmer 1.5s infinite;border-radius:4px}.skeleton-image[_ngcontent-%COMP%]{width:100%;height:400px;background:linear-gradient(90deg,#f0f0f0 25%,#e0e0e0,#f0f0f0 75%);background-size:200% 100%;animation:_ngcontent-%COMP%_shimmer 1.5s infinite}.skeleton-actions[_ngcontent-%COMP%]{height:60px;background:linear-gradient(90deg,#f0f0f0 25%,#e0e0e0,#f0f0f0 75%);background-size:200% 100%;animation:_ngcontent-%COMP%_shimmer 1.5s infinite}@keyframes _ngcontent-%COMP%_shimmer{0%{background-position:-200% 0}to{background-position:200% 0}}.feed-posts[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:12px}.instagram-post[_ngcontent-%COMP%]{background:#fff;border:1px solid #dbdbdb;border-radius:8px;overflow:visible}@media (max-width: 768px){.instagram-post[_ngcontent-%COMP%]{overflow:visible;min-height:auto}}.post-header[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;padding:16px}.user-info[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px}.user-avatar-container[_ngcontent-%COMP%]{position:relative}.user-avatar[_ngcontent-%COMP%]{width:32px;height:32px;border-radius:50%;object-fit:cover}.user-details[_ngcontent-%COMP%]{display:flex;flex-direction:column}.username[_ngcontent-%COMP%]{font-size:14px;font-weight:600;color:#262626;margin:0;line-height:1.2}.post-location[_ngcontent-%COMP%]{font-size:12px;color:#8e8e8e;line-height:1.2}.post-options[_ngcontent-%COMP%]{background:none;border:none;cursor:pointer;padding:8px;color:#262626}.post-options[_ngcontent-%COMP%]:hover{color:#8e8e8e}.post-media-container[_ngcontent-%COMP%]{position:relative;width:100%;max-height:600px;overflow:hidden}.post-image-container[_ngcontent-%COMP%], .post-video-container[_ngcontent-%COMP%]{width:100%;position:relative}.post-image[_ngcontent-%COMP%]{width:100%;height:auto;display:block}.post-video[_ngcontent-%COMP%]{width:100%;height:auto;display:block;max-height:600px;object-fit:cover}.video-overlay[_ngcontent-%COMP%]{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);opacity:0;transition:opacity .3s ease}.post-video-container[_ngcontent-%COMP%]:hover   .video-overlay[_ngcontent-%COMP%]{opacity:1}.play-pause-btn[_ngcontent-%COMP%]{width:60px;height:60px;border-radius:50%;background:#000000b3;border:none;color:#fff;font-size:20px;cursor:pointer;display:flex;align-items:center;justify-content:center}.reel-indicator[_ngcontent-%COMP%]{position:absolute;top:16px;left:16px;background:#000000b3;color:#fff;padding:6px 12px;border-radius:20px;font-size:12px;font-weight:600;display:flex;align-items:center;gap:6px}.product-tags-overlay[_ngcontent-%COMP%]{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%)}.product-tag-btn[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;background:#000000b3;border:2px solid white;color:#fff;cursor:pointer;display:flex;align-items:center;justify-content:center;font-size:16px;transition:all .3s ease}.product-tag-btn[_ngcontent-%COMP%]:hover{transform:scale(1.1);background:#000000e6}.post-actions[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;padding:8px 16px}.primary-actions[_ngcontent-%COMP%]{display:flex;gap:16px}.action-btn[_ngcontent-%COMP%]{background:none;border:none;cursor:pointer;padding:8px;font-size:24px;color:#262626;transition:all .2s ease}.action-btn[_ngcontent-%COMP%]:hover{color:#8e8e8e}.action-btn.liked[_ngcontent-%COMP%]{color:#ed4956;animation:_ngcontent-%COMP%_likeAnimation .3s ease}.action-btn.saved[_ngcontent-%COMP%]{color:#262626}@keyframes _ngcontent-%COMP%_likeAnimation{0%,to{transform:scale(1)}50%{transform:scale(1.2)}}.likes-section[_ngcontent-%COMP%]{padding:0 16px 8px}.likes-count[_ngcontent-%COMP%]{font-size:14px;font-weight:600;color:#262626}.post-caption[_ngcontent-%COMP%]{padding:0 16px 8px;font-size:14px;line-height:1.4}.post-caption[_ngcontent-%COMP%]   .username[_ngcontent-%COMP%]{font-weight:600;color:#262626;margin-right:8px}.post-caption[_ngcontent-%COMP%]   .caption-text[_ngcontent-%COMP%]{color:#262626}.hashtags[_ngcontent-%COMP%]{margin-top:8px}.hashtag[_ngcontent-%COMP%]{color:#00376b;margin-right:8px;cursor:pointer}.hashtag[_ngcontent-%COMP%]:hover{text-decoration:underline}.comments-preview[_ngcontent-%COMP%]{padding:0 16px 8px}.view-comments-btn[_ngcontent-%COMP%]{background:none;border:none;color:#8e8e8e;font-size:14px;cursor:pointer;padding:0}.view-comments-btn[_ngcontent-%COMP%]:hover{color:#262626}.post-time[_ngcontent-%COMP%]{padding:0 16px 8px;font-size:10px;color:#8e8e8e;text-transform:uppercase;letter-spacing:.2px}.add-comment-section[_ngcontent-%COMP%]{display:flex;align-items:center;padding:16px;border-top:1px solid #efefef;gap:12px}.comment-input[_ngcontent-%COMP%]{flex:1;border:none;outline:none;font-size:14px;color:#262626}.comment-input[_ngcontent-%COMP%]::placeholder{color:#8e8e8e}.post-comment-btn[_ngcontent-%COMP%]{background:none;border:none;color:#0095f6;font-size:14px;font-weight:600;cursor:pointer}.post-comment-btn[_ngcontent-%COMP%]:disabled{color:#b2dffc;cursor:not-allowed}.post-comment-btn[_ngcontent-%COMP%]:hover:not(:disabled){color:#00376b}.ecommerce-section[_ngcontent-%COMP%]{border-top:1px solid #efefef;padding:16px;background:#fafafa;margin-top:8px;position:relative;z-index:1;overflow:visible}@media (max-width: 768px){.ecommerce-section[_ngcontent-%COMP%]{padding:12px 16px 16px;background:#fff;border-top:1px solid #efefef;margin-top:0}}.product-showcase[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:12px}@media (max-width: 768px){.product-showcase[_ngcontent-%COMP%]{gap:8px}}@media (max-width: 425px){.product-showcase[_ngcontent-%COMP%]{gap:6px}}@media (max-width: 320px){.product-showcase[_ngcontent-%COMP%]{gap:4px}}.featured-product[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;padding:12px;border:1px solid #efefef;border-radius:8px;transition:all .2s ease;background:#fff;overflow:visible;position:relative}.featured-product[_ngcontent-%COMP%]:hover{background:#f8f9fa;box-shadow:0 2px 8px #0000001a}@media (max-width: 768px){.featured-product[_ngcontent-%COMP%]{padding:10px;gap:10px;border-radius:6px}}@media (max-width: 425px){.featured-product[_ngcontent-%COMP%]{flex-direction:column;align-items:stretch;gap:8px}}.product-header[_ngcontent-%COMP%]{display:none}@media (max-width: 425px){.product-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;width:100%;padding-bottom:5px;border-bottom:1px solid #f0f0f0;margin-bottom:0}}@media (max-width: 375px){.product-header[_ngcontent-%COMP%]{gap:6px;padding-bottom:4px}}@media (max-width: 320px){.product-header[_ngcontent-%COMP%]{gap:5px;padding-bottom:3px}}.product-thumbnail[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:4px;object-fit:cover;flex-shrink:0}@media (max-width: 425px){.product-thumbnail[_ngcontent-%COMP%]{display:none}}.product-header[_ngcontent-%COMP%]   .product-thumbnail[_ngcontent-%COMP%]{width:45px;height:45px;border-radius:6px;display:block}@media (max-width: 375px){.product-header[_ngcontent-%COMP%]   .product-thumbnail[_ngcontent-%COMP%]{width:40px;height:40px}}@media (max-width: 320px){.product-header[_ngcontent-%COMP%]   .product-thumbnail[_ngcontent-%COMP%]{width:35px;height:35px}}.product-details[_ngcontent-%COMP%]{flex:1;display:flex;flex-direction:column;gap:2px;min-width:0}@media (max-width: 425px){.product-details[_ngcontent-%COMP%]{display:none}}.product-header[_ngcontent-%COMP%]   .product-details[_ngcontent-%COMP%]{flex:1;display:flex;flex-direction:column;gap:4px;min-width:0}.product-name[_ngcontent-%COMP%]{font-size:12px;font-weight:600;color:#262626;line-height:1.3;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}@media (max-width: 425px){.product-name[_ngcontent-%COMP%]{font-size:13px}}@media (max-width: 375px){.product-name[_ngcontent-%COMP%]{font-size:12px}}@media (max-width: 320px){.product-name[_ngcontent-%COMP%]{font-size:11px}}.product-price[_ngcontent-%COMP%]{font-size:12px;color:#8e8e8e;font-weight:500}@media (max-width: 425px){.product-price[_ngcontent-%COMP%]{font-size:12px}}@media (max-width: 375px){.product-price[_ngcontent-%COMP%]{font-size:11px}}@media (max-width: 320px){.product-price[_ngcontent-%COMP%]{font-size:10px}}.product-actions[_ngcontent-%COMP%]{display:flex;gap:8px;align-items:center;margin-top:8px;padding:8px 0;overflow:visible;position:relative;z-index:2;min-height:44px}@media (max-width: 768px){.product-actions[_ngcontent-%COMP%]{gap:6px;padding:8px 0 12px;flex-wrap:wrap;justify-content:flex-start}}@media (max-width: 480px){.product-actions[_ngcontent-%COMP%]{gap:4px;padding:6px 0 10px}}@media (max-width: 425px){.product-actions[_ngcontent-%COMP%]{width:100%;justify-content:flex-start;gap:6px;margin-top:5px;padding:5px 0 0;min-height:36px}}@media (max-width: 375px){.product-actions[_ngcontent-%COMP%]{gap:5px;margin-top:4px;padding:4px 0 0;min-height:32px}}@media (max-width: 320px){.product-actions[_ngcontent-%COMP%]{gap:4px;margin-top:3px;padding:3px 0 0;min-height:28px}}.shop-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,#667eea,#764ba2);color:#fff;border:none;padding:8px 16px;border-radius:18px;font-size:12px;font-weight:600;cursor:pointer;transition:all .3s ease;box-shadow:0 2px 8px #667eea4d;position:relative;overflow:hidden;min-width:80px;height:36px;display:flex;align-items:center;justify-content:center;flex-shrink:0}.shop-btn[_ngcontent-%COMP%]:before{content:"";position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,.2),transparent);transition:left .5s}.shop-btn[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#764ba2,#667eea);transform:translateY(-2px);box-shadow:0 4px 15px #667eea66}.shop-btn[_ngcontent-%COMP%]:hover:before{left:100%}.shop-btn[_ngcontent-%COMP%]:active{transform:translateY(0)}@media (max-width: 425px){.shop-btn[_ngcontent-%COMP%]{min-width:70px;height:32px;font-size:11px;padding:6px 12px}}@media (max-width: 375px){.shop-btn[_ngcontent-%COMP%]{min-width:65px;height:30px;font-size:10px;padding:5px 10px}}@media (max-width: 320px){.shop-btn[_ngcontent-%COMP%]{min-width:60px;height:28px;font-size:9px;padding:4px 8px}}.cart-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,#ff6b6b,#ee5a24);color:#fff;border:none;padding:0;border-radius:50%;width:36px;height:36px;cursor:pointer;transition:all .3s ease;box-shadow:0 2px 8px #ff6b6b4d;display:flex!important;align-items:center;justify-content:center;position:relative;overflow:hidden;visibility:visible!important;opacity:1!important;flex-shrink:0}.cart-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:14px;transition:transform .3s ease}.cart-btn[_ngcontent-%COMP%]:before{content:"";position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,.2),transparent);transition:left .5s}.cart-btn[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#ee5a24,#ff6b6b);transform:translateY(-2px) scale(1.05);box-shadow:0 4px 15px #ff6b6b66}.cart-btn[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%]{transform:scale(1.1)}.cart-btn[_ngcontent-%COMP%]:hover:before{left:100%}.cart-btn[_ngcontent-%COMP%]:active{transform:translateY(0) scale(1)}@media (max-width: 425px){.cart-btn[_ngcontent-%COMP%]{width:32px;height:32px}.cart-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:12px}}@media (max-width: 375px){.cart-btn[_ngcontent-%COMP%]{width:30px;height:30px}.cart-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:11px}}@media (max-width: 320px){.cart-btn[_ngcontent-%COMP%]{width:28px;height:28px}.cart-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:10px}}.wishlist-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,#ff9a9e,#fecfef);color:#e91e63;border:none;padding:0;border-radius:50%;width:36px;height:36px;cursor:pointer;transition:all .3s ease;box-shadow:0 2px 8px #ff9a9e4d;display:flex!important;align-items:center;justify-content:center;position:relative;overflow:hidden;visibility:visible!important;opacity:1!important;flex-shrink:0}.wishlist-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:14px;transition:transform .3s ease}.wishlist-btn[_ngcontent-%COMP%]:before{content:"";position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,.2),transparent);transition:left .5s}.wishlist-btn[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#fecfef,#ff9a9e);color:#c2185b;transform:translateY(-2px) scale(1.05);box-shadow:0 4px 15px #ff9a9e66}.wishlist-btn[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%]{transform:scale(1.1)}.wishlist-btn[_ngcontent-%COMP%]:hover:before{left:100%}.wishlist-btn[_ngcontent-%COMP%]:active{transform:translateY(0) scale(1)}.wishlist-btn.active[_ngcontent-%COMP%]{background:linear-gradient(135deg,#e91e63,#c2185b);color:#fff}.wishlist-btn.active[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#c2185b,#e91e63);color:#fff}@media (max-width: 425px){.wishlist-btn[_ngcontent-%COMP%]{width:32px;height:32px}.wishlist-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:12px}}@media (max-width: 375px){.wishlist-btn[_ngcontent-%COMP%]{width:30px;height:30px}.wishlist-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:11px}}@media (max-width: 320px){.wishlist-btn[_ngcontent-%COMP%]{width:28px;height:28px}.wishlist-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:10px}}.buy-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,#4facfe,#00f2fe);color:#fff;border:none;padding:0;border-radius:50%;width:36px;height:36px;cursor:pointer;transition:all .3s ease;box-shadow:0 2px 8px #4facfe4d;display:flex!important;align-items:center;justify-content:center;position:relative;overflow:hidden;visibility:visible!important;opacity:1!important;flex-shrink:0}.buy-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:14px;transition:transform .3s ease}.buy-btn[_ngcontent-%COMP%]:before{content:"";position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,.2),transparent);transition:left .5s}.buy-btn[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#00f2fe,#4facfe);transform:translateY(-2px) scale(1.05);box-shadow:0 4px 15px #4facfe66}.buy-btn[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%]{transform:scale(1.1)}.buy-btn[_ngcontent-%COMP%]:hover:before{left:100%}.buy-btn[_ngcontent-%COMP%]:active{transform:translateY(0) scale(1)}@media (max-width: 425px){.buy-btn[_ngcontent-%COMP%]{width:32px;height:32px}.buy-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:12px}}@media (max-width: 375px){.buy-btn[_ngcontent-%COMP%]{width:30px;height:30px}.buy-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:11px}}@media (max-width: 320px){.buy-btn[_ngcontent-%COMP%]{width:28px;height:28px}.buy-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:10px}}@media (max-width: 768px){.ecommerce-section[_ngcontent-%COMP%]{padding:12px 16px 20px;background:#fff;border-top:1px solid #efefef;margin-bottom:8px}.product-actions[_ngcontent-%COMP%]{gap:8px;padding:10px 0 15px;overflow:visible;position:relative}.shop-btn[_ngcontent-%COMP%]{padding:8px 14px;font-size:12px;border-radius:18px;min-width:60px}.cart-btn[_ngcontent-%COMP%], .wishlist-btn[_ngcontent-%COMP%], .buy-btn[_ngcontent-%COMP%]{width:36px;height:36px;padding:8px}.cart-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .wishlist-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .buy-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:14px}}@media (max-width: 480px){.ecommerce-section[_ngcontent-%COMP%]{padding:12px 8px 25px;margin-bottom:15px;background:#f8f9fa;border-top:2px solid #dee2e6}.featured-product[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start;padding:12px 8px;gap:8px}.product-details[_ngcontent-%COMP%]{width:100%;margin-bottom:8px}.product-actions[_ngcontent-%COMP%]{width:100%;gap:8px;flex-wrap:wrap;padding:10px 0 15px;justify-content:space-between}.shop-btn[_ngcontent-%COMP%]{flex:1;padding:8px 12px;font-size:11px;border-radius:16px;min-width:70px;max-width:120px}.cart-btn[_ngcontent-%COMP%], .wishlist-btn[_ngcontent-%COMP%], .buy-btn[_ngcontent-%COMP%]{width:36px;height:36px;padding:8px;flex-shrink:0}.cart-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .wishlist-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .buy-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:12px}}@media (max-width: 425px){.instagram-post[_ngcontent-%COMP%]{margin-bottom:12px!important;border-radius:0;overflow:visible!important;position:relative}.ecommerce-section[_ngcontent-%COMP%]{padding:10px!important;margin-bottom:10px!important;background:linear-gradient(135deg,#f8f9fa,#e9ecef)!important;border:2px solid #dee2e6!important;border-radius:12px!important;position:relative!important;z-index:100!important;min-height:140px!important;overflow:visible!important;box-shadow:0 6px 20px #0000001a!important;max-width:385px}.product-showcase[_ngcontent-%COMP%]{gap:12px!important;overflow:visible!important}.featured-product[_ngcontent-%COMP%]{display:flex!important;flex-direction:column!important;align-items:stretch!important;padding:12px!important;background:#fff!important;border:1px solid #e9ecef!important;border-radius:10px!important;margin-bottom:10px!important;overflow:visible!important;position:relative!important;box-shadow:0 2px 8px #00000014!important;transition:all .3s ease!important;gap:8px!important}.featured-product[_ngcontent-%COMP%]:hover{transform:translateY(-2px)!important;box-shadow:0 4px 15px #0000001f!important}.featured-product[_ngcontent-%COMP%] > .product-thumbnail[_ngcontent-%COMP%]{display:none!important}.featured-product[_ngcontent-%COMP%] > .product-details[_ngcontent-%COMP%]{display:none!important}.product-header[_ngcontent-%COMP%]{display:flex!important;align-items:center!important;gap:8px!important;width:100%!important;padding-bottom:5px!important;border-bottom:1px solid #f0f0f0!important;margin-bottom:0!important}.product-header[_ngcontent-%COMP%]   .product-thumbnail[_ngcontent-%COMP%]{width:45px!important;height:45px!important;border-radius:6px!important;border:1px solid #e9ecef!important;object-fit:cover!important;flex-shrink:0!important;display:block!important}.product-header[_ngcontent-%COMP%]   .product-details[_ngcontent-%COMP%]{flex:1!important;display:flex!important;flex-direction:column!important;gap:4px!important;min-width:0!important}.product-header[_ngcontent-%COMP%]   .product-name[_ngcontent-%COMP%]{font-size:13px!important;font-weight:600!important;margin-bottom:4px!important;color:#212529!important;display:block!important;line-height:1.3!important;overflow:hidden!important;text-overflow:ellipsis!important;white-space:nowrap!important}.product-header[_ngcontent-%COMP%]   .product-price[_ngcontent-%COMP%]{font-size:12px!important;color:#6c757d!important;font-weight:500!important;display:block!important}.product-actions[_ngcontent-%COMP%]{display:flex!important;flex-direction:row!important;gap:6px!important;padding:5px 0 0!important;align-items:center!important;justify-content:flex-start!important;overflow:visible!important;position:relative!important;z-index:10!important;width:100%!important;margin-top:5px!important}.shop-btn[_ngcontent-%COMP%]{width:70px!important;padding:6px 12px!important;font-size:11px!important;font-weight:600!important;border-radius:16px!important;min-height:32px!important;height:32px!important;display:flex!important;align-items:center!important;justify-content:center!important;margin-bottom:0!important;margin-right:0!important;background:linear-gradient(135deg,#667eea,#764ba2)!important;color:#fff!important;border:none!important;box-shadow:0 2px 8px #667eea4d!important;transition:all .3s ease!important;flex-shrink:0!important;visibility:visible!important;opacity:1!important;position:relative!important;z-index:10!important}.cart-btn[_ngcontent-%COMP%]{width:32px!important;height:32px!important;padding:0!important;border-radius:50%!important;display:flex!important;align-items:center!important;justify-content:center!important;border:none!important;cursor:pointer!important;transition:all .3s ease!important;margin:0!important;flex-shrink:0!important;visibility:visible!important;opacity:1!important;position:relative!important;z-index:10!important;background:linear-gradient(135deg,#ff6b6b,#ee5a24)!important;color:#fff!important;box-shadow:0 2px 6px #ff6b6b4d!important}.cart-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:12px!important;font-weight:700!important}.wishlist-btn[_ngcontent-%COMP%]{width:32px!important;height:32px!important;padding:0!important;border-radius:50%!important;display:flex!important;align-items:center!important;justify-content:center!important;border:none!important;cursor:pointer!important;transition:all .3s ease!important;margin:0!important;flex-shrink:0!important;visibility:visible!important;opacity:1!important;position:relative!important;z-index:10!important;background:linear-gradient(135deg,#ff9a9e,#fecfef)!important;color:#e91e63!important;box-shadow:0 2px 6px #ff9a9e4d!important}.wishlist-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:12px!important;font-weight:700!important}.buy-btn[_ngcontent-%COMP%]{width:32px!important;height:32px!important;padding:0!important;border-radius:50%!important;display:flex!important;align-items:center!important;justify-content:center!important;border:none!important;cursor:pointer!important;transition:all .3s ease!important;margin:0!important;flex-shrink:0!important;visibility:visible!important;opacity:1!important;position:relative!important;z-index:10!important;background:linear-gradient(135deg,#4facfe,#00f2fe)!important;color:#fff!important;box-shadow:0 2px 6px #4facfe4d!important}.buy-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:12px!important;font-weight:700!important}.cart-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,#ff6b6b,#ee5a24)!important;color:#fff!important;box-shadow:0 2px 6px #ff6b6b4d!important}.wishlist-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,#ff9ff3,#f368e0)!important;color:#fff!important;box-shadow:0 2px 6px #ff9ff34d!important}.buy-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,#4facfe,#00f2fe)!important;color:#fff!important;box-shadow:0 2px 6px #4facfe4d!important}.cart-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .wishlist-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .buy-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:14px!important;font-weight:700!important}.shop-btn[_ngcontent-%COMP%]:hover{transform:translateY(-1px)!important;box-shadow:0 4px 12px #667eea80!important}.cart-btn[_ngcontent-%COMP%]:hover, .wishlist-btn[_ngcontent-%COMP%]:hover, .buy-btn[_ngcontent-%COMP%]:hover{transform:translateY(-1px) scale(1.05)!important}}@media (max-width: 375px){.ecommerce-section[_ngcontent-%COMP%]{padding:10px 8px!important;margin-bottom:18px!important;background:linear-gradient(135deg,#f1f3f4,#e8eaed)!important;border:1px solid #dadce0!important;border-radius:10px!important;min-height:130px!important;box-shadow:0 4px 16px #00000014!important;max-width:335px}.featured-product[_ngcontent-%COMP%]{padding:10px!important;margin-bottom:8px!important;border-radius:8px!important}.product-thumbnail[_ngcontent-%COMP%]{width:50px!important;height:50px!important;margin-right:10px!important;border-radius:6px!important}.product-name[_ngcontent-%COMP%]{font-size:12px!important;font-weight:600!important;line-height:1.2!important}.product-price[_ngcontent-%COMP%]{font-size:11px!important;font-weight:500!important}.product-actions[_ngcontent-%COMP%]{min-width:70px!important;gap:4px!important}.shop-btn[_ngcontent-%COMP%]{width:65px!important;padding:6px 8px!important;font-size:10px!important;min-height:28px!important;border-radius:14px!important}.cart-btn[_ngcontent-%COMP%], .wishlist-btn[_ngcontent-%COMP%], .buy-btn[_ngcontent-%COMP%]{width:28px!important;height:28px!important;padding:4px!important}.cart-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .wishlist-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .buy-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:12px!important}}@media (max-width: 350px){.ecommerce-section[_ngcontent-%COMP%]{padding:12px 6px 25px!important;margin-bottom:15px!important;background:linear-gradient(135deg,#f8f9fa,#e9ecef)!important;border:1px solid #ced4da!important;border-radius:8px!important;min-height:120px!important;box-shadow:0 3px 12px #0000000f!important;max-width:290px}.featured-product[_ngcontent-%COMP%]{padding:8px!important;margin-bottom:6px!important;border-radius:6px!important;gap:8px!important}.product-thumbnail[_ngcontent-%COMP%]{width:45px!important;height:45px!important;margin-right:8px!important;border-radius:5px!important}.product-details[_ngcontent-%COMP%]{margin-right:6px!important}.product-name[_ngcontent-%COMP%]{font-size:11px!important;font-weight:600!important;line-height:1.2!important;margin-bottom:2px!important}.product-price[_ngcontent-%COMP%]{font-size:10px!important;font-weight:500!important}.product-actions[_ngcontent-%COMP%]{min-width:60px!important;gap:3px!important}.shop-btn[_ngcontent-%COMP%]{width:55px!important;padding:5px 6px!important;font-size:9px!important;min-height:24px!important;border-radius:12px!important;margin-bottom:2px!important}.cart-btn[_ngcontent-%COMP%], .wishlist-btn[_ngcontent-%COMP%], .buy-btn[_ngcontent-%COMP%]{width:24px!important;height:24px!important;padding:3px!important;margin:.5px!important}.cart-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .wishlist-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .buy-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:10px!important}}@media (max-width: 320px){.ecommerce-section[_ngcontent-%COMP%]{padding:10px 4px!important;margin-bottom:12px!important;background:#f8f9fa!important;border:1px solid #dee2e6!important;border-radius:6px!important;min-height:110px!important;box-shadow:0 2px 8px #0000000d!important;max-width:285px}.featured-product[_ngcontent-%COMP%]{padding:6px!important;margin-bottom:4px!important;border-radius:4px!important;gap:6px!important}.product-thumbnail[_ngcontent-%COMP%]{width:40px!important;height:40px!important;margin-right:6px!important;border-radius:4px!important}.product-details[_ngcontent-%COMP%]{margin-right:4px!important}.product-name[_ngcontent-%COMP%]{font-size:10px!important;font-weight:600!important;line-height:1.1!important;margin-bottom:1px!important}.product-price[_ngcontent-%COMP%]{font-size:9px!important;font-weight:500!important}.product-actions[_ngcontent-%COMP%]{min-width:50px!important;gap:2px!important}.shop-btn[_ngcontent-%COMP%]{width:45px!important;padding:4px 5px!important;font-size:8px!important;min-height:20px!important;border-radius:10px!important;margin-bottom:1px!important}.cart-btn[_ngcontent-%COMP%], .wishlist-btn[_ngcontent-%COMP%], .buy-btn[_ngcontent-%COMP%]{width:20px!important;height:20px!important;padding:2px!important;margin:.5px!important}.cart-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .wishlist-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .buy-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:8px!important}}.load-more[_ngcontent-%COMP%]{text-align:center;padding:20px}.load-more-btn[_ngcontent-%COMP%]{background:#0095f6;color:#fff;border:none;padding:12px 24px;border-radius:8px;font-size:14px;font-weight:600;cursor:pointer;transition:background .2s ease}.load-more-btn[_ngcontent-%COMP%]:hover{background:#00376b}.empty-feed[_ngcontent-%COMP%]{text-align:center;padding:60px 20px;background:#fff;border:1px solid #dbdbdb;border-radius:8px;margin:20px}.empty-content[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:48px;color:#dbdbdb;margin-bottom:16px}.empty-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:20px;font-weight:600;color:#262626;margin:0 0 8px}.empty-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:14px;color:#8e8e8e;margin:0}@media (max-width: 768px){.instagram-feed[_ngcontent-%COMP%]{max-width:100%}.instagram-post[_ngcontent-%COMP%]{border-radius:0;border-left:none;border-right:none;margin-bottom:0}.feed-posts[_ngcontent-%COMP%]{gap:0}}@media (max-width: 480px){.post-header[_ngcontent-%COMP%]{padding:12px}.post-actions[_ngcontent-%COMP%]{padding:6px 12px}.likes-section[_ngcontent-%COMP%], .post-caption[_ngcontent-%COMP%], .comments-preview[_ngcontent-%COMP%], .post-time[_ngcontent-%COMP%]{padding-left:12px;padding-right:12px}.add-comment-section[_ngcontent-%COMP%]{padding:12px}.ecommerce-section[_ngcontent-%COMP%]{padding:12px 16px 20px;background:#f8f9fa;border-top:2px solid #e9ecef;margin-bottom:8px;position:relative;z-index:1;overflow:visible}}']})}}return i})();var s=l(467),f=l(8359),_=l(1708),u=l(4412),M=l(1626);let C=(()=>{class i{constructor(t){this.http=t,this.API_URL="http://localhost:3001/api",this.trendingProductsSubject=new u.t([]),this.suggestedProductsSubject=new u.t([]),this.newArrivalsSubject=new u.t([]),this.featuredBrandsSubject=new u.t([]),this.influencersSubject=new u.t([]),this.trendingProducts$=this.trendingProductsSubject.asObservable(),this.suggestedProducts$=this.suggestedProductsSubject.asObservable(),this.newArrivals$=this.newArrivalsSubject.asObservable(),this.featuredBrands$=this.featuredBrandsSubject.asObservable(),this.influencers$=this.influencersSubject.asObservable()}getTrendingProducts(t=1,o=12){return this.http.get(`${this.API_URL}/v1/products/trending`,{params:{page:t.toString(),limit:o.toString()}})}getSuggestedProducts(t=1,o=12){return this.http.get(`${this.API_URL}/v1/products/suggested`,{params:{page:t.toString(),limit:o.toString()}})}getNewArrivals(t=1,o=12){return this.http.get(`${this.API_URL}/v1/products/new-arrivals`,{params:{page:t.toString(),limit:o.toString()}})}getFeaturedBrands(){return this.http.get(`${this.API_URL}/v1/products/featured-brands`)}getInfluencers(t=1,o=10){return this.http.get(`${this.API_URL}/v1/users/influencers`,{params:{page:t.toString(),limit:o.toString()}})}loadTrendingProducts(){var t=this;return(0,s.A)(function*(o=1,e=12){try{const r=yield t.getTrendingProducts(o,e).toPromise();r?.success&&r?.products&&t.trendingProductsSubject.next(r.products)}catch(r){console.error("Error loading trending products:",r)}}).apply(this,arguments)}loadSuggestedProducts(){var t=this;return(0,s.A)(function*(o=1,e=12){try{const r=yield t.getSuggestedProducts(o,e).toPromise();r?.success&&r?.products&&t.suggestedProductsSubject.next(r.products)}catch(r){console.error("Error loading suggested products:",r)}}).apply(this,arguments)}loadNewArrivals(){var t=this;return(0,s.A)(function*(o=1,e=12){try{const r=yield t.getNewArrivals(o,e).toPromise();r?.success&&r?.products&&t.newArrivalsSubject.next(r.products)}catch(r){console.error("Error loading new arrivals:",r)}}).apply(this,arguments)}loadFeaturedBrands(){var t=this;return(0,s.A)(function*(){try{const o=yield t.getFeaturedBrands().toPromise();o?.success&&o?.brands&&t.featuredBrandsSubject.next(o.brands)}catch(o){console.error("Error loading featured brands:",o)}})()}loadInfluencers(){var t=this;return(0,s.A)(function*(o=1,e=10){try{const r=yield t.getInfluencers(o,e).toPromise();r?.success&&r?.influencers&&t.influencersSubject.next(r.influencers)}catch(r){console.error("Error loading influencers:",r)}}).apply(this,arguments)}clearCache(){this.trendingProductsSubject.next([]),this.suggestedProductsSubject.next([]),this.newArrivalsSubject.next([]),this.featuredBrandsSubject.next([]),this.influencersSubject.next([])}getCurrentTrendingProducts(){return this.trendingProductsSubject.value}getCurrentSuggestedProducts(){return this.suggestedProductsSubject.value}getCurrentNewArrivals(){return this.newArrivalsSubject.value}getCurrentFeaturedBrands(){return this.featuredBrandsSubject.value}getCurrentInfluencers(){return this.influencersSubject.value}static{this.\u0275fac=function(o){return new(o||i)(n.KVO(M.Qq))}}static{this.\u0275prov=n.jDH({token:i,factory:i.\u0275fac,providedIn:"root"})}}return i})(),P=(()=>{class i{constructor(t){this.http=t,this.API_URL="http://localhost:3001/api",this.likedProductsSubject=new u.t(new Set),this.likedPostsSubject=new u.t(new Set),this.likedProducts$=this.likedProductsSubject.asObservable(),this.likedPosts$=this.likedPostsSubject.asObservable(),this.loadUserLikes()}likeProduct(t){var o=this;return(0,s.A)(function*(){try{const e=localStorage.getItem("token");if(!e)throw new Error("Authentication required");const r=yield o.http.post(`${o.API_URL}/v1/ecommerce/products/${t}/like`,{},{headers:{Authorization:`Bearer ${e}`}}).toPromise();if(r?.success){const c=o.likedProductsSubject.value;r.isLiked?c.add(t):c.delete(t),o.likedProductsSubject.next(new Set(c))}return r||{success:!1,message:"Unknown error"}}catch(e){return console.error("Error liking product:",e),{success:!1,message:"Failed to like product"}}})()}shareProduct(t,o){var e=this;return(0,s.A)(function*(){try{const r=localStorage.getItem("token");if(!r)throw new Error("Authentication required");return(yield e.http.post(`${e.API_URL}/v1/ecommerce/products/${t}/share`,o,{headers:{Authorization:`Bearer ${r}`}}).toPromise())||{success:!1,message:"Unknown error"}}catch(r){return console.error("Error sharing product:",r),{success:!1,message:"Failed to share product"}}})()}commentOnProduct(t,o,e){var r=this;return(0,s.A)(function*(){try{const c=localStorage.getItem("token");if(!c)throw new Error("Authentication required");return(yield r.http.post(`${r.API_URL}/v1/product-comments`,{product:t,text:o,rating:e},{headers:{Authorization:`Bearer ${c}`}}).toPromise())||{success:!1,message:"Unknown error"}}catch(c){return console.error("Error commenting on product:",c),{success:!1,message:"Failed to add comment"}}})()}getProductComments(t){var o=this;return(0,s.A)(function*(e,r=1,c=10){try{const p=yield o.http.get(`${o.API_URL}/v1/product-comments?product=${e}&page=${r}&limit=${c}`).toPromise();return{comments:p?.data?.comments||[],total:p?.data?.total||0}}catch(p){return console.error("Error fetching product comments:",p),{comments:[],total:0}}}).apply(this,arguments)}likePost(t){var o=this;return(0,s.A)(function*(){try{const e=localStorage.getItem("token");if(!e)throw new Error("Authentication required");const r=yield o.http.post(`${o.API_URL}/v1/posts/${t}/like`,{},{headers:{Authorization:`Bearer ${e}`}}).toPromise();if(r?.success){const c=o.likedPostsSubject.value;r.isLiked?c.add(t):c.delete(t),o.likedPostsSubject.next(new Set(c))}return r||{success:!1,message:"Unknown error"}}catch(e){return console.error("Error liking post:",e),{success:!1,message:"Failed to like post"}}})()}sharePost(t,o){var e=this;return(0,s.A)(function*(){try{const r=localStorage.getItem("token");if(!r)throw new Error("Authentication required");return(yield e.http.post(`${e.API_URL}/v1/posts/${t}/share`,o,{headers:{Authorization:`Bearer ${r}`}}).toPromise())||{success:!1,message:"Unknown error"}}catch(r){return console.error("Error sharing post:",r),{success:!1,message:"Failed to share post"}}})()}commentOnPost(t,o){var e=this;return(0,s.A)(function*(){try{const r=localStorage.getItem("token");if(!r)throw new Error("Authentication required");return(yield e.http.post(`${e.API_URL}/v1/posts/${t}/comment`,{text:o},{headers:{Authorization:`Bearer ${r}`}}).toPromise())||{success:!1,message:"Unknown error"}}catch(r){return console.error("Error commenting on post:",r),{success:!1,message:"Failed to add comment"}}})()}isProductLiked(t){return this.likedProductsSubject.value.has(t)}isPostLiked(t){return this.likedPostsSubject.value.has(t)}loadUserLikes(){var t=this;return(0,s.A)(function*(){try{const o=localStorage.getItem("token");if(!o)return;const e=yield t.http.get(`${t.API_URL}/v1/user/liked-products`,{headers:{Authorization:`Bearer ${o}`}}).toPromise();if(e?.success){const c=new Set(e.data.map(p=>p._id));t.likedProductsSubject.next(c)}const r=yield t.http.get(`${t.API_URL}/v1/user/liked-posts`,{headers:{Authorization:`Bearer ${o}`}}).toPromise();if(r?.success){const c=new Set(r.data.map(p=>p._id));t.likedPostsSubject.next(c)}}catch(o){console.error("Error loading user likes:",o)}})()}clearUserData(){this.likedProductsSubject.next(new Set),this.likedPostsSubject.next(new Set)}generateShareUrl(t,o,e){const r=encodeURIComponent(o),c=encodeURIComponent(e);switch(t){case"facebook":return`https://www.facebook.com/sharer/sharer.php?u=${r}`;case"twitter":return`https://twitter.com/intent/tweet?url=${r}&text=${c}`;case"whatsapp":return`https://wa.me/?text=${c}%20${r}`;case"email":return`mailto:?subject=${c}&body=${r}`;default:return o}}static{this.\u0275fac=function(o){return new(o||i)(n.KVO(M.Qq))}}static{this.\u0275prov=n.jDH({token:i,factory:i.\u0275fac,providedIn:"root"})}}return i})();const q=()=>[1,2,3,4,5,6,7,8],Q=()=>[1,2,3,4,5];function Z(i,a){if(1&i){const t=n.RV6();n.j41(0,"div",13)(1,"button",14),n.bIt("click",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.toggleSectionLike())}),n.nrm(2,"ion-icon",15),n.j41(3,"span",16),n.EFF(4),n.k0s()(),n.j41(5,"button",17),n.bIt("click",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.openComments())}),n.nrm(6,"ion-icon",18),n.j41(7,"span",16),n.EFF(8),n.k0s()(),n.j41(9,"button",19),n.bIt("click",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.shareSection())}),n.nrm(10,"ion-icon",20),n.j41(11,"span",21),n.EFF(12,"Share"),n.k0s()(),n.j41(13,"button",22),n.bIt("click",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.toggleSectionBookmark())}),n.nrm(14,"ion-icon",15),n.k0s(),n.j41(15,"button",23),n.bIt("click",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.openMusicPlayer())}),n.nrm(16,"ion-icon",24),n.j41(17,"span",21),n.EFF(18,"Music"),n.k0s()()()}if(2&i){const t=n.XpG();n.R7$(),n.AVh("active",t.isSectionLiked),n.R7$(),n.Y8G("name",t.isSectionLiked?"heart":"heart-outline"),n.R7$(2),n.JRh(t.formatCount(t.sectionLikes)),n.R7$(4),n.JRh(t.formatCount(t.sectionComments)),n.R7$(5),n.AVh("active",t.isSectionBookmarked),n.R7$(),n.Y8G("name",t.isSectionBookmarked?"bookmark":"bookmark-outline")}}function nn(i,a){1&i&&(n.j41(0,"div",28),n.nrm(1,"div",29),n.j41(2,"div",30),n.nrm(3,"div",31)(4,"div",32)(5,"div",33),n.k0s()())}function tn(i,a){1&i&&(n.j41(0,"div",25)(1,"div",26),n.DNE(2,nn,6,0,"div",27),n.k0s()()),2&i&&(n.R7$(2),n.Y8G("ngForOf",n.lJ4(1,q)))}function en(i,a){if(1&i){const t=n.RV6();n.j41(0,"div",34),n.nrm(1,"ion-icon",35),n.j41(2,"p",36),n.EFF(3),n.k0s(),n.j41(4,"button",37),n.bIt("click",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.onRetry())}),n.nrm(5,"ion-icon",38),n.EFF(6," Try Again "),n.k0s()()}if(2&i){const t=n.XpG();n.R7$(3),n.JRh(t.error)}}function on(i,a){if(1&i&&(n.j41(0,"div",69),n.EFF(1),n.k0s()),2&i){const t=n.XpG().$implicit,o=n.XpG(2);n.R7$(),n.SpI(" ",o.getDiscountPercentage(t),"% OFF ")}}function rn(i,a){if(1&i&&(n.j41(0,"span",70),n.EFF(1),n.k0s()),2&i){const t=n.XpG().$implicit,o=n.XpG(2);n.R7$(),n.JRh(o.formatPrice(t.originalPrice))}}function an(i,a){if(1&i&&n.nrm(0,"ion-icon",15),2&i){const t=a.$implicit,o=n.XpG().$implicit;n.AVh("filled",t<=o.rating.average),n.Y8G("name",t<=o.rating.average?"star":"star-outline")}}function cn(i,a){if(1&i){const t=n.RV6();n.j41(0,"div",46),n.bIt("click",function(){const e=n.eBV(t).$implicit,r=n.XpG(2);return n.Njj(r.onProductClick(e))}),n.j41(1,"div",47),n.nrm(2,"img",48),n.j41(3,"div",49),n.nrm(4,"ion-icon",50),n.EFF(5," Trending "),n.k0s(),n.DNE(6,on,2,1,"div",51),n.j41(7,"div",52)(8,"button",14),n.bIt("click",function(e){const r=n.eBV(t).$implicit,c=n.XpG(2);return n.Njj(c.onLikeProduct(r,e))}),n.nrm(9,"ion-icon",15),n.k0s(),n.j41(10,"button",19),n.bIt("click",function(e){const r=n.eBV(t).$implicit,c=n.XpG(2);return n.Njj(c.onShareProduct(r,e))}),n.nrm(11,"ion-icon",53),n.k0s()()(),n.j41(12,"div",54)(13,"div",55),n.EFF(14),n.k0s(),n.j41(15,"h3",56),n.EFF(16),n.k0s(),n.j41(17,"div",57)(18,"span",58),n.EFF(19),n.k0s(),n.DNE(20,rn,2,1,"span",59),n.k0s(),n.j41(21,"div",60)(22,"div",61),n.DNE(23,an,1,3,"ion-icon",62),n.k0s(),n.j41(24,"span",63),n.EFF(25),n.k0s()(),n.j41(26,"div",64)(27,"button",65),n.bIt("click",function(e){const r=n.eBV(t).$implicit,c=n.XpG(2);return n.Njj(c.onAddToCart(r,e))}),n.nrm(28,"ion-icon",66),n.EFF(29," Add to Cart "),n.k0s(),n.j41(30,"button",67),n.bIt("click",function(e){const r=n.eBV(t).$implicit,c=n.XpG(2);return n.Njj(c.onAddToWishlist(r,e))}),n.nrm(31,"ion-icon",68),n.k0s()()()()}if(2&i){const t=a.$implicit,o=n.XpG(2);n.R7$(2),n.Y8G("src",t.images[0].url,n.B4B)("alt",t.images[0].alt||t.name),n.R7$(4),n.Y8G("ngIf",o.getDiscountPercentage(t)>0),n.R7$(2),n.AVh("liked",o.isProductLiked(t._id)),n.BMQ("aria-label","Like "+t.name),n.R7$(),n.Y8G("name",o.isProductLiked(t._id)?"heart":"heart-outline"),n.R7$(),n.BMQ("aria-label","Share "+t.name),n.R7$(4),n.JRh(t.brand),n.R7$(2),n.JRh(t.name),n.R7$(3),n.JRh(o.formatPrice(t.price)),n.R7$(),n.Y8G("ngIf",t.originalPrice&&t.originalPrice>t.price),n.R7$(3),n.Y8G("ngForOf",n.lJ4(14,Q)),n.R7$(2),n.SpI("(",t.rating.count,")")}}function sn(i,a){if(1&i){const t=n.RV6();n.j41(0,"div",39)(1,"button",40),n.bIt("click",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.slidePrev())}),n.nrm(2,"ion-icon",41),n.k0s(),n.j41(3,"button",42),n.bIt("click",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.slideNext())}),n.nrm(4,"ion-icon",8),n.k0s(),n.j41(5,"div",43),n.bIt("mouseenter",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.pauseAutoSlide())})("mouseleave",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.resumeAutoSlide())}),n.j41(6,"div",44),n.DNE(7,cn,32,15,"div",45),n.k0s()()()}if(2&i){const t=n.XpG();n.R7$(),n.Y8G("disabled",0===t.currentSlide),n.R7$(2),n.Y8G("disabled",t.currentSlide>=t.maxSlide),n.R7$(3),n.xc7("transform","translateX("+t.slideOffset+"px)"),n.R7$(),n.Y8G("ngForOf",t.trendingProducts)("ngForTrackBy",t.trackByProductId)}}function dn(i,a){1&i&&(n.j41(0,"div",71),n.nrm(1,"ion-icon",72),n.j41(2,"h3",73),n.EFF(3,"No Trending Products"),n.k0s(),n.j41(4,"p",74),n.EFF(5,"Check back later for trending items"),n.k0s()())}let v=(()=>{class i{constructor(t,o,e,r,c){this.trendingService=t,this.socialService=o,this.cartService=e,this.wishlistService=r,this.router=c,this.trendingProducts=[],this.isLoading=!0,this.error=null,this.likedProducts=new Set,this.subscription=new f.yU,this.currentSlide=0,this.slideOffset=0,this.cardWidth=280,this.visibleCards=4,this.maxSlide=0,this.autoSlideDelay=3e3,this.isAutoSliding=!0,this.isPaused=!1,this.isSectionLiked=!1,this.isSectionBookmarked=!1,this.sectionLikes=365,this.sectionComments=105,this.isMobile=!1,this.carouselOptions={loop:!0,mouseDrag:!0,touchDrag:!0,pullDrag:!1,dots:!0,navSpeed:700,navText:['<ion-icon name="chevron-back"></ion-icon>','<ion-icon name="chevron-forward"></ion-icon>'],autoplay:!0,autoplayTimeout:4e3,autoplayHoverPause:!0,autoplaySpeed:1e3,smartSpeed:1e3,fluidSpeed:!0,responsive:{0:{items:1,margin:10,nav:!1,dots:!0},576:{items:2,margin:15,nav:!0,dots:!0},768:{items:3,margin:20,nav:!0,dots:!0},992:{items:4,margin:20,nav:!0,dots:!1}},nav:!0,margin:20,stagePadding:0,center:!1,animateOut:!1,animateIn:!1}}ngOnInit(){this.loadTrendingProducts(),this.subscribeTrendingProducts(),this.subscribeLikedProducts(),this.updateResponsiveSettings(),this.setupResizeListener(),this.checkMobileDevice()}ngOnDestroy(){this.subscription.unsubscribe(),this.stopAutoSlide()}subscribeTrendingProducts(){this.subscription.add(this.trendingService.trendingProducts$.subscribe(t=>{this.trendingProducts=t,this.isLoading=!1,this.updateSliderOnProductsLoad()}))}subscribeLikedProducts(){this.subscription.add(this.socialService.likedProducts$.subscribe(t=>{this.likedProducts=t}))}loadTrendingProducts(){var t=this;return(0,s.A)(function*(){try{t.isLoading=!0,t.error=null,yield t.trendingService.loadTrendingProducts(1,8)}catch(o){console.error("Error loading trending products:",o),t.error="Failed to load trending products",t.isLoading=!1}})()}onProductClick(t){this.router.navigate(["/product",t._id])}onLikeProduct(t,o){var e=this;return(0,s.A)(function*(){o.stopPropagation();try{const r=yield e.socialService.likeProduct(t._id);r.success?console.log(r.message):console.error("Failed to like product:",r.message)}catch(r){console.error("Error liking product:",r)}})()}onShareProduct(t,o){var e=this;return(0,s.A)(function*(){o.stopPropagation();try{const r=`${window.location.origin}/product/${t._id}`;yield navigator.clipboard.writeText(r),yield e.socialService.shareProduct(t._id,{platform:"copy_link",message:`Check out this amazing ${t.name} from ${t.brand}!`}),console.log("Product link copied to clipboard!")}catch(r){console.error("Error sharing product:",r)}})()}onAddToCart(t,o){var e=this;return(0,s.A)(function*(){o.stopPropagation();try{yield e.cartService.addToCart(t._id,1),console.log("Product added to cart!")}catch(r){console.error("Error adding to cart:",r)}})()}onAddToWishlist(t,o){var e=this;return(0,s.A)(function*(){o.stopPropagation();try{yield e.wishlistService.addToWishlist(t._id),console.log("Product added to wishlist!")}catch(r){console.error("Error adding to wishlist:",r)}})()}getDiscountPercentage(t){return t.originalPrice&&t.originalPrice>t.price?Math.round((t.originalPrice-t.price)/t.originalPrice*100):0}formatPrice(t){return new Intl.NumberFormat("en-IN",{style:"currency",currency:"INR",minimumFractionDigits:0}).format(t)}onRetry(){this.loadTrendingProducts()}onViewAll(){this.router.navigate(["/products"],{queryParams:{filter:"trending"}})}isProductLiked(t){return this.likedProducts.has(t)}trackByProductId(t,o){return o._id}startAutoSlide(){!this.isAutoSliding||this.isPaused||(this.stopAutoSlide(),this.autoSlideInterval=setInterval(()=>{!this.isPaused&&this.trendingProducts.length>this.visibleCards&&this.autoSlideNext()},this.autoSlideDelay))}stopAutoSlide(){this.autoSlideInterval&&(clearInterval(this.autoSlideInterval),this.autoSlideInterval=null)}autoSlideNext(){this.currentSlide>=this.maxSlide?this.currentSlide=0:this.currentSlide++,this.updateSlideOffset()}pauseAutoSlide(){this.isPaused=!0,this.stopAutoSlide()}resumeAutoSlide(){this.isPaused=!1,this.startAutoSlide()}updateResponsiveSettings(){const t=window.innerWidth;t<=480?(this.cardWidth=195,this.visibleCards=1):t<=768?(this.cardWidth=215,this.visibleCards=2):t<=1200?(this.cardWidth=260,this.visibleCards=3):(this.cardWidth=280,this.visibleCards=4),this.updateSliderLimits(),this.updateSlideOffset()}setupResizeListener(){window.addEventListener("resize",()=>{this.updateResponsiveSettings()})}updateSliderLimits(){this.maxSlide=Math.max(0,this.trendingProducts.length-this.visibleCards)}slidePrev(){this.currentSlide>0&&(this.currentSlide--,this.updateSlideOffset(),this.restartAutoSlideAfterInteraction())}slideNext(){this.currentSlide<this.maxSlide&&(this.currentSlide++,this.updateSlideOffset(),this.restartAutoSlideAfterInteraction())}restartAutoSlideAfterInteraction(){this.stopAutoSlide(),setTimeout(()=>{this.startAutoSlide()},2e3)}updateSlideOffset(){this.slideOffset=-this.currentSlide*this.cardWidth}updateSliderOnProductsLoad(){setTimeout(()=>{this.updateSliderLimits(),this.currentSlide=0,this.slideOffset=0,this.startAutoSlide()},100)}toggleSectionLike(){this.isSectionLiked=!this.isSectionLiked,this.isSectionLiked?this.sectionLikes++:this.sectionLikes--}toggleSectionBookmark(){this.isSectionBookmarked=!this.isSectionBookmarked}openComments(){console.log("Opening comments for trending products section")}shareSection(){navigator.share?navigator.share({title:"Trending Products",text:"Check out these trending fashion products!",url:window.location.href}):(navigator.clipboard.writeText(window.location.href),console.log("Link copied to clipboard"))}openMusicPlayer(){console.log("Opening music player for trending products")}formatCount(t){return t>=1e6?(t/1e6).toFixed(1)+"M":t>=1e3?(t/1e3).toFixed(1)+"K":t.toString()}checkMobileDevice(){this.isMobile=window.innerWidth<=768}static{this.\u0275fac=function(o){return new(o||i)(n.rXU(C),n.rXU(P),n.rXU(b.CartService),n.rXU(x.WishlistService),n.rXU(m.Ix))}}static{this.\u0275cmp=n.VBU({type:i,selectors:[["app-trending-products"]],standalone:!0,features:[n.aNF],decls:16,vars:5,consts:[[1,"trending-products-container"],["class","mobile-action-buttons",4,"ngIf"],[1,"section-header"],[1,"header-content"],[1,"section-title"],["name","trending-up",1,"title-icon"],[1,"section-subtitle"],[1,"view-all-btn",3,"click"],["name","chevron-forward"],["class","loading-container",4,"ngIf"],["class","error-container",4,"ngIf"],["class","products-slider-container",4,"ngIf"],["class","empty-container",4,"ngIf"],[1,"mobile-action-buttons"],[1,"action-btn","like-btn",3,"click"],[3,"name"],[1,"action-count"],[1,"action-btn","comment-btn",3,"click"],["name","chatbubble-outline"],[1,"action-btn","share-btn",3,"click"],["name","arrow-redo-outline"],[1,"action-text"],[1,"action-btn","bookmark-btn",3,"click"],[1,"action-btn","music-btn",3,"click"],["name","musical-notes"],[1,"loading-container"],[1,"loading-grid"],["class","loading-card",4,"ngFor","ngForOf"],[1,"loading-card"],[1,"loading-image"],[1,"loading-content"],[1,"loading-line","short"],[1,"loading-line","medium"],[1,"loading-line","long"],[1,"error-container"],["name","alert-circle",1,"error-icon"],[1,"error-message"],[1,"retry-btn",3,"click"],["name","refresh"],[1,"products-slider-container"],[1,"slider-nav","prev-btn",3,"click","disabled"],["name","chevron-back"],[1,"slider-nav","next-btn",3,"click","disabled"],[1,"products-slider-wrapper",3,"mouseenter","mouseleave"],[1,"products-slider"],["class","product-card",3,"click",4,"ngFor","ngForOf","ngForTrackBy"],[1,"product-card",3,"click"],[1,"product-image-container"],["loading","lazy",1,"product-image",3,"src","alt"],[1,"trending-badge"],["name","trending-up"],["class","discount-badge",4,"ngIf"],[1,"action-buttons"],["name","share-outline"],[1,"product-info"],[1,"product-brand"],[1,"product-name"],[1,"price-section"],[1,"current-price"],["class","original-price",4,"ngIf"],[1,"rating-section"],[1,"stars"],[3,"name","filled",4,"ngFor","ngForOf"],[1,"rating-text"],[1,"product-actions"],[1,"cart-btn",3,"click"],["name","bag-add-outline"],[1,"wishlist-btn",3,"click"],["name","heart-outline"],[1,"discount-badge"],[1,"original-price"],[1,"empty-container"],["name","trending-up-outline",1,"empty-icon"],[1,"empty-title"],[1,"empty-message"]],template:function(o,e){1&o&&(n.j41(0,"div",0),n.DNE(1,Z,19,8,"div",1),n.j41(2,"div",2)(3,"div",3)(4,"h2",4),n.nrm(5,"ion-icon",5),n.EFF(6," Trending Now "),n.k0s(),n.j41(7,"p",6),n.EFF(8,"Most popular products this week"),n.k0s()(),n.j41(9,"button",7),n.bIt("click",function(){return e.onViewAll()}),n.EFF(10," View All "),n.nrm(11,"ion-icon",8),n.k0s()(),n.DNE(12,tn,3,2,"div",9)(13,en,7,1,"div",10)(14,sn,8,6,"div",11)(15,dn,6,0,"div",12),n.k0s()),2&o&&(n.R7$(),n.Y8G("ngIf",e.isMobile),n.R7$(11),n.Y8G("ngIf",e.isLoading),n.R7$(),n.Y8G("ngIf",e.error&&!e.isLoading),n.R7$(),n.Y8G("ngIf",!e.isLoading&&!e.error&&e.trendingProducts.length>0),n.R7$(),n.Y8G("ngIf",!e.isLoading&&!e.error&&0===e.trendingProducts.length))},dependencies:[d.MD,d.Sq,d.bT,g.bv,g.iq,_.Rl],styles:['.trending-products-container[_ngcontent-%COMP%]{padding:20px;background:linear-gradient(135deg,#f8f9fa,#e9ecef);border-radius:16px;margin-bottom:24px;position:relative;max-width:675px}.mobile-action-buttons[_ngcontent-%COMP%]{position:absolute;right:15px;top:50%;transform:translateY(-50%);display:flex;flex-direction:column;gap:20px;z-index:10}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]{width:48px;height:48px;border-radius:50%;border:none;background:#fff3;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);color:#fff;display:flex;flex-direction:column;align-items:center;justify-content:center;cursor:pointer;transition:all .3s ease;position:relative}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:24px;margin-bottom:2px}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   .action-count[_ngcontent-%COMP%], .mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   .action-text[_ngcontent-%COMP%]{font-size:10px;font-weight:600;line-height:1}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:hover{transform:scale(1.1);background:#ffffff4d}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active[_ngcontent-%COMP%]{background:#ffffffe6;color:#ff6b35}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active.like-btn[_ngcontent-%COMP%]{color:#ff3040}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active.like-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_heartBeat .6s ease-in-out}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active.bookmark-btn[_ngcontent-%COMP%]{color:gold}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.like-btn.active[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:#ff3040}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.comment-btn[_ngcontent-%COMP%]:hover, .mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.share-btn[_ngcontent-%COMP%]:hover{background:#ffffff4d}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.music-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,#ff3040,#ff6b35)}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.music-btn[_ngcontent-%COMP%]:hover{transform:scale(1.1) rotate(15deg)}@keyframes _ngcontent-%COMP%_heartBeat{0%{transform:scale(1)}25%{transform:scale(1.3)}50%{transform:scale(1.1)}75%{transform:scale(1.25)}to{transform:scale(1)}}@media (min-width: 769px){.mobile-action-buttons[_ngcontent-%COMP%]{display:none}}.section-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:24px}.section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]{flex:1}.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{font-size:24px;font-weight:700;color:#1a1a1a;margin:0 0 8px;display:flex;align-items:center;gap:12px}.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   .title-icon[_ngcontent-%COMP%]{font-size:28px;color:#ff6b35}.section-header[_ngcontent-%COMP%]   .section-subtitle[_ngcontent-%COMP%]{font-size:14px;color:#666;margin:0}.section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,#ff6b35,#f7931e);color:#fff;border:none;padding:12px 20px;border-radius:25px;font-weight:600;font-size:14px;display:flex;align-items:center;gap:8px;cursor:pointer;transition:all .3s ease;box-shadow:0 4px 15px #ff6b354d}.section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 6px 20px #ff6b3566}.section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:16px}.loading-container[_ngcontent-%COMP%]   .loading-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(280px,1fr));gap:20px}.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]{background:#fff;border-radius:16px;overflow:hidden;box-shadow:0 4px 20px #00000014}.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-image[_ngcontent-%COMP%]{width:100%;height:200px;background:linear-gradient(90deg,#f0f0f0 25%,#e0e0e0,#f0f0f0 75%);background-size:200% 100%;animation:_ngcontent-%COMP%_loading 1.5s infinite}.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]{padding:16px}.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line[_ngcontent-%COMP%]{height:12px;background:linear-gradient(90deg,#f0f0f0 25%,#e0e0e0,#f0f0f0 75%);background-size:200% 100%;animation:_ngcontent-%COMP%_loading 1.5s infinite;border-radius:6px;margin-bottom:8px}.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.short[_ngcontent-%COMP%]{width:40%}.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.medium[_ngcontent-%COMP%]{width:60%}.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.long[_ngcontent-%COMP%]{width:80%}@keyframes _ngcontent-%COMP%_loading{0%{background-position:200% 0}to{background-position:-200% 0}}.error-container[_ngcontent-%COMP%]{text-align:center;padding:40px 20px}.error-container[_ngcontent-%COMP%]   .error-icon[_ngcontent-%COMP%]{font-size:48px;color:#dc3545;margin-bottom:16px}.error-container[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%]{font-size:16px;color:#666;margin-bottom:20px}.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]{background:#007bff;color:#fff;border:none;padding:12px 24px;border-radius:8px;font-weight:600;cursor:pointer;display:flex;align-items:center;gap:8px;margin:0 auto;transition:background .3s ease}.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]:hover{background:#0056b3}.products-slider-container[_ngcontent-%COMP%]{position:relative;padding:0 30px}.products-slider-container[_ngcontent-%COMP%]:hover   .owl-carousel[_ngcontent-%COMP%]   .owl-dots[_ngcontent-%COMP%]   .owl-dot.active[_ngcontent-%COMP%]:after{animation-play-state:paused;border-color:#ff6b3580}.products-slider-container[_ngcontent-%COMP%]     .owl-carousel{position:relative}.products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-stage{transition:transform 1s ease-in-out!important}.products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav{position:absolute;top:50%;transform:translateY(-50%);width:100%;z-index:10}.products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev, .products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next{position:absolute;background:#000000b3!important;color:#fff!important;border:none!important;width:40px!important;height:40px!important;border-radius:50%!important;display:flex!important;align-items:center!important;justify-content:center!important;cursor:pointer!important;transition:all .3s ease!important;font-size:16px!important;outline:none!important}.products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev ion-icon, .products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next ion-icon{font-size:18px}.products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev:hover, .products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next:hover{background:#000000e6!important;transform:scale(1.1)!important;box-shadow:0 4px 15px #0000004d!important}.products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev.disabled, .products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next.disabled{opacity:.3!important;cursor:not-allowed!important}.products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev{left:-30px}.products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next{right:-30px}.products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-dots{text-align:center;margin-top:20px;padding:10px 0}.products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-dots .owl-dot{width:10px!important;height:10px!important;border-radius:50%!important;background:#ff6b354d!important;margin:0 6px!important;cursor:pointer!important;transition:all .4s ease!important;border:2px solid transparent!important;outline:none!important;position:relative!important}.products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-dots .owl-dot.active{background:#ff6b35!important;transform:scale(1.3)!important;border-color:#ff6b354d!important}.products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-dots .owl-dot.active:after{content:"";position:absolute;inset:-3px;border:2px solid #ff6b35;border-radius:50%;animation:_ngcontent-%COMP%_progress-ring 4s linear infinite}.products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-dots .owl-dot:hover:not(.active){background:#ff6b3599!important;transform:scale(1.1)!important}.products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-stage-outer{padding:0;overflow:visible}@keyframes _ngcontent-%COMP%_progress-ring{0%{transform:rotate(0);border-color:#ff6b35 transparent transparent transparent}25%{border-color:#ff6b35 #ff6b35 transparent transparent}50%{border-color:#ff6b35 #ff6b35 #ff6b35 transparent}75%{border-color:#ff6b35 #ff6b35 #ff6b35 #ff6b35}to{transform:rotate(360deg);border-color:#ff6b35 transparent transparent transparent}}.products-slider-container[_ngcontent-%COMP%]{position:relative;margin:0 -20px}.products-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]{position:absolute;top:50%;transform:translateY(-50%);z-index:10;background:#000000b3;color:#fff;border:none;width:40px;height:40px;border-radius:50%;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all .3s ease}.products-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]:hover:not(:disabled){background:#000000e6;transform:translateY(-50%) scale(1.1)}.products-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]:disabled{opacity:.3;cursor:not-allowed}.products-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px}.products-slider-container[_ngcontent-%COMP%]   .slider-nav.prev-btn[_ngcontent-%COMP%]{left:-20px}.products-slider-container[_ngcontent-%COMP%]   .slider-nav.next-btn[_ngcontent-%COMP%]{right:-20px}.products-slider-wrapper[_ngcontent-%COMP%]{overflow:hidden;padding:0 20px}.products-slider[_ngcontent-%COMP%]{display:flex;transition:transform .4s cubic-bezier(.25,.46,.45,.94);gap:20px}.products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]{flex:0 0 260px;width:260px}@media (max-width: 1200px){.products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]{flex:0 0 240px;width:240px}}@media (max-width: 768px){.products-slider-container[_ngcontent-%COMP%]{margin:0 -10px}.products-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]{width:35px;height:35px}.products-slider-container[_ngcontent-%COMP%]   .slider-nav.prev-btn[_ngcontent-%COMP%]{left:-15px}.products-slider-container[_ngcontent-%COMP%]   .slider-nav.next-btn[_ngcontent-%COMP%]{right:-15px}.products-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:16px}.products-slider-wrapper[_ngcontent-%COMP%]{padding:0 10px}.products-slider[_ngcontent-%COMP%]{gap:15px}.products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]{flex:0 0 200px;width:200px}}@media (max-width: 480px){.products-slider[_ngcontent-%COMP%]   .product-card[_ngcontent-%COMP%]{flex:0 0 180px;width:180px}}.product-card[_ngcontent-%COMP%]{background:#fff;border-radius:16px;overflow:hidden;box-shadow:0 4px 20px #00000014;transition:all .3s ease;cursor:pointer;width:100%;height:auto}.product-card[_ngcontent-%COMP%]:hover{transform:translateY(-8px);box-shadow:0 12px 40px #00000026}.product-card[_ngcontent-%COMP%]:hover   .action-buttons[_ngcontent-%COMP%]{opacity:1;transform:translateY(0)}.product-image-container[_ngcontent-%COMP%]{position:relative;overflow:hidden}.product-image-container[_ngcontent-%COMP%]   .product-image[_ngcontent-%COMP%]{width:100%;height:200px;object-fit:cover;transition:transform .3s ease}.product-image-container[_ngcontent-%COMP%]   .trending-badge[_ngcontent-%COMP%]{position:absolute;top:12px;left:12px;background:linear-gradient(135deg,#ff6b35,#f7931e);color:#fff;padding:6px 12px;border-radius:20px;font-size:12px;font-weight:600;display:flex;align-items:center;gap:4px}.product-image-container[_ngcontent-%COMP%]   .trending-badge[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:14px}.product-image-container[_ngcontent-%COMP%]   .discount-badge[_ngcontent-%COMP%]{position:absolute;top:12px;right:12px;background:#dc3545;color:#fff;padding:6px 10px;border-radius:12px;font-size:12px;font-weight:700}.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]{position:absolute;top:50%;right:12px;transform:translateY(-50%);display:flex;flex-direction:column;gap:8px;opacity:0;transition:all .3s ease}.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;border:none;background:#ffffffe6;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all .3s ease}.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:20px;color:#333}.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:hover{background:#fff;transform:scale(1.1)}.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn.like-btn[_ngcontent-%COMP%]:hover   ion-icon[_ngcontent-%COMP%]{color:#dc3545}.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn.like-btn.liked[_ngcontent-%COMP%]{background:#dc35451a}.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn.like-btn.liked[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:#dc3545}.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn.share-btn[_ngcontent-%COMP%]:hover   ion-icon[_ngcontent-%COMP%]{color:#007bff}.product-info[_ngcontent-%COMP%]{padding:16px}.product-info[_ngcontent-%COMP%]   .product-brand[_ngcontent-%COMP%]{font-size:12px;color:#666;text-transform:uppercase;font-weight:600;letter-spacing:.5px;margin-bottom:4px}.product-info[_ngcontent-%COMP%]   .product-name[_ngcontent-%COMP%]{font-size:16px;font-weight:600;color:#1a1a1a;margin:0 0 12px;line-height:1.4;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;overflow:hidden}.product-info[_ngcontent-%COMP%]   .price-section[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;margin-bottom:12px}.product-info[_ngcontent-%COMP%]   .price-section[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%]{font-size:18px;font-weight:700;color:#ff6b35}.product-info[_ngcontent-%COMP%]   .price-section[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%]{font-size:14px;color:#999;text-decoration:line-through}.product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;margin-bottom:16px}.product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]{display:flex;gap:2px}.product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:14px;color:#ddd}.product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   ion-icon.filled[_ngcontent-%COMP%]{color:#ffc107}.product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%]   .rating-text[_ngcontent-%COMP%]{font-size:12px;color:#666}.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]{display:flex;gap:8px}.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .cart-btn[_ngcontent-%COMP%]{flex:1;background:linear-gradient(135deg,#ff6b35,#f7931e);color:#fff;border:none;padding:12px 16px;border-radius:8px;font-weight:600;font-size:14px;display:flex;align-items:center;justify-content:center;gap:8px;cursor:pointer;transition:all .3s ease}.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .cart-btn[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 15px #ff6b354d}.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .cart-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:16px}.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .wishlist-btn[_ngcontent-%COMP%]{width:44px;height:44px;border:2px solid #e9ecef;background:#fff;border-radius:8px;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all .3s ease}.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .wishlist-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:20px;color:#666}.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .wishlist-btn[_ngcontent-%COMP%]:hover{border-color:#ff6b35}.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .wishlist-btn[_ngcontent-%COMP%]:hover   ion-icon[_ngcontent-%COMP%]{color:#ff6b35}.empty-container[_ngcontent-%COMP%]{text-align:center;padding:60px 20px}.empty-container[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%]{font-size:64px;color:#ccc;margin-bottom:20px}.empty-container[_ngcontent-%COMP%]   .empty-title[_ngcontent-%COMP%]{font-size:20px;font-weight:600;color:#333;margin-bottom:8px}.empty-container[_ngcontent-%COMP%]   .empty-message[_ngcontent-%COMP%]{font-size:14px;color:#666}@media (max-width: 768px){.trending-products-container[_ngcontent-%COMP%]{padding:16px}.section-header[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start;gap:16px}.section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]{align-self:flex-end}.products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-stage-outer{padding:0 10px}.products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev, .products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next{width:35px!important;height:35px!important;font-size:16px!important}.products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev{left:-15px}.products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next{right:-15px}.section-title[_ngcontent-%COMP%]{font-size:20px}}@media (max-width: 575.98px){.products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-stage-outer{padding:0 5px}.products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev, .products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next{width:30px!important;height:30px!important;font-size:14px!important}.products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-prev{left:-10px}.products-slider-container[_ngcontent-%COMP%]     .owl-carousel .owl-nav .owl-next{right:-10px}}']})}}return i})();const ln=()=>[1,2,3,4],gn=()=>[1,2,3],pn=()=>[1,2,3,4,5];function mn(i,a){if(1&i){const t=n.RV6();n.j41(0,"div",11)(1,"button",12),n.bIt("click",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.toggleSectionLike())}),n.nrm(2,"ion-icon",13),n.j41(3,"span",14),n.EFF(4),n.k0s()(),n.j41(5,"button",15),n.bIt("click",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.openComments())}),n.nrm(6,"ion-icon",16),n.j41(7,"span",14),n.EFF(8),n.k0s()(),n.j41(9,"button",17),n.bIt("click",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.shareSection())}),n.nrm(10,"ion-icon",18),n.j41(11,"span",19),n.EFF(12,"Share"),n.k0s()(),n.j41(13,"button",20),n.bIt("click",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.toggleSectionBookmark())}),n.nrm(14,"ion-icon",13),n.k0s(),n.j41(15,"button",21),n.bIt("click",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.openMusicPlayer())}),n.nrm(16,"ion-icon",22),n.j41(17,"span",19),n.EFF(18,"Music"),n.k0s()()()}if(2&i){const t=n.XpG();n.R7$(),n.AVh("active",t.isSectionLiked),n.R7$(),n.Y8G("name",t.isSectionLiked?"heart":"heart-outline"),n.R7$(2),n.JRh(t.formatCount(t.sectionLikes)),n.R7$(4),n.JRh(t.formatCount(t.sectionComments)),n.R7$(5),n.AVh("active",t.isSectionBookmarked),n.R7$(),n.Y8G("name",t.isSectionBookmarked?"bookmark":"bookmark-outline")}}function un(i,a){1&i&&n.nrm(0,"div",32)}function fn(i,a){1&i&&(n.j41(0,"div",26)(1,"div",27),n.nrm(2,"div",28)(3,"div",29),n.k0s(),n.j41(4,"div",30),n.DNE(5,un,1,0,"div",31),n.k0s()()),2&i&&(n.R7$(5),n.Y8G("ngForOf",n.lJ4(1,gn)))}function _n(i,a){1&i&&(n.j41(0,"div",23)(1,"div",24),n.DNE(2,fn,6,2,"div",25),n.k0s()()),2&i&&(n.R7$(2),n.Y8G("ngForOf",n.lJ4(1,ln)))}function hn(i,a){if(1&i){const t=n.RV6();n.j41(0,"div",33),n.nrm(1,"ion-icon",34),n.j41(2,"p",35),n.EFF(3),n.k0s(),n.j41(4,"button",36),n.bIt("click",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.onRetry())}),n.nrm(5,"ion-icon",37),n.EFF(6," Try Again "),n.k0s()()}if(2&i){const t=n.XpG();n.R7$(3),n.JRh(t.error)}}function bn(i,a){if(1&i&&(n.j41(0,"span",77),n.EFF(1),n.k0s()),2&i){const t=n.XpG().$implicit,o=n.XpG(3);n.R7$(),n.JRh(o.formatPrice(t.originalPrice))}}function xn(i,a){if(1&i&&n.nrm(0,"ion-icon",13),2&i){const t=a.$implicit,o=n.XpG().$implicit;n.AVh("filled",t<=o.rating.average),n.Y8G("name",t<=o.rating.average?"star":"star-outline")}}function Cn(i,a){if(1&i){const t=n.RV6();n.j41(0,"div",63),n.bIt("click",function(e){const r=n.eBV(t).$implicit,c=n.XpG(3);return n.Njj(c.onProductClick(r,e))}),n.j41(1,"div",64),n.nrm(2,"img",65),n.j41(3,"div",66)(4,"button",12),n.bIt("click",function(e){const r=n.eBV(t).$implicit,c=n.XpG(3);return n.Njj(c.onLikeProduct(r,e))}),n.nrm(5,"ion-icon",13),n.k0s(),n.j41(6,"button",17),n.bIt("click",function(e){const r=n.eBV(t).$implicit,c=n.XpG(3);return n.Njj(c.onShareProduct(r,e))}),n.nrm(7,"ion-icon",67),n.k0s()()(),n.j41(8,"div",68)(9,"h5",69),n.EFF(10),n.k0s(),n.j41(11,"div",70)(12,"span",71),n.EFF(13),n.k0s(),n.DNE(14,bn,2,1,"span",72),n.k0s(),n.j41(15,"div",73)(16,"div",74),n.DNE(17,xn,1,3,"ion-icon",75),n.k0s(),n.j41(18,"span",76),n.EFF(19),n.k0s()()()()}if(2&i){const t=a.$implicit,o=n.XpG(3);n.R7$(2),n.Y8G("src",t.images[0].url,n.B4B)("alt",t.images[0].alt||t.name),n.R7$(2),n.AVh("liked",o.isProductLiked(t._id)),n.BMQ("aria-label","Like "+t.name),n.R7$(),n.Y8G("name",o.isProductLiked(t._id)?"heart":"heart-outline"),n.R7$(),n.BMQ("aria-label","Share "+t.name),n.R7$(4),n.JRh(t.name),n.R7$(3),n.JRh(o.formatPrice(t.price)),n.R7$(),n.Y8G("ngIf",t.originalPrice&&t.originalPrice>t.price),n.R7$(3),n.Y8G("ngForOf",n.lJ4(12,pn)),n.R7$(2),n.SpI("(",t.rating.count,")")}}function Pn(i,a){if(1&i){const t=n.RV6();n.j41(0,"div",46),n.bIt("click",function(){const e=n.eBV(t).$implicit,r=n.XpG(2);return n.Njj(r.onBrandClick(e))}),n.j41(1,"div",47)(2,"div",48)(3,"h3",49),n.EFF(4),n.k0s(),n.j41(5,"div",50)(6,"div",51),n.nrm(7,"ion-icon",52),n.j41(8,"span"),n.EFF(9),n.k0s()(),n.j41(10,"div",51),n.nrm(11,"ion-icon",53),n.j41(12,"span"),n.EFF(13),n.k0s()(),n.j41(14,"div",51),n.nrm(15,"ion-icon",54),n.j41(16,"span"),n.EFF(17),n.k0s()()()(),n.j41(18,"div",55),n.nrm(19,"ion-icon",56),n.EFF(20," Featured "),n.k0s()(),n.j41(21,"div",57)(22,"h4",58),n.EFF(23,"Top Products"),n.k0s(),n.j41(24,"div",59),n.DNE(25,Cn,20,13,"div",60),n.k0s()(),n.j41(26,"div",61)(27,"button",62)(28,"span"),n.EFF(29),n.k0s(),n.nrm(30,"ion-icon",42),n.k0s()()()}if(2&i){const t=a.$implicit,o=n.XpG(2);n.R7$(4),n.JRh(t.brand),n.R7$(5),n.SpI("",t.productCount," Products"),n.R7$(4),n.SpI("",t.avgRating,"/5"),n.R7$(4),n.SpI("",o.formatNumber(t.totalViews)," Views"),n.R7$(8),n.Y8G("ngForOf",t.topProducts)("ngForTrackBy",o.trackByProductId),n.R7$(4),n.SpI("View All ",t.brand," Products")}}function On(i,a){if(1&i){const t=n.RV6();n.j41(0,"div",38)(1,"button",39),n.bIt("click",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.slidePrev())}),n.nrm(2,"ion-icon",40),n.k0s(),n.j41(3,"button",41),n.bIt("click",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.slideNext())}),n.nrm(4,"ion-icon",42),n.k0s(),n.j41(5,"div",43),n.bIt("mouseenter",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.pauseAutoSlide())})("mouseleave",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.resumeAutoSlide())}),n.j41(6,"div",44),n.DNE(7,Pn,31,7,"div",45),n.k0s()()()}if(2&i){const t=n.XpG();n.R7$(),n.Y8G("disabled",0===t.currentSlide),n.R7$(2),n.Y8G("disabled",t.currentSlide>=t.maxSlide),n.R7$(3),n.xc7("transform","translateX("+t.slideOffset+"px)"),n.R7$(),n.Y8G("ngForOf",t.featuredBrands)("ngForTrackBy",t.trackByBrandName)}}function Mn(i,a){1&i&&(n.j41(0,"div",78),n.nrm(1,"ion-icon",79),n.j41(2,"h3",80),n.EFF(3,"No Featured Brands"),n.k0s(),n.j41(4,"p",81),n.EFF(5,"Check back later for featured brand collections"),n.k0s()())}let k=(()=>{class i{constructor(t,o,e){this.trendingService=t,this.socialService=o,this.router=e,this.featuredBrands=[],this.isLoading=!0,this.error=null,this.likedProducts=new Set,this.subscription=new f.yU,this.currentSlide=0,this.slideOffset=0,this.cardWidth=320,this.visibleCards=3,this.maxSlide=0,this.autoSlideDelay=4e3,this.isAutoSliding=!0,this.isPaused=!1,this.isSectionLiked=!1,this.isSectionBookmarked=!1,this.sectionLikes=287,this.sectionComments=89,this.isMobile=!1}ngOnInit(){this.loadFeaturedBrands(),this.subscribeFeaturedBrands(),this.subscribeLikedProducts(),this.updateResponsiveSettings(),this.setupResizeListener(),this.checkMobileDevice()}ngOnDestroy(){this.subscription.unsubscribe(),this.stopAutoSlide()}subscribeFeaturedBrands(){this.subscription.add(this.trendingService.featuredBrands$.subscribe(t=>{this.featuredBrands=t,this.isLoading=!1,this.updateSliderOnBrandsLoad()}))}subscribeLikedProducts(){this.subscription.add(this.socialService.likedProducts$.subscribe(t=>{this.likedProducts=t}))}loadFeaturedBrands(){var t=this;return(0,s.A)(function*(){try{t.isLoading=!0,t.error=null,yield t.trendingService.loadFeaturedBrands()}catch(o){console.error("Error loading featured brands:",o),t.error="Failed to load featured brands",t.isLoading=!1}})()}onBrandClick(t){this.router.navigate(["/products"],{queryParams:{brand:t.brand}})}onProductClick(t,o){o.stopPropagation(),this.router.navigate(["/product",t._id])}onLikeProduct(t,o){var e=this;return(0,s.A)(function*(){o.stopPropagation();try{const r=yield e.socialService.likeProduct(t._id);r.success?console.log(r.message):console.error("Failed to like product:",r.message)}catch(r){console.error("Error liking product:",r)}})()}onShareProduct(t,o){var e=this;return(0,s.A)(function*(){o.stopPropagation();try{const r=`${window.location.origin}/product/${t._id}`;yield navigator.clipboard.writeText(r),yield e.socialService.shareProduct(t._id,{platform:"copy_link",message:`Check out this amazing ${t.name} from ${t.brand}!`}),console.log("Product link copied to clipboard!")}catch(r){console.error("Error sharing product:",r)}})()}formatPrice(t){return new Intl.NumberFormat("en-IN",{style:"currency",currency:"INR",minimumFractionDigits:0}).format(t)}formatNumber(t){return t>=1e6?(t/1e6).toFixed(1)+"M":t>=1e3?(t/1e3).toFixed(1)+"K":t.toString()}onRetry(){this.loadFeaturedBrands()}trackByBrandName(t,o){return o.brand}isProductLiked(t){return this.likedProducts.has(t)}trackByProductId(t,o){return o._id}startAutoSlide(){!this.isAutoSliding||this.isPaused||(this.stopAutoSlide(),this.autoSlideInterval=setInterval(()=>{!this.isPaused&&this.featuredBrands.length>this.visibleCards&&this.autoSlideNext()},this.autoSlideDelay))}stopAutoSlide(){this.autoSlideInterval&&(clearInterval(this.autoSlideInterval),this.autoSlideInterval=null)}autoSlideNext(){this.currentSlide>=this.maxSlide?this.currentSlide=0:this.currentSlide++,this.updateSlideOffset()}pauseAutoSlide(){this.isPaused=!0,this.stopAutoSlide()}resumeAutoSlide(){this.isPaused=!1,this.startAutoSlide()}updateResponsiveSettings(){const t=window.innerWidth;t<=768?(this.cardWidth=280,this.visibleCards=1):t<=1200?(this.cardWidth=320,this.visibleCards=2):(this.cardWidth=340,this.visibleCards=3),this.updateSliderLimits(),this.updateSlideOffset()}setupResizeListener(){window.addEventListener("resize",()=>{this.updateResponsiveSettings()})}updateSliderLimits(){this.maxSlide=Math.max(0,this.featuredBrands.length-this.visibleCards)}slidePrev(){this.currentSlide>0&&(this.currentSlide--,this.updateSlideOffset(),this.restartAutoSlideAfterInteraction())}slideNext(){this.currentSlide<this.maxSlide&&(this.currentSlide++,this.updateSlideOffset(),this.restartAutoSlideAfterInteraction())}updateSlideOffset(){this.slideOffset=-this.currentSlide*this.cardWidth}restartAutoSlideAfterInteraction(){this.stopAutoSlide(),setTimeout(()=>{this.startAutoSlide()},2e3)}updateSliderOnBrandsLoad(){setTimeout(()=>{this.updateSliderLimits(),this.currentSlide=0,this.slideOffset=0,this.startAutoSlide()},100)}toggleSectionLike(){this.isSectionLiked=!this.isSectionLiked,this.isSectionLiked?this.sectionLikes++:this.sectionLikes--}toggleSectionBookmark(){this.isSectionBookmarked=!this.isSectionBookmarked}openComments(){console.log("Opening comments for featured brands section")}shareSection(){navigator.share?navigator.share({title:"Featured Brands",text:"Check out these amazing featured fashion brands!",url:window.location.href}):(navigator.clipboard.writeText(window.location.href),console.log("Link copied to clipboard"))}openMusicPlayer(){console.log("Opening music player for featured brands")}formatCount(t){return t>=1e6?(t/1e6).toFixed(1)+"M":t>=1e3?(t/1e3).toFixed(1)+"K":t.toString()}checkMobileDevice(){this.isMobile=window.innerWidth<=768}static{this.\u0275fac=function(o){return new(o||i)(n.rXU(C),n.rXU(P),n.rXU(m.Ix))}}static{this.\u0275cmp=n.VBU({type:i,selectors:[["app-featured-brands"]],standalone:!0,features:[n.aNF],decls:13,vars:5,consts:[[1,"featured-brands-container"],["class","mobile-action-buttons",4,"ngIf"],[1,"section-header"],[1,"header-content"],[1,"section-title"],["name","diamond",1,"title-icon"],[1,"section-subtitle"],["class","loading-container",4,"ngIf"],["class","error-container",4,"ngIf"],["class","brands-slider-container",4,"ngIf"],["class","empty-container",4,"ngIf"],[1,"mobile-action-buttons"],[1,"action-btn","like-btn",3,"click"],[3,"name"],[1,"action-count"],[1,"action-btn","comment-btn",3,"click"],["name","chatbubble-outline"],[1,"action-btn","share-btn",3,"click"],["name","arrow-redo-outline"],[1,"action-text"],[1,"action-btn","bookmark-btn",3,"click"],[1,"action-btn","music-btn",3,"click"],["name","musical-notes"],[1,"loading-container"],[1,"loading-grid"],["class","loading-brand-card",4,"ngFor","ngForOf"],[1,"loading-brand-card"],[1,"loading-header"],[1,"loading-brand-name"],[1,"loading-stats"],[1,"loading-products"],["class","loading-product",4,"ngFor","ngForOf"],[1,"loading-product"],[1,"error-container"],["name","alert-circle",1,"error-icon"],[1,"error-message"],[1,"retry-btn",3,"click"],["name","refresh"],[1,"brands-slider-container"],[1,"slider-nav","prev-btn",3,"click","disabled"],["name","chevron-back"],[1,"slider-nav","next-btn",3,"click","disabled"],["name","chevron-forward"],[1,"brands-slider-wrapper",3,"mouseenter","mouseleave"],[1,"brands-slider"],["class","brand-card",3,"click",4,"ngFor","ngForOf","ngForTrackBy"],[1,"brand-card",3,"click"],[1,"brand-header"],[1,"brand-info"],[1,"brand-name"],[1,"brand-stats"],[1,"stat-item"],["name","bag-outline"],["name","star"],["name","eye-outline"],[1,"brand-badge"],["name","diamond"],[1,"top-products"],[1,"products-title"],[1,"products-list"],["class","product-item",3,"click",4,"ngFor","ngForOf","ngForTrackBy"],[1,"view-more-section"],[1,"view-more-btn"],[1,"product-item",3,"click"],[1,"product-image-container"],["loading","lazy",1,"product-image",3,"src","alt"],[1,"product-actions"],["name","share-outline"],[1,"product-details"],[1,"product-name"],[1,"product-price"],[1,"current-price"],["class","original-price",4,"ngIf"],[1,"product-rating"],[1,"stars"],[3,"name","filled",4,"ngFor","ngForOf"],[1,"rating-count"],[1,"original-price"],[1,"empty-container"],["name","diamond-outline",1,"empty-icon"],[1,"empty-title"],[1,"empty-message"]],template:function(o,e){1&o&&(n.j41(0,"div",0),n.DNE(1,mn,19,8,"div",1),n.j41(2,"div",2)(3,"div",3)(4,"h2",4),n.nrm(5,"ion-icon",5),n.EFF(6," Featured Brands "),n.k0s(),n.j41(7,"p",6),n.EFF(8,"Top brands with amazing collections"),n.k0s()()(),n.DNE(9,_n,3,2,"div",7)(10,hn,7,1,"div",8)(11,On,8,6,"div",9)(12,Mn,6,0,"div",10),n.k0s()),2&o&&(n.R7$(),n.Y8G("ngIf",e.isMobile),n.R7$(8),n.Y8G("ngIf",e.isLoading),n.R7$(),n.Y8G("ngIf",e.error&&!e.isLoading),n.R7$(),n.Y8G("ngIf",!e.isLoading&&!e.error&&e.featuredBrands.length>0),n.R7$(),n.Y8G("ngIf",!e.isLoading&&!e.error&&0===e.featuredBrands.length))},dependencies:[d.MD,d.Sq,d.bT,g.bv,g.iq,_.Rl],styles:[".featured-brands-container[_ngcontent-%COMP%]{padding:20px;background:linear-gradient(135deg,#667eea,#764ba2);border-radius:16px;margin-bottom:24px;color:#fff;position:relative}.mobile-action-buttons[_ngcontent-%COMP%]{position:absolute;right:15px;top:50%;transform:translateY(-50%);display:flex;flex-direction:column;gap:20px;z-index:10}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]{width:48px;height:48px;border-radius:50%;border:none;background:#fff3;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);color:#fff;display:flex;flex-direction:column;align-items:center;justify-content:center;cursor:pointer;transition:all .3s ease;position:relative}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:24px;margin-bottom:2px}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   .action-count[_ngcontent-%COMP%], .mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   .action-text[_ngcontent-%COMP%]{font-size:10px;font-weight:600;line-height:1}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:hover{transform:scale(1.1);background:#ffffff4d}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active[_ngcontent-%COMP%]{background:#ffffffe6;color:#667eea}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active.like-btn[_ngcontent-%COMP%]{color:#ff3040}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active.like-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_heartBeat .6s ease-in-out}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active.bookmark-btn[_ngcontent-%COMP%]{color:gold}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.like-btn.active[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:#ff3040}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.music-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,#ff3040,#667eea)}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.music-btn[_ngcontent-%COMP%]:hover{transform:scale(1.1) rotate(15deg)}@keyframes _ngcontent-%COMP%_heartBeat{0%{transform:scale(1)}25%{transform:scale(1.3)}50%{transform:scale(1.1)}75%{transform:scale(1.25)}to{transform:scale(1)}}@media (min-width: 769px){.mobile-action-buttons[_ngcontent-%COMP%]{display:none}}.section-header[_ngcontent-%COMP%]{margin-bottom:24px}.section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]{text-align:center}.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{font-size:24px;font-weight:700;color:#fff;margin:0 0 8px;display:flex;align-items:center;justify-content:center;gap:12px}.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   .title-icon[_ngcontent-%COMP%]{font-size:28px;color:gold}.section-header[_ngcontent-%COMP%]   .section-subtitle[_ngcontent-%COMP%]{font-size:14px;color:#fffc;margin:0}.loading-container[_ngcontent-%COMP%]   .loading-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(350px,1fr));gap:20px}.loading-container[_ngcontent-%COMP%]   .loading-brand-card[_ngcontent-%COMP%]{background:#ffffff1a;border-radius:16px;padding:20px;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.loading-container[_ngcontent-%COMP%]   .loading-brand-card[_ngcontent-%COMP%]   .loading-header[_ngcontent-%COMP%]{margin-bottom:16px}.loading-container[_ngcontent-%COMP%]   .loading-brand-card[_ngcontent-%COMP%]   .loading-header[_ngcontent-%COMP%]   .loading-brand-name[_ngcontent-%COMP%]{height:24px;background:#fff3;border-radius:8px;margin-bottom:8px;animation:_ngcontent-%COMP%_loading 1.5s infinite}.loading-container[_ngcontent-%COMP%]   .loading-brand-card[_ngcontent-%COMP%]   .loading-header[_ngcontent-%COMP%]   .loading-stats[_ngcontent-%COMP%]{height:16px;background:#fff3;border-radius:8px;width:70%;animation:_ngcontent-%COMP%_loading 1.5s infinite}.loading-container[_ngcontent-%COMP%]   .loading-brand-card[_ngcontent-%COMP%]   .loading-products[_ngcontent-%COMP%]{display:flex;gap:12px}.loading-container[_ngcontent-%COMP%]   .loading-brand-card[_ngcontent-%COMP%]   .loading-products[_ngcontent-%COMP%]   .loading-product[_ngcontent-%COMP%]{flex:1;height:120px;background:#fff3;border-radius:12px;animation:_ngcontent-%COMP%_loading 1.5s infinite}@keyframes _ngcontent-%COMP%_loading{0%,to{opacity:.6}50%{opacity:1}}.error-container[_ngcontent-%COMP%]{text-align:center;padding:40px 20px}.error-container[_ngcontent-%COMP%]   .error-icon[_ngcontent-%COMP%]{font-size:48px;color:#ff6b6b;margin-bottom:16px}.error-container[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%]{font-size:16px;color:#fffc;margin-bottom:20px}.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]{background:#fff3;color:#fff;border:2px solid rgba(255,255,255,.3);padding:12px 24px;border-radius:8px;font-weight:600;cursor:pointer;display:flex;align-items:center;gap:8px;margin:0 auto;transition:all .3s ease;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]:hover{background:#ffffff4d;border-color:#ffffff80}.brands-slider-container[_ngcontent-%COMP%]{position:relative;margin:0 -20px}.brands-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]{position:absolute;top:50%;transform:translateY(-50%);z-index:10;background:#000000b3;color:#fff;border:none;width:40px;height:40px;border-radius:50%;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all .3s ease}.brands-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]:hover:not(:disabled){background:#000000e6;transform:translateY(-50%) scale(1.1)}.brands-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]:disabled{opacity:.3;cursor:not-allowed}.brands-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px}.brands-slider-container[_ngcontent-%COMP%]   .slider-nav.prev-btn[_ngcontent-%COMP%]{left:-20px}.brands-slider-container[_ngcontent-%COMP%]   .slider-nav.next-btn[_ngcontent-%COMP%]{right:-20px}.brands-slider-wrapper[_ngcontent-%COMP%]{overflow:hidden;padding:0 20px}.brands-slider[_ngcontent-%COMP%]{display:flex;transition:transform .4s cubic-bezier(.25,.46,.45,.94);gap:20px}.brands-slider[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]{flex:0 0 300px;width:300px}@media (max-width: 1200px){.brands-slider[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]{flex:0 0 280px;width:280px}}@media (max-width: 768px){.brands-slider-container[_ngcontent-%COMP%]{margin:0 -10px}.brands-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]{width:35px;height:35px}.brands-slider-container[_ngcontent-%COMP%]   .slider-nav.prev-btn[_ngcontent-%COMP%]{left:-15px}.brands-slider-container[_ngcontent-%COMP%]   .slider-nav.next-btn[_ngcontent-%COMP%]{right:-15px}.brands-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:16px}.brands-slider-wrapper[_ngcontent-%COMP%]{padding:0 10px}.brands-slider[_ngcontent-%COMP%]{gap:15px}.brands-slider[_ngcontent-%COMP%]   .brand-card[_ngcontent-%COMP%]{flex:0 0 260px;width:260px}}.brands-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(350px,1fr));gap:20px}.brand-card[_ngcontent-%COMP%]{background:#ffffff1a;border-radius:16px;padding:20px;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border:1px solid rgba(255,255,255,.2);transition:all .3s ease;cursor:pointer}.brand-card[_ngcontent-%COMP%]:hover{transform:translateY(-8px);background:#ffffff26;box-shadow:0 12px 40px #0003}.brand-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:20px}.brand-header[_ngcontent-%COMP%]   .brand-info[_ngcontent-%COMP%]{flex:1}.brand-header[_ngcontent-%COMP%]   .brand-name[_ngcontent-%COMP%]{font-size:20px;font-weight:700;color:#fff;margin:0 0 12px}.brand-header[_ngcontent-%COMP%]   .brand-stats[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:6px}.brand-header[_ngcontent-%COMP%]   .brand-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-size:12px;color:#fffc}.brand-header[_ngcontent-%COMP%]   .brand-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:14px;color:gold}.brand-header[_ngcontent-%COMP%]   .brand-badge[_ngcontent-%COMP%]{background:linear-gradient(135deg,gold,#ffed4e);color:#333;padding:8px 12px;border-radius:20px;font-size:12px;font-weight:600;display:flex;align-items:center;gap:6px}.brand-header[_ngcontent-%COMP%]   .brand-badge[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:14px}.top-products[_ngcontent-%COMP%]{margin-bottom:20px}.top-products[_ngcontent-%COMP%]   .products-title[_ngcontent-%COMP%]{font-size:16px;font-weight:600;color:#fff;margin:0 0 16px}.top-products[_ngcontent-%COMP%]   .products-list[_ngcontent-%COMP%]{display:flex;gap:12px;overflow-x:auto;padding-bottom:8px}.top-products[_ngcontent-%COMP%]   .products-list[_ngcontent-%COMP%]::-webkit-scrollbar{height:4px}.top-products[_ngcontent-%COMP%]   .products-list[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:#ffffff1a;border-radius:2px}.top-products[_ngcontent-%COMP%]   .products-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#ffffff4d;border-radius:2px}.product-item[_ngcontent-%COMP%]{flex:0 0 140px;background:#ffffff1a;border-radius:12px;overflow:hidden;transition:all .3s ease;cursor:pointer}.product-item[_ngcontent-%COMP%]:hover{transform:translateY(-4px);background:#ffffff26}.product-item[_ngcontent-%COMP%]:hover   .product-actions[_ngcontent-%COMP%]{opacity:1}.product-image-container[_ngcontent-%COMP%]{position:relative}.product-image-container[_ngcontent-%COMP%]   .product-image[_ngcontent-%COMP%]{width:100%;height:100px;object-fit:cover}.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]{position:absolute;top:8px;right:8px;display:flex;flex-direction:column;gap:4px;opacity:0;transition:opacity .3s ease}.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]{width:28px;height:28px;border-radius:50%;border:none;background:#ffffffe6;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all .3s ease}.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:14px;color:#333}.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:hover{background:#fff;transform:scale(1.1)}.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn.like-btn[_ngcontent-%COMP%]:hover   ion-icon[_ngcontent-%COMP%]{color:#dc3545}.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn.like-btn.liked[_ngcontent-%COMP%]{background:#dc354533}.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn.like-btn.liked[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:#dc3545}.product-image-container[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .action-btn.share-btn[_ngcontent-%COMP%]:hover   ion-icon[_ngcontent-%COMP%]{color:#007bff}.product-details[_ngcontent-%COMP%]{padding:12px}.product-details[_ngcontent-%COMP%]   .product-name[_ngcontent-%COMP%]{font-size:12px;font-weight:600;color:#fff;margin:0 0 8px;line-height:1.3;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;overflow:hidden}.product-details[_ngcontent-%COMP%]   .product-price[_ngcontent-%COMP%]{display:flex;align-items:center;gap:6px;margin-bottom:8px}.product-details[_ngcontent-%COMP%]   .product-price[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%]{font-size:14px;font-weight:700;color:gold}.product-details[_ngcontent-%COMP%]   .product-price[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%]{font-size:10px;color:#fff9;text-decoration:line-through}.product-details[_ngcontent-%COMP%]   .product-rating[_ngcontent-%COMP%]{display:flex;align-items:center;gap:6px}.product-details[_ngcontent-%COMP%]   .product-rating[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]{display:flex;gap:1px}.product-details[_ngcontent-%COMP%]   .product-rating[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:10px;color:#ffffff4d}.product-details[_ngcontent-%COMP%]   .product-rating[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   ion-icon.filled[_ngcontent-%COMP%]{color:gold}.product-details[_ngcontent-%COMP%]   .product-rating[_ngcontent-%COMP%]   .rating-count[_ngcontent-%COMP%]{font-size:10px;color:#fff9}.view-more-section[_ngcontent-%COMP%]   .view-more-btn[_ngcontent-%COMP%]{width:100%;background:#ffffff1a;border:2px solid rgba(255,255,255,.2);color:#fff;padding:12px 16px;border-radius:8px;font-weight:600;font-size:14px;display:flex;align-items:center;justify-content:center;gap:8px;cursor:pointer;transition:all .3s ease}.view-more-section[_ngcontent-%COMP%]   .view-more-btn[_ngcontent-%COMP%]:hover{background:#fff3;border-color:#fff6}.view-more-section[_ngcontent-%COMP%]   .view-more-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:16px}.empty-container[_ngcontent-%COMP%]{text-align:center;padding:60px 20px}.empty-container[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%]{font-size:64px;color:#fff6;margin-bottom:20px}.empty-container[_ngcontent-%COMP%]   .empty-title[_ngcontent-%COMP%]{font-size:20px;font-weight:600;color:#fff;margin-bottom:8px}.empty-container[_ngcontent-%COMP%]   .empty-message[_ngcontent-%COMP%]{font-size:14px;color:#ffffffb3}@media (max-width: 768px){.featured-brands-container[_ngcontent-%COMP%]{padding:16px}.brands-grid[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:16px}.brand-header[_ngcontent-%COMP%]{flex-direction:column;gap:12px}.brand-header[_ngcontent-%COMP%]   .brand-badge[_ngcontent-%COMP%]{align-self:flex-start}.brand-stats[_ngcontent-%COMP%]{flex-direction:row!important;flex-wrap:wrap;gap:12px!important}.section-title[_ngcontent-%COMP%]{font-size:20px}}"]})}}return i})();const vn=()=>[1,2,3,4,5,6],kn=()=>[1,2,3,4,5];function wn(i,a){if(1&i){const t=n.RV6();n.j41(0,"div",12)(1,"button",13),n.bIt("click",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.toggleSectionLike())}),n.nrm(2,"ion-icon",14),n.j41(3,"span",15),n.EFF(4),n.k0s()(),n.j41(5,"button",16),n.bIt("click",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.openComments())}),n.nrm(6,"ion-icon",17),n.j41(7,"span",15),n.EFF(8),n.k0s()(),n.j41(9,"button",18),n.bIt("click",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.shareSection())}),n.nrm(10,"ion-icon",19),n.j41(11,"span",20),n.EFF(12,"Share"),n.k0s()(),n.j41(13,"button",21),n.bIt("click",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.toggleSectionBookmark())}),n.nrm(14,"ion-icon",14),n.k0s(),n.j41(15,"button",22),n.bIt("click",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.openMusicPlayer())}),n.nrm(16,"ion-icon",23),n.j41(17,"span",20),n.EFF(18,"Music"),n.k0s()()()}if(2&i){const t=n.XpG();n.R7$(),n.AVh("active",t.isSectionLiked),n.R7$(),n.Y8G("name",t.isSectionLiked?"heart":"heart-outline"),n.R7$(2),n.JRh(t.formatCount(t.sectionLikes)),n.R7$(4),n.JRh(t.formatCount(t.sectionComments)),n.R7$(5),n.AVh("active",t.isSectionBookmarked),n.R7$(),n.Y8G("name",t.isSectionBookmarked?"bookmark":"bookmark-outline")}}function yn(i,a){1&i&&(n.j41(0,"div",27),n.nrm(1,"div",28),n.j41(2,"div",29),n.nrm(3,"div",30)(4,"div",31)(5,"div",32),n.k0s()())}function Sn(i,a){1&i&&(n.j41(0,"div",24)(1,"div",25),n.DNE(2,yn,6,0,"div",26),n.k0s()()),2&i&&(n.R7$(2),n.Y8G("ngForOf",n.lJ4(1,vn)))}function Fn(i,a){if(1&i){const t=n.RV6();n.j41(0,"div",33),n.nrm(1,"ion-icon",34),n.j41(2,"p",35),n.EFF(3),n.k0s(),n.j41(4,"button",36),n.bIt("click",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.onRetry())}),n.nrm(5,"ion-icon",37),n.EFF(6," Try Again "),n.k0s()()}if(2&i){const t=n.XpG();n.R7$(3),n.JRh(t.error)}}function jn(i,a){if(1&i&&(n.j41(0,"div",70),n.EFF(1),n.k0s()),2&i){const t=n.XpG().$implicit,o=n.XpG(2);n.R7$(),n.SpI(" ",o.getDiscountPercentage(t),"% OFF ")}}function In(i,a){if(1&i&&(n.j41(0,"span",71),n.EFF(1),n.k0s()),2&i){const t=n.XpG().$implicit,o=n.XpG(2);n.R7$(),n.JRh(o.formatPrice(t.originalPrice))}}function Tn(i,a){if(1&i&&n.nrm(0,"ion-icon",14),2&i){const t=a.$implicit,o=n.XpG().$implicit;n.AVh("filled",t<=o.rating.average),n.Y8G("name",t<=o.rating.average?"star":"star-outline")}}function Rn(i,a){if(1&i){const t=n.RV6();n.j41(0,"div",46),n.bIt("click",function(){const e=n.eBV(t).$implicit,r=n.XpG(2);return n.Njj(r.onProductClick(e))}),n.j41(1,"div",47),n.nrm(2,"img",48),n.j41(3,"div",49),n.nrm(4,"ion-icon",50),n.EFF(5," New "),n.k0s(),n.j41(6,"div",51),n.EFF(7),n.k0s(),n.DNE(8,jn,2,1,"div",52),n.j41(9,"div",53)(10,"button",13),n.bIt("click",function(e){const r=n.eBV(t).$implicit,c=n.XpG(2);return n.Njj(c.onLikeProduct(r,e))}),n.nrm(11,"ion-icon",14),n.k0s(),n.j41(12,"button",18),n.bIt("click",function(e){const r=n.eBV(t).$implicit,c=n.XpG(2);return n.Njj(c.onShareProduct(r,e))}),n.nrm(13,"ion-icon",54),n.k0s()()(),n.j41(14,"div",55)(15,"div",56),n.EFF(16),n.k0s(),n.j41(17,"h3",57),n.EFF(18),n.k0s(),n.j41(19,"div",58)(20,"span",59),n.EFF(21),n.k0s(),n.DNE(22,In,2,1,"span",60),n.k0s(),n.j41(23,"div",61)(24,"div",62),n.DNE(25,Tn,1,3,"ion-icon",63),n.k0s(),n.j41(26,"span",64),n.EFF(27),n.k0s()(),n.j41(28,"div",65)(29,"button",66),n.bIt("click",function(e){const r=n.eBV(t).$implicit,c=n.XpG(2);return n.Njj(c.onAddToCart(r,e))}),n.nrm(30,"ion-icon",67),n.EFF(31," Add to Cart "),n.k0s(),n.j41(32,"button",68),n.bIt("click",function(e){const r=n.eBV(t).$implicit,c=n.XpG(2);return n.Njj(c.onAddToWishlist(r,e))}),n.nrm(33,"ion-icon",69),n.k0s()()()()}if(2&i){const t=a.$implicit,o=n.XpG(2);n.xc7("width",o.cardWidth,"px"),n.R7$(2),n.Y8G("src",t.images[0].url,n.B4B)("alt",t.images[0].alt||t.name),n.R7$(5),n.SpI(" ",o.getDaysAgo(t.createdAt)," days ago "),n.R7$(),n.Y8G("ngIf",o.getDiscountPercentage(t)>0),n.R7$(2),n.AVh("liked",o.isProductLiked(t._id)),n.BMQ("aria-label","Like "+t.name),n.R7$(),n.Y8G("name",o.isProductLiked(t._id)?"heart":"heart-outline"),n.R7$(),n.BMQ("aria-label","Share "+t.name),n.R7$(4),n.JRh(t.brand),n.R7$(2),n.JRh(t.name),n.R7$(3),n.JRh(o.formatPrice(t.price)),n.R7$(),n.Y8G("ngIf",t.originalPrice&&t.originalPrice>t.price),n.R7$(3),n.Y8G("ngForOf",n.lJ4(17,kn)),n.R7$(2),n.SpI("(",t.rating.count,")")}}function zn(i,a){1&i&&(n.j41(0,"div",72),n.nrm(1,"ion-icon",73),n.j41(2,"h3",74),n.EFF(3,"No New Arrivals"),n.k0s(),n.j41(4,"p",75),n.EFF(5,"Check back soon for fresh new styles"),n.k0s()())}function $n(i,a){if(1&i){const t=n.RV6();n.j41(0,"div",38),n.bIt("mouseenter",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.pauseAutoSlide())})("mouseleave",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.resumeAutoSlide())}),n.j41(1,"button",39),n.bIt("click",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.prevSlide())}),n.nrm(2,"ion-icon",40),n.k0s(),n.j41(3,"button",41),n.bIt("click",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.nextSlide())}),n.nrm(4,"ion-icon",8),n.k0s(),n.j41(5,"div",42)(6,"div",43),n.DNE(7,Rn,34,18,"div",44),n.k0s(),n.DNE(8,zn,6,0,"div",45),n.k0s()()}if(2&i){const t=n.XpG();n.R7$(),n.Y8G("disabled",!t.canGoPrev),n.BMQ("aria-label","Previous products"),n.R7$(2),n.Y8G("disabled",!t.canGoNext),n.BMQ("aria-label","Next products"),n.R7$(3),n.xc7("transform","translateX(-"+t.slideOffset+"px)"),n.R7$(),n.Y8G("ngForOf",t.newArrivals)("ngForTrackBy",t.trackByProductId),n.R7$(),n.Y8G("ngIf",!t.isLoading&&!t.error&&0===t.newArrivals.length)}}let w=(()=>{class i{constructor(t,o,e,r,c){this.trendingService=t,this.socialService=o,this.cartService=e,this.wishlistService=r,this.router=c,this.newArrivals=[],this.isLoading=!0,this.error=null,this.likedProducts=new Set,this.subscription=new f.yU,this.currentSlide=0,this.slideOffset=0,this.cardWidth=280,this.visibleCards=4,this.maxSlide=0,this.autoSlideDelay=3500,this.isSectionLiked=!1,this.isSectionBookmarked=!1,this.sectionLikes=421,this.sectionComments=156,this.isMobile=!1}ngOnInit(){this.loadNewArrivals(),this.subscribeNewArrivals(),this.subscribeLikedProducts(),this.initializeSlider(),this.startAutoSlide(),this.checkMobileDevice()}ngOnDestroy(){this.subscription.unsubscribe(),this.stopAutoSlide()}subscribeNewArrivals(){this.subscription.add(this.trendingService.newArrivals$.subscribe(t=>{this.newArrivals=t,this.isLoading=!1,this.calculateMaxSlide(),this.currentSlide=0,this.updateSlidePosition()}))}subscribeLikedProducts(){this.subscription.add(this.socialService.likedProducts$.subscribe(t=>{this.likedProducts=t}))}loadNewArrivals(){var t=this;return(0,s.A)(function*(){try{t.isLoading=!0,t.error=null,yield t.trendingService.loadNewArrivals(1,6)}catch(o){console.error("Error loading new arrivals:",o),t.error="Failed to load new arrivals",t.isLoading=!1}})()}onProductClick(t){this.router.navigate(["/product",t._id])}onLikeProduct(t,o){var e=this;return(0,s.A)(function*(){o.stopPropagation();try{const r=yield e.socialService.likeProduct(t._id);r.success?console.log(r.message):console.error("Failed to like product:",r.message)}catch(r){console.error("Error liking product:",r)}})()}onShareProduct(t,o){var e=this;return(0,s.A)(function*(){o.stopPropagation();try{const r=`${window.location.origin}/product/${t._id}`;yield navigator.clipboard.writeText(r),yield e.socialService.shareProduct(t._id,{platform:"copy_link",message:`Check out this fresh arrival: ${t.name} from ${t.brand}!`}),console.log("Product link copied to clipboard!")}catch(r){console.error("Error sharing product:",r)}})()}onAddToCart(t,o){var e=this;return(0,s.A)(function*(){o.stopPropagation();try{yield e.cartService.addToCart(t._id,1),console.log("Product added to cart!")}catch(r){console.error("Error adding to cart:",r)}})()}onAddToWishlist(t,o){var e=this;return(0,s.A)(function*(){o.stopPropagation();try{yield e.wishlistService.addToWishlist(t._id),console.log("Product added to wishlist!")}catch(r){console.error("Error adding to wishlist:",r)}})()}getDiscountPercentage(t){return t.originalPrice&&t.originalPrice>t.price?Math.round((t.originalPrice-t.price)/t.originalPrice*100):0}formatPrice(t){return new Intl.NumberFormat("en-IN",{style:"currency",currency:"INR",minimumFractionDigits:0}).format(t)}getDaysAgo(t){const o=new Date,e=new Date(t),r=Math.abs(o.getTime()-e.getTime());return Math.ceil(r/864e5)}onRetry(){this.loadNewArrivals()}onViewAll(){this.router.navigate(["/products"],{queryParams:{filter:"new-arrivals"}})}isProductLiked(t){return this.likedProducts.has(t)}trackByProductId(t,o){return o._id}initializeSlider(){this.updateResponsiveSettings(),this.calculateMaxSlide(),window.addEventListener("resize",()=>this.updateResponsiveSettings())}updateResponsiveSettings(){const t=window.innerWidth;t>=1200?(this.visibleCards=4,this.cardWidth=280):t>=992?(this.visibleCards=3,this.cardWidth=260):t>=768?(this.visibleCards=2,this.cardWidth=240):(this.visibleCards=1,this.cardWidth=220),this.calculateMaxSlide(),this.updateSlidePosition()}calculateMaxSlide(){this.maxSlide=Math.max(0,this.newArrivals.length-this.visibleCards)}updateSlidePosition(){this.slideOffset=this.currentSlide*(this.cardWidth+16)}nextSlide(){this.currentSlide<this.maxSlide&&(this.currentSlide++,this.updateSlidePosition())}prevSlide(){this.currentSlide>0&&(this.currentSlide--,this.updateSlidePosition())}startAutoSlide(){this.autoSlideInterval=setInterval(()=>{this.currentSlide>=this.maxSlide?this.currentSlide=0:this.currentSlide++,this.updateSlidePosition()},this.autoSlideDelay)}stopAutoSlide(){this.autoSlideInterval&&(clearInterval(this.autoSlideInterval),this.autoSlideInterval=null)}pauseAutoSlide(){this.stopAutoSlide()}resumeAutoSlide(){this.startAutoSlide()}get canGoPrev(){return this.currentSlide>0}get canGoNext(){return this.currentSlide<this.maxSlide}toggleSectionLike(){this.isSectionLiked=!this.isSectionLiked,this.isSectionLiked?this.sectionLikes++:this.sectionLikes--}toggleSectionBookmark(){this.isSectionBookmarked=!this.isSectionBookmarked}openComments(){console.log("Opening comments for new arrivals section")}shareSection(){navigator.share?navigator.share({title:"New Arrivals",text:"Check out these fresh new fashion arrivals!",url:window.location.href}):(navigator.clipboard.writeText(window.location.href),console.log("Link copied to clipboard"))}openMusicPlayer(){console.log("Opening music player for new arrivals")}formatCount(t){return t>=1e6?(t/1e6).toFixed(1)+"M":t>=1e3?(t/1e3).toFixed(1)+"K":t.toString()}checkMobileDevice(){this.isMobile=window.innerWidth<=768}static{this.\u0275fac=function(o){return new(o||i)(n.rXU(C),n.rXU(P),n.rXU(b.CartService),n.rXU(x.WishlistService),n.rXU(m.Ix))}}static{this.\u0275cmp=n.VBU({type:i,selectors:[["app-new-arrivals"]],standalone:!0,features:[n.aNF],decls:15,vars:4,consts:[[1,"new-arrivals-container"],["class","mobile-action-buttons",4,"ngIf"],[1,"section-header"],[1,"header-content"],[1,"section-title"],["name","sparkles",1,"title-icon"],[1,"section-subtitle"],[1,"view-all-btn",3,"click"],["name","chevron-forward"],["class","loading-container",4,"ngIf"],["class","error-container",4,"ngIf"],["class","products-slider-container",3,"mouseenter","mouseleave",4,"ngIf"],[1,"mobile-action-buttons"],[1,"action-btn","like-btn",3,"click"],[3,"name"],[1,"action-count"],[1,"action-btn","comment-btn",3,"click"],["name","chatbubble-outline"],[1,"action-btn","share-btn",3,"click"],["name","arrow-redo-outline"],[1,"action-text"],[1,"action-btn","bookmark-btn",3,"click"],[1,"action-btn","music-btn",3,"click"],["name","musical-notes"],[1,"loading-container"],[1,"loading-grid"],["class","loading-card",4,"ngFor","ngForOf"],[1,"loading-card"],[1,"loading-image"],[1,"loading-content"],[1,"loading-line","short"],[1,"loading-line","medium"],[1,"loading-line","long"],[1,"error-container"],["name","alert-circle",1,"error-icon"],[1,"error-message"],[1,"retry-btn",3,"click"],["name","refresh"],[1,"products-slider-container",3,"mouseenter","mouseleave"],[1,"nav-btn","prev-btn",3,"click","disabled"],["name","chevron-back"],[1,"nav-btn","next-btn",3,"click","disabled"],[1,"products-slider-wrapper"],[1,"products-slider"],["class","product-card",3,"width","click",4,"ngFor","ngForOf","ngForTrackBy"],["class","empty-container",4,"ngIf"],[1,"product-card",3,"click"],[1,"product-image-container"],["loading","lazy",1,"product-image",3,"src","alt"],[1,"new-badge"],["name","sparkles"],[1,"days-badge"],["class","discount-badge",4,"ngIf"],[1,"action-buttons"],["name","share-outline"],[1,"product-info"],[1,"product-brand"],[1,"product-name"],[1,"price-section"],[1,"current-price"],["class","original-price",4,"ngIf"],[1,"rating-section"],[1,"stars"],[3,"name","filled",4,"ngFor","ngForOf"],[1,"rating-text"],[1,"product-actions"],[1,"cart-btn",3,"click"],["name","bag-add-outline"],[1,"wishlist-btn",3,"click"],["name","heart-outline"],[1,"discount-badge"],[1,"original-price"],[1,"empty-container"],["name","sparkles-outline",1,"empty-icon"],[1,"empty-title"],[1,"empty-message"]],template:function(o,e){1&o&&(n.j41(0,"div",0),n.DNE(1,wn,19,8,"div",1),n.j41(2,"div",2)(3,"div",3)(4,"h2",4),n.nrm(5,"ion-icon",5),n.EFF(6," New Arrivals "),n.k0s(),n.j41(7,"p",6),n.EFF(8,"Fresh styles just landed"),n.k0s()(),n.j41(9,"button",7),n.bIt("click",function(){return e.onViewAll()}),n.EFF(10," View All "),n.nrm(11,"ion-icon",8),n.k0s()(),n.DNE(12,Sn,3,2,"div",9)(13,Fn,7,1,"div",10)(14,$n,9,9,"div",11),n.k0s()),2&o&&(n.R7$(),n.Y8G("ngIf",e.isMobile),n.R7$(11),n.Y8G("ngIf",e.isLoading),n.R7$(),n.Y8G("ngIf",e.error&&!e.isLoading),n.R7$(),n.Y8G("ngIf",!e.isLoading&&!e.error&&e.newArrivals.length>0))},dependencies:[d.MD,d.Sq,d.bT,g.bv,g.iq,_.Rl],styles:[".new-arrivals-container[_ngcontent-%COMP%]{padding:20px;background:linear-gradient(135deg,#667eea,#764ba2);border-radius:16px;margin-bottom:24px;position:relative}.mobile-action-buttons[_ngcontent-%COMP%]{position:absolute;right:15px;top:50%;transform:translateY(-50%);display:flex;flex-direction:column;gap:20px;z-index:10}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]{width:48px;height:48px;border-radius:50%;border:none;background:#fff3;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);color:#fff;display:flex;flex-direction:column;align-items:center;justify-content:center;cursor:pointer;transition:all .3s ease;position:relative}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:24px;margin-bottom:2px}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   .action-count[_ngcontent-%COMP%], .mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   .action-text[_ngcontent-%COMP%]{font-size:10px;font-weight:600;line-height:1}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:hover{transform:scale(1.1);background:#ffffff4d}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active[_ngcontent-%COMP%]{background:#ffffffe6;color:#667eea}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active.like-btn[_ngcontent-%COMP%]{color:#ff3040}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active.like-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_heartBeat .6s ease-in-out}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active.bookmark-btn[_ngcontent-%COMP%]{color:gold}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.like-btn.active[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:#ff3040}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.music-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,#ff3040,#667eea)}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.music-btn[_ngcontent-%COMP%]:hover{transform:scale(1.1) rotate(15deg)}@keyframes _ngcontent-%COMP%_heartBeat{0%{transform:scale(1)}25%{transform:scale(1.3)}50%{transform:scale(1.1)}75%{transform:scale(1.25)}to{transform:scale(1)}}@media (min-width: 769px){.mobile-action-buttons[_ngcontent-%COMP%]{display:none}}.section-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:24px}.section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]{flex:1}.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{font-size:24px;font-weight:700;color:#fff;margin:0 0 8px;display:flex;align-items:center;gap:12px}.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   .title-icon[_ngcontent-%COMP%]{font-size:28px;color:gold}.section-header[_ngcontent-%COMP%]   .section-subtitle[_ngcontent-%COMP%]{font-size:14px;color:#fffc;margin:0}.section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]{background:#fff3;color:#fff;border:2px solid rgba(255,255,255,.3);padding:12px 20px;border-radius:25px;font-weight:600;font-size:14px;display:flex;align-items:center;gap:8px;cursor:pointer;transition:all .3s ease;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]:hover{background:#ffffff4d;border-color:#ffffff80;transform:translateY(-2px)}.section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:16px}.loading-container[_ngcontent-%COMP%]   .loading-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(280px,1fr));gap:20px}.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]{background:#ffffff1a;border-radius:16px;overflow:hidden;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-image[_ngcontent-%COMP%]{width:100%;height:200px;background:#fff3;animation:_ngcontent-%COMP%_loading 1.5s infinite}.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]{padding:16px}.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line[_ngcontent-%COMP%]{height:12px;background:#fff3;animation:_ngcontent-%COMP%_loading 1.5s infinite;border-radius:6px;margin-bottom:8px}.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.short[_ngcontent-%COMP%]{width:40%}.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.medium[_ngcontent-%COMP%]{width:60%}.loading-container[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.long[_ngcontent-%COMP%]{width:80%}@keyframes _ngcontent-%COMP%_loading{0%,to{opacity:.6}50%{opacity:1}}.error-container[_ngcontent-%COMP%]{text-align:center;padding:40px 20px}.error-container[_ngcontent-%COMP%]   .error-icon[_ngcontent-%COMP%]{font-size:48px;color:#ff6b6b;margin-bottom:16px}.error-container[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%]{font-size:16px;color:#fffc;margin-bottom:20px}.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]{background:#fff3;color:#fff;border:2px solid rgba(255,255,255,.3);padding:12px 24px;border-radius:8px;font-weight:600;cursor:pointer;display:flex;align-items:center;gap:8px;margin:0 auto;transition:all .3s ease;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]:hover{background:#ffffff4d;border-color:#ffffff80}.products-slider-container[_ngcontent-%COMP%]{position:relative;overflow:hidden}.products-slider-container[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%]{position:absolute;top:50%;transform:translateY(-50%);z-index:10;background:#000000b3;color:#fff;border:none;width:40px;height:40px;border-radius:50%;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all .3s ease}.products-slider-container[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%]:hover:not(:disabled){background:#000000e6;transform:translateY(-50%) scale(1.1)}.products-slider-container[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%]:disabled{opacity:.3;cursor:not-allowed}.products-slider-container[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:20px}.products-slider-container[_ngcontent-%COMP%]   .nav-btn.prev-btn[_ngcontent-%COMP%]{left:-20px}.products-slider-container[_ngcontent-%COMP%]   .nav-btn.next-btn[_ngcontent-%COMP%]{right:-20px}.products-slider-wrapper[_ngcontent-%COMP%]{overflow:hidden;padding:0 10px}.products-slider[_ngcontent-%COMP%]{display:flex;gap:16px;transition:transform .3s ease;will-change:transform}.product-card[_ngcontent-%COMP%]{background:#ffffff1a;border-radius:16px;overflow:hidden;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border:1px solid rgba(255,255,255,.2);transition:all .3s ease;cursor:pointer;flex-shrink:0;min-width:280px}.product-card[_ngcontent-%COMP%]:hover{transform:translateY(-8px);background:#ffffff26;box-shadow:0 12px 40px #0003}.product-card[_ngcontent-%COMP%]:hover   .action-buttons[_ngcontent-%COMP%]{opacity:1;transform:translateY(0)}.product-image-container[_ngcontent-%COMP%]{position:relative;overflow:hidden}.product-image-container[_ngcontent-%COMP%]   .product-image[_ngcontent-%COMP%]{width:100%;height:200px;object-fit:cover;transition:transform .3s ease}.product-image-container[_ngcontent-%COMP%]   .new-badge[_ngcontent-%COMP%]{position:absolute;top:12px;left:12px;background:linear-gradient(135deg,gold,#ffed4e);color:#333;padding:6px 12px;border-radius:20px;font-size:12px;font-weight:600;display:flex;align-items:center;gap:4px}.product-image-container[_ngcontent-%COMP%]   .new-badge[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:14px}.product-image-container[_ngcontent-%COMP%]   .days-badge[_ngcontent-%COMP%]{position:absolute;top:50px;left:12px;background:#ffffffe6;color:#333;padding:4px 8px;border-radius:12px;font-size:10px;font-weight:600}.product-image-container[_ngcontent-%COMP%]   .discount-badge[_ngcontent-%COMP%]{position:absolute;top:12px;right:12px;background:#dc3545;color:#fff;padding:6px 10px;border-radius:12px;font-size:12px;font-weight:700}.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]{position:absolute;top:50%;right:12px;transform:translateY(-50%);display:flex;flex-direction:column;gap:8px;opacity:0;transition:all .3s ease}.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;border:none;background:#ffffffe6;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all .3s ease}.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:20px;color:#333}.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:hover{background:#fff;transform:scale(1.1)}.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn.like-btn[_ngcontent-%COMP%]:hover   ion-icon[_ngcontent-%COMP%]{color:#dc3545}.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn.like-btn.liked[_ngcontent-%COMP%]{background:#dc354526}.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn.like-btn.liked[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:#dc3545}.product-image-container[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .action-btn.share-btn[_ngcontent-%COMP%]:hover   ion-icon[_ngcontent-%COMP%]{color:#007bff}.product-info[_ngcontent-%COMP%]{padding:16px}.product-info[_ngcontent-%COMP%]   .product-brand[_ngcontent-%COMP%]{font-size:12px;color:#ffffffb3;text-transform:uppercase;font-weight:600;letter-spacing:.5px;margin-bottom:4px}.product-info[_ngcontent-%COMP%]   .product-name[_ngcontent-%COMP%]{font-size:16px;font-weight:600;color:#fff;margin:0 0 12px;line-height:1.4;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;overflow:hidden}.product-info[_ngcontent-%COMP%]   .price-section[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;margin-bottom:12px}.product-info[_ngcontent-%COMP%]   .price-section[_ngcontent-%COMP%]   .current-price[_ngcontent-%COMP%]{font-size:18px;font-weight:700;color:gold}.product-info[_ngcontent-%COMP%]   .price-section[_ngcontent-%COMP%]   .original-price[_ngcontent-%COMP%]{font-size:14px;color:#fff9;text-decoration:line-through}.product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;margin-bottom:16px}.product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]{display:flex;gap:2px}.product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:14px;color:#ffffff4d}.product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%]   .stars[_ngcontent-%COMP%]   ion-icon.filled[_ngcontent-%COMP%]{color:gold}.product-info[_ngcontent-%COMP%]   .rating-section[_ngcontent-%COMP%]   .rating-text[_ngcontent-%COMP%]{font-size:12px;color:#fff9}.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]{display:flex;gap:8px}.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .cart-btn[_ngcontent-%COMP%]{flex:1;background:#fff3;color:#fff;border:2px solid rgba(255,255,255,.3);padding:12px 16px;border-radius:8px;font-weight:600;font-size:14px;display:flex;align-items:center;justify-content:center;gap:8px;cursor:pointer;transition:all .3s ease;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .cart-btn[_ngcontent-%COMP%]:hover{background:#ffffff4d;border-color:#ffffff80;transform:translateY(-2px)}.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .cart-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:16px}.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .wishlist-btn[_ngcontent-%COMP%]{width:44px;height:44px;border:2px solid rgba(255,255,255,.3);background:#ffffff1a;border-radius:8px;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all .3s ease;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .wishlist-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:20px;color:#fffc}.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .wishlist-btn[_ngcontent-%COMP%]:hover{background:#fff3;border-color:#ffffff80}.product-info[_ngcontent-%COMP%]   .product-actions[_ngcontent-%COMP%]   .wishlist-btn[_ngcontent-%COMP%]:hover   ion-icon[_ngcontent-%COMP%]{color:gold}.empty-container[_ngcontent-%COMP%]{text-align:center;padding:60px 20px}.empty-container[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%]{font-size:64px;color:#fff6;margin-bottom:20px}.empty-container[_ngcontent-%COMP%]   .empty-title[_ngcontent-%COMP%]{font-size:20px;font-weight:600;color:#fff;margin-bottom:8px}.empty-container[_ngcontent-%COMP%]   .empty-message[_ngcontent-%COMP%]{font-size:14px;color:#ffffffb3}@media (max-width: 768px){.new-arrivals-container[_ngcontent-%COMP%]{padding:16px}.section-header[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start;gap:16px}.section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]{align-self:flex-end}.products-slider-container[_ngcontent-%COMP%]   .nav-btn.prev-btn[_ngcontent-%COMP%]{left:-15px}.products-slider-container[_ngcontent-%COMP%]   .nav-btn.next-btn[_ngcontent-%COMP%]{right:-15px}.product-card[_ngcontent-%COMP%]{min-width:250px}}@media (max-width: 768px){.products-slider-container[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%]{width:35px;height:35px}.products-slider-container[_ngcontent-%COMP%]   .nav-btn.prev-btn[_ngcontent-%COMP%]{left:-10px}.products-slider-container[_ngcontent-%COMP%]   .nav-btn.next-btn[_ngcontent-%COMP%]{right:-10px}.products-slider-container[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px}.products-slider-wrapper[_ngcontent-%COMP%]{padding:0 5px}.products-slider[_ngcontent-%COMP%]{gap:12px}.product-card[_ngcontent-%COMP%]{min-width:220px}.section-title[_ngcontent-%COMP%]{font-size:20px}}"]})}}return i})();const Bn=()=>[1,2,3,4];function An(i,a){if(1&i){const t=n.RV6();n.j41(0,"div",11)(1,"button",12),n.bIt("click",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.toggleSectionLike())}),n.nrm(2,"ion-icon",13),n.j41(3,"span",14),n.EFF(4),n.k0s()(),n.j41(5,"button",15),n.bIt("click",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.openComments())}),n.nrm(6,"ion-icon",16),n.j41(7,"span",14),n.EFF(8),n.k0s()(),n.j41(9,"button",17),n.bIt("click",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.shareSection())}),n.nrm(10,"ion-icon",18),n.j41(11,"span",19),n.EFF(12,"Share"),n.k0s()(),n.j41(13,"button",20),n.bIt("click",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.toggleSectionBookmark())}),n.nrm(14,"ion-icon",13),n.k0s(),n.j41(15,"button",21),n.bIt("click",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.openMusicPlayer())}),n.nrm(16,"ion-icon",22),n.j41(17,"span",19),n.EFF(18,"Music"),n.k0s()()()}if(2&i){const t=n.XpG();n.R7$(),n.AVh("active",t.isSectionLiked),n.R7$(),n.Y8G("name",t.isSectionLiked?"heart":"heart-outline"),n.R7$(2),n.JRh(t.formatCount(t.sectionLikes)),n.R7$(4),n.JRh(t.formatCount(t.sectionComments)),n.R7$(5),n.AVh("active",t.isSectionBookmarked),n.R7$(),n.Y8G("name",t.isSectionBookmarked?"bookmark":"bookmark-outline")}}function Gn(i,a){1&i&&(n.j41(0,"div",26),n.nrm(1,"div",27),n.j41(2,"div",28),n.nrm(3,"div",29)(4,"div",30)(5,"div",31),n.k0s()())}function Nn(i,a){1&i&&(n.j41(0,"div",23)(1,"div",24),n.DNE(2,Gn,6,0,"div",25),n.k0s()()),2&i&&(n.R7$(2),n.Y8G("ngForOf",n.lJ4(1,Bn)))}function En(i,a){if(1&i){const t=n.RV6();n.j41(0,"div",32),n.nrm(1,"ion-icon",33),n.j41(2,"p",34),n.EFF(3),n.k0s(),n.j41(4,"button",35),n.bIt("click",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.onRetry())}),n.nrm(5,"ion-icon",36),n.EFF(6," Try Again "),n.k0s()()}if(2&i){const t=n.XpG();n.R7$(3),n.JRh(t.error)}}function Ln(i,a){1&i&&(n.j41(0,"div",56),n.nrm(1,"ion-icon",57),n.k0s())}function Yn(i,a){if(1&i){const t=n.RV6();n.j41(0,"div",45),n.bIt("click",function(){const e=n.eBV(t).$implicit,r=n.XpG(2);return n.Njj(r.onUserClick(e))}),n.j41(1,"div",46),n.nrm(2,"img",47),n.DNE(3,Ln,2,0,"div",48),n.k0s(),n.j41(4,"div",49)(5,"h3",50),n.EFF(6),n.k0s(),n.j41(7,"p",51),n.EFF(8),n.k0s(),n.j41(9,"p",52),n.EFF(10),n.k0s(),n.j41(11,"p",53),n.EFF(12),n.k0s(),n.j41(13,"p",54),n.EFF(14),n.k0s()(),n.j41(15,"button",55),n.bIt("click",function(e){const r=n.eBV(t).$implicit,c=n.XpG(2);return n.Njj(c.onFollowUser(r,e))}),n.j41(16,"span"),n.EFF(17),n.k0s(),n.nrm(18,"ion-icon",13),n.k0s()()}if(2&i){const t=a.$implicit,o=n.XpG(2);n.R7$(2),n.Y8G("src",t.avatar,n.B4B)("alt",t.fullName),n.R7$(),n.Y8G("ngIf",t.isInfluencer),n.R7$(3),n.JRh(t.fullName),n.R7$(2),n.SpI("@",t.username,""),n.R7$(2),n.SpI("",o.formatFollowerCount(t.followerCount)," followers"),n.R7$(2),n.JRh(t.category),n.R7$(2),n.JRh(t.followedBy),n.R7$(),n.AVh("following",t.isFollowing),n.R7$(2),n.JRh(t.isFollowing?"Following":"Follow"),n.R7$(),n.Y8G("name",t.isFollowing?"checkmark":"add")}}function Vn(i,a){if(1&i){const t=n.RV6();n.j41(0,"div",37)(1,"button",38),n.bIt("click",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.slidePrev())}),n.nrm(2,"ion-icon",39),n.k0s(),n.j41(3,"button",40),n.bIt("click",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.slideNext())}),n.nrm(4,"ion-icon",41),n.k0s(),n.j41(5,"div",42),n.bIt("mouseenter",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.pauseAutoSlide())})("mouseleave",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.resumeAutoSlide())}),n.j41(6,"div",43),n.DNE(7,Yn,19,12,"div",44),n.k0s()()()}if(2&i){const t=n.XpG();n.R7$(),n.Y8G("disabled",0===t.currentSlide),n.R7$(2),n.Y8G("disabled",t.currentSlide>=t.maxSlide),n.R7$(3),n.xc7("transform","translateX("+t.slideOffset+"px)"),n.R7$(),n.Y8G("ngForOf",t.suggestedUsers)("ngForTrackBy",t.trackByUserId)}}function Xn(i,a){1&i&&(n.j41(0,"div",58),n.nrm(1,"ion-icon",59),n.j41(2,"h3",60),n.EFF(3,"No Suggestions"),n.k0s(),n.j41(4,"p",61),n.EFF(5,"Check back later for user suggestions"),n.k0s()())}let y=(()=>{class i{constructor(t){this.router=t,this.suggestedUsers=[],this.isLoading=!0,this.error=null,this.subscription=new f.yU,this.currentSlide=0,this.slideOffset=0,this.cardWidth=200,this.visibleCards=4,this.maxSlide=0,this.autoSlideDelay=5e3,this.isAutoSliding=!0,this.isPaused=!1,this.isSectionLiked=!1,this.isSectionBookmarked=!1,this.sectionLikes=198,this.sectionComments=67,this.isMobile=!1}ngOnInit(){this.loadSuggestedUsers(),this.updateResponsiveSettings(),this.setupResizeListener(),this.checkMobileDevice()}ngOnDestroy(){this.subscription.unsubscribe(),this.stopAutoSlide()}loadSuggestedUsers(){var t=this;return(0,s.A)(function*(){try{t.isLoading=!0,t.error=null,t.suggestedUsers=[{id:"1",username:"fashionista_maya",fullName:"Maya Patel",avatar:"https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=150&h=150&fit=crop&crop=face",followedBy:"Followed by john_doe and 12 others",isFollowing:!1,isInfluencer:!0,followerCount:45e3,category:"Fashion"},{id:"2",username:"style_guru_raj",fullName:"Raj Kumar",avatar:"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",followedBy:"Followed by sarah_k and 8 others",isFollowing:!1,isInfluencer:!0,followerCount:32e3,category:"Menswear"},{id:"3",username:"trendy_sara",fullName:"Sara Johnson",avatar:"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face",followedBy:"Followed by alex_m and 15 others",isFollowing:!1,isInfluencer:!1,followerCount:8500,category:"Casual"},{id:"4",username:"luxury_lover",fullName:"Emma Wilson",avatar:"https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face",followedBy:"Followed by mike_t and 20 others",isFollowing:!1,isInfluencer:!0,followerCount:67e3,category:"Luxury"},{id:"5",username:"street_style_alex",fullName:"Alex Chen",avatar:"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",followedBy:"Followed by lisa_p and 5 others",isFollowing:!1,isInfluencer:!1,followerCount:12e3,category:"Streetwear"},{id:"6",username:"boho_bella",fullName:"Isabella Rodriguez",avatar:"https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face",followedBy:"Followed by tom_h and 18 others",isFollowing:!1,isInfluencer:!0,followerCount:28e3,category:"Boho"}],t.isLoading=!1,t.updateSliderOnUsersLoad()}catch(o){console.error("Error loading suggested users:",o),t.error="Failed to load suggested users",t.isLoading=!1}})()}onUserClick(t){this.router.navigate(["/profile",t.username])}onFollowUser(t,o){o.stopPropagation(),t.isFollowing=!t.isFollowing,t.isFollowing?t.followerCount++:t.followerCount--}formatFollowerCount(t){return t>=1e6?(t/1e6).toFixed(1)+"M":t>=1e3?(t/1e3).toFixed(1)+"K":t.toString()}onRetry(){this.loadSuggestedUsers()}trackByUserId(t,o){return o.id}startAutoSlide(){!this.isAutoSliding||this.isPaused||(this.stopAutoSlide(),this.autoSlideInterval=setInterval(()=>{!this.isPaused&&this.suggestedUsers.length>this.visibleCards&&this.autoSlideNext()},this.autoSlideDelay))}stopAutoSlide(){this.autoSlideInterval&&(clearInterval(this.autoSlideInterval),this.autoSlideInterval=null)}autoSlideNext(){this.currentSlide>=this.maxSlide?this.currentSlide=0:this.currentSlide++,this.updateSlideOffset()}pauseAutoSlide(){this.isPaused=!0,this.stopAutoSlide()}resumeAutoSlide(){this.isPaused=!1,this.startAutoSlide()}updateResponsiveSettings(){const t=window.innerWidth;t<=480?(this.cardWidth=180,this.visibleCards=1):t<=768?(this.cardWidth=200,this.visibleCards=2):t<=1200?(this.cardWidth=220,this.visibleCards=3):(this.cardWidth=220,this.visibleCards=4),this.updateSliderLimits(),this.updateSlideOffset()}setupResizeListener(){window.addEventListener("resize",()=>{this.updateResponsiveSettings()})}updateSliderLimits(){this.maxSlide=Math.max(0,this.suggestedUsers.length-this.visibleCards)}slidePrev(){this.currentSlide>0&&(this.currentSlide--,this.updateSlideOffset(),this.restartAutoSlideAfterInteraction())}slideNext(){this.currentSlide<this.maxSlide&&(this.currentSlide++,this.updateSlideOffset(),this.restartAutoSlideAfterInteraction())}updateSlideOffset(){this.slideOffset=-this.currentSlide*this.cardWidth}restartAutoSlideAfterInteraction(){this.stopAutoSlide(),setTimeout(()=>{this.startAutoSlide()},2e3)}updateSliderOnUsersLoad(){setTimeout(()=>{this.updateSliderLimits(),this.currentSlide=0,this.slideOffset=0,this.startAutoSlide()},100)}toggleSectionLike(){this.isSectionLiked=!this.isSectionLiked,this.isSectionLiked?this.sectionLikes++:this.sectionLikes--}toggleSectionBookmark(){this.isSectionBookmarked=!this.isSectionBookmarked}openComments(){console.log("Opening comments for suggested users section")}shareSection(){navigator.share?navigator.share({title:"Suggested for You",text:"Discover amazing fashion creators!",url:window.location.href}):(navigator.clipboard.writeText(window.location.href),console.log("Link copied to clipboard"))}openMusicPlayer(){console.log("Opening music player for suggested users")}formatCount(t){return t>=1e6?(t/1e6).toFixed(1)+"M":t>=1e3?(t/1e3).toFixed(1)+"K":t.toString()}checkMobileDevice(){this.isMobile=window.innerWidth<=768}static{this.\u0275fac=function(o){return new(o||i)(n.rXU(m.Ix))}}static{this.\u0275cmp=n.VBU({type:i,selectors:[["app-suggested-for-you"]],standalone:!0,features:[n.aNF],decls:13,vars:5,consts:[[1,"suggested-users-container"],["class","mobile-action-buttons",4,"ngIf"],[1,"section-header"],[1,"header-content"],[1,"section-title"],["name","people",1,"title-icon"],[1,"section-subtitle"],["class","loading-container",4,"ngIf"],["class","error-container",4,"ngIf"],["class","users-slider-container",4,"ngIf"],["class","empty-container",4,"ngIf"],[1,"mobile-action-buttons"],[1,"action-btn","like-btn",3,"click"],[3,"name"],[1,"action-count"],[1,"action-btn","comment-btn",3,"click"],["name","chatbubble-outline"],[1,"action-btn","share-btn",3,"click"],["name","arrow-redo-outline"],[1,"action-text"],[1,"action-btn","bookmark-btn",3,"click"],[1,"action-btn","music-btn",3,"click"],["name","musical-notes"],[1,"loading-container"],[1,"loading-grid"],["class","loading-user-card",4,"ngFor","ngForOf"],[1,"loading-user-card"],[1,"loading-avatar"],[1,"loading-content"],[1,"loading-line","short"],[1,"loading-line","medium"],[1,"loading-line","long"],[1,"error-container"],["name","alert-circle",1,"error-icon"],[1,"error-message"],[1,"retry-btn",3,"click"],["name","refresh"],[1,"users-slider-container"],[1,"slider-nav","prev-btn",3,"click","disabled"],["name","chevron-back"],[1,"slider-nav","next-btn",3,"click","disabled"],["name","chevron-forward"],[1,"users-slider-wrapper",3,"mouseenter","mouseleave"],[1,"users-slider"],["class","user-card",3,"click",4,"ngFor","ngForOf","ngForTrackBy"],[1,"user-card",3,"click"],[1,"user-avatar-container"],["loading","lazy",1,"user-avatar",3,"src","alt"],["class","influencer-badge",4,"ngIf"],[1,"user-info"],[1,"user-name"],[1,"username"],[1,"follower-count"],[1,"category-tag"],[1,"followed-by"],[1,"follow-btn",3,"click"],[1,"influencer-badge"],["name","star"],[1,"empty-container"],["name","people-outline",1,"empty-icon"],[1,"empty-title"],[1,"empty-message"]],template:function(o,e){1&o&&(n.j41(0,"div",0),n.DNE(1,An,19,8,"div",1),n.j41(2,"div",2)(3,"div",3)(4,"h2",4),n.nrm(5,"ion-icon",5),n.EFF(6," Suggested for you "),n.k0s(),n.j41(7,"p",6),n.EFF(8,"Discover amazing fashion creators"),n.k0s()()(),n.DNE(9,Nn,3,2,"div",7)(10,En,7,1,"div",8)(11,Vn,8,6,"div",9)(12,Xn,6,0,"div",10),n.k0s()),2&o&&(n.R7$(),n.Y8G("ngIf",e.isMobile),n.R7$(8),n.Y8G("ngIf",e.isLoading),n.R7$(),n.Y8G("ngIf",e.error&&!e.isLoading),n.R7$(),n.Y8G("ngIf",!e.isLoading&&!e.error&&e.suggestedUsers.length>0),n.R7$(),n.Y8G("ngIf",!e.isLoading&&!e.error&&0===e.suggestedUsers.length))},dependencies:[d.MD,d.Sq,d.bT,g.bv,g.iq,_.Rl],styles:[".suggested-users-container[_ngcontent-%COMP%]{padding:20px;background:linear-gradient(135deg,#f8f9fa,#e9ecef);border-radius:16px;margin-bottom:24px;position:relative;max-width:675px}.mobile-action-buttons[_ngcontent-%COMP%]{position:absolute;right:15px;top:50%;transform:translateY(-50%);display:flex;flex-direction:column;gap:20px;z-index:10}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]{width:48px;height:48px;border-radius:50%;border:none;background:#6c5ce733;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);color:#6c5ce7;display:flex;flex-direction:column;align-items:center;justify-content:center;cursor:pointer;transition:all .3s ease;position:relative}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:24px;margin-bottom:2px}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   .action-count[_ngcontent-%COMP%], .mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   .action-text[_ngcontent-%COMP%]{font-size:10px;font-weight:600;line-height:1}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:hover{transform:scale(1.1);background:#6c5ce74d}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active[_ngcontent-%COMP%]{background:#6c5ce7e6;color:#fff}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active.like-btn[_ngcontent-%COMP%]{background:#ff3040e6;color:#fff}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active.like-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_heartBeat .6s ease-in-out}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active.bookmark-btn[_ngcontent-%COMP%]{background:#ffd700e6;color:#fff}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.like-btn.active[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:#fff}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.music-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,#ff3040,#6c5ce7);color:#fff}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.music-btn[_ngcontent-%COMP%]:hover{transform:scale(1.1) rotate(15deg)}@keyframes _ngcontent-%COMP%_heartBeat{0%{transform:scale(1)}25%{transform:scale(1.3)}50%{transform:scale(1.1)}75%{transform:scale(1.25)}to{transform:scale(1)}}@media (min-width: 769px){.mobile-action-buttons[_ngcontent-%COMP%]{display:none}}.section-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:24px}.section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]{flex:1}.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{font-size:24px;font-weight:700;color:#1a1a1a;margin:0 0 8px;display:flex;align-items:center;gap:12px}.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   .title-icon[_ngcontent-%COMP%]{font-size:28px;color:#6c5ce7}.section-header[_ngcontent-%COMP%]   .section-subtitle[_ngcontent-%COMP%]{font-size:14px;color:#666;margin:0}.loading-container[_ngcontent-%COMP%]   .loading-grid[_ngcontent-%COMP%]{display:flex;gap:20px;overflow-x:auto;padding-bottom:8px}.loading-container[_ngcontent-%COMP%]   .loading-user-card[_ngcontent-%COMP%]{flex:0 0 180px;background:#ffffffb3;border-radius:16px;padding:20px}.loading-container[_ngcontent-%COMP%]   .loading-user-card[_ngcontent-%COMP%]   .loading-avatar[_ngcontent-%COMP%]{width:80px;height:80px;border-radius:50%;background:linear-gradient(90deg,#f0f0f0 25%,#e0e0e0,#f0f0f0 75%);background-size:200% 100%;animation:_ngcontent-%COMP%_loading 1.5s infinite;margin:0 auto 16px}.loading-container[_ngcontent-%COMP%]   .loading-user-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line[_ngcontent-%COMP%]{height:12px;border-radius:6px;background:linear-gradient(90deg,#f0f0f0 25%,#e0e0e0,#f0f0f0 75%);background-size:200% 100%;animation:_ngcontent-%COMP%_loading 1.5s infinite;margin-bottom:8px}.loading-container[_ngcontent-%COMP%]   .loading-user-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.short[_ngcontent-%COMP%]{width:60%}.loading-container[_ngcontent-%COMP%]   .loading-user-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.medium[_ngcontent-%COMP%]{width:80%}.loading-container[_ngcontent-%COMP%]   .loading-user-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.long[_ngcontent-%COMP%]{width:100%}@keyframes _ngcontent-%COMP%_loading{0%{background-position:200% 0}to{background-position:-200% 0}}.error-container[_ngcontent-%COMP%]{text-align:center;padding:40px 20px}.error-container[_ngcontent-%COMP%]   .error-icon[_ngcontent-%COMP%]{font-size:48px;color:#e74c3c;margin-bottom:16px}.error-container[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%]{color:#666;margin-bottom:20px;font-size:16px}.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,#e74c3c,#c0392b);color:#fff;border:none;padding:12px 24px;border-radius:25px;font-weight:600;display:flex;align-items:center;gap:8px;margin:0 auto;cursor:pointer;transition:all .3s ease}.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 8px 25px #e74c3c4d}.users-slider-container[_ngcontent-%COMP%]{position:relative;margin:0 -20px}.users-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]{position:absolute;top:50%;transform:translateY(-50%);z-index:10;background:#000000b3;color:#fff;border:none;width:40px;height:40px;border-radius:50%;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all .3s ease}.users-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]:hover:not(:disabled){background:#000000e6;transform:translateY(-50%) scale(1.1)}.users-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]:disabled{opacity:.3;cursor:not-allowed}.users-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px}.users-slider-container[_ngcontent-%COMP%]   .slider-nav.prev-btn[_ngcontent-%COMP%]{left:-20px}.users-slider-container[_ngcontent-%COMP%]   .slider-nav.next-btn[_ngcontent-%COMP%]{right:-20px}.users-slider-wrapper[_ngcontent-%COMP%]{overflow:hidden;padding:0 20px}.users-slider[_ngcontent-%COMP%]{display:flex;transition:transform .4s cubic-bezier(.25,.46,.45,.94);gap:20px}.users-slider[_ngcontent-%COMP%]   .user-card[_ngcontent-%COMP%]{flex:0 0 180px;width:180px}.user-card[_ngcontent-%COMP%]{background:#fff;border-radius:16px;padding:20px;text-align:center;box-shadow:0 4px 20px #00000014;transition:all .3s ease;cursor:pointer}.user-card[_ngcontent-%COMP%]:hover{transform:translateY(-8px);box-shadow:0 12px 40px #00000026}.user-avatar-container[_ngcontent-%COMP%]{position:relative;margin-bottom:16px}.user-avatar-container[_ngcontent-%COMP%]   .user-avatar[_ngcontent-%COMP%]{width:80px;height:80px;border-radius:50%;object-fit:cover;border:3px solid #6c5ce7;margin:0 auto;display:block}.user-avatar-container[_ngcontent-%COMP%]   .influencer-badge[_ngcontent-%COMP%]{position:absolute;top:-5px;right:calc(50% - 45px);background:linear-gradient(135deg,gold,#ffb347);color:#fff;width:24px;height:24px;border-radius:50%;display:flex;align-items:center;justify-content:center;font-size:12px;border:2px solid white}.user-avatar-container[_ngcontent-%COMP%]   .influencer-badge[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:12px}.user-info[_ngcontent-%COMP%]{margin-bottom:16px}.user-info[_ngcontent-%COMP%]   .user-name[_ngcontent-%COMP%]{font-size:16px;font-weight:600;color:#1a1a1a;margin:0 0 4px}.user-info[_ngcontent-%COMP%]   .username[_ngcontent-%COMP%]{font-size:14px;color:#6c5ce7;margin:0 0 8px;font-weight:500}.user-info[_ngcontent-%COMP%]   .follower-count[_ngcontent-%COMP%]{font-size:12px;color:#666;margin:0 0 4px;font-weight:600}.user-info[_ngcontent-%COMP%]   .category-tag[_ngcontent-%COMP%]{font-size:11px;color:#6c5ce7;background:#6c5ce71a;padding:2px 8px;border-radius:12px;display:inline-block;margin:0 0 8px}.user-info[_ngcontent-%COMP%]   .followed-by[_ngcontent-%COMP%]{font-size:11px;color:#999;margin:0;line-height:1.3}.follow-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,#6c5ce7,#5a4fcf);color:#fff;border:none;padding:8px 16px;border-radius:20px;font-size:12px;font-weight:600;display:flex;align-items:center;gap:4px;margin:0 auto;cursor:pointer;transition:all .3s ease}.follow-btn[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 6px 20px #6c5ce74d}.follow-btn.following[_ngcontent-%COMP%]{background:linear-gradient(135deg,#00b894,#00a085)}.follow-btn.following[_ngcontent-%COMP%]:hover{box-shadow:0 6px 20px #00b8944d}.follow-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:14px}.empty-container[_ngcontent-%COMP%]{text-align:center;padding:60px 20px}.empty-container[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%]{font-size:64px;color:#ddd;margin-bottom:20px}.empty-container[_ngcontent-%COMP%]   .empty-title[_ngcontent-%COMP%]{font-size:20px;font-weight:600;color:#666;margin:0 0 8px}.empty-container[_ngcontent-%COMP%]   .empty-message[_ngcontent-%COMP%]{color:#999;margin:0}@media (max-width: 1200px){.users-slider[_ngcontent-%COMP%]   .user-card[_ngcontent-%COMP%]{flex:0 0 170px;width:170px}}@media (max-width: 768px){.users-slider-container[_ngcontent-%COMP%]{margin:0 -10px}.users-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]{width:35px;height:35px}.users-slider-container[_ngcontent-%COMP%]   .slider-nav.prev-btn[_ngcontent-%COMP%]{left:-15px}.users-slider-container[_ngcontent-%COMP%]   .slider-nav.next-btn[_ngcontent-%COMP%]{right:-15px}.users-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:16px}.users-slider-wrapper[_ngcontent-%COMP%]{padding:0 10px}.users-slider[_ngcontent-%COMP%]{gap:15px}.users-slider[_ngcontent-%COMP%]   .user-card[_ngcontent-%COMP%]{flex:0 0 160px;width:160px;padding:16px}.user-avatar-container[_ngcontent-%COMP%]   .user-avatar[_ngcontent-%COMP%]{width:70px;height:70px}}@media (max-width: 480px){.users-slider[_ngcontent-%COMP%]   .user-card[_ngcontent-%COMP%]{flex:0 0 150px;width:150px}}"]})}}return i})();const Dn=()=>[1,2,3];function Un(i,a){if(1&i){const t=n.RV6();n.j41(0,"div",11)(1,"button",12),n.bIt("click",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.toggleSectionLike())}),n.nrm(2,"ion-icon",13),n.j41(3,"span",14),n.EFF(4),n.k0s()(),n.j41(5,"button",15),n.bIt("click",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.openComments())}),n.nrm(6,"ion-icon",16),n.j41(7,"span",14),n.EFF(8),n.k0s()(),n.j41(9,"button",17),n.bIt("click",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.shareSection())}),n.nrm(10,"ion-icon",18),n.j41(11,"span",19),n.EFF(12,"Share"),n.k0s()(),n.j41(13,"button",20),n.bIt("click",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.toggleSectionBookmark())}),n.nrm(14,"ion-icon",13),n.k0s(),n.j41(15,"button",21),n.bIt("click",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.openMusicPlayer())}),n.nrm(16,"ion-icon",22),n.j41(17,"span",19),n.EFF(18,"Music"),n.k0s()()()}if(2&i){const t=n.XpG();n.R7$(),n.AVh("active",t.isSectionLiked),n.R7$(),n.Y8G("name",t.isSectionLiked?"heart":"heart-outline"),n.R7$(2),n.JRh(t.formatCount(t.sectionLikes)),n.R7$(4),n.JRh(t.formatCount(t.sectionComments)),n.R7$(5),n.AVh("active",t.isSectionBookmarked),n.R7$(),n.Y8G("name",t.isSectionBookmarked?"bookmark":"bookmark-outline")}}function Wn(i,a){1&i&&(n.j41(0,"div",26),n.nrm(1,"div",27),n.j41(2,"div",28),n.nrm(3,"div",29)(4,"div",30)(5,"div",31),n.j41(6,"div",32),n.nrm(7,"div",33)(8,"div",33),n.k0s()()())}function Jn(i,a){1&i&&(n.j41(0,"div",23)(1,"div",24),n.DNE(2,Wn,9,0,"div",25),n.k0s()()),2&i&&(n.R7$(2),n.Y8G("ngForOf",n.lJ4(1,Dn)))}function Hn(i,a){if(1&i){const t=n.RV6();n.j41(0,"div",34),n.nrm(1,"ion-icon",35),n.j41(2,"p",36),n.EFF(3),n.k0s(),n.j41(4,"button",37),n.bIt("click",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.onRetry())}),n.nrm(5,"ion-icon",38),n.EFF(6," Try Again "),n.k0s()()}if(2&i){const t=n.XpG();n.R7$(3),n.JRh(t.error)}}function Kn(i,a){1&i&&(n.j41(0,"div",65),n.nrm(1,"ion-icon",66),n.k0s())}function qn(i,a){if(1&i&&(n.j41(0,"span",67),n.EFF(1),n.k0s()),2&i){const t=a.$implicit;n.R7$(),n.SpI(" ",t," ")}}function Qn(i,a){if(1&i&&(n.j41(0,"span",68),n.EFF(1),n.k0s()),2&i){const t=n.XpG().$implicit;n.R7$(),n.SpI(" +",t.topBrands.length-2," ")}}function Zn(i,a){if(1&i){const t=n.RV6();n.j41(0,"div",47),n.bIt("click",function(){const e=n.eBV(t).$implicit,r=n.XpG(2);return n.Njj(r.onInfluencerClick(e))}),n.j41(1,"div",48),n.nrm(2,"img",49),n.DNE(3,Kn,2,0,"div",50),n.k0s(),n.j41(4,"div",51)(5,"h3",52),n.EFF(6),n.k0s(),n.j41(7,"p",53),n.EFF(8),n.k0s(),n.j41(9,"p",54),n.EFF(10),n.k0s(),n.j41(11,"div",55)(12,"div",56)(13,"span",57),n.EFF(14),n.k0s(),n.j41(15,"span",58),n.EFF(16,"Followers"),n.k0s()(),n.j41(17,"div",56)(18,"span",57),n.EFF(19),n.k0s(),n.j41(20,"span",58),n.EFF(21,"Engagement"),n.k0s()()(),n.j41(22,"div",59)(23,"span",60),n.EFF(24,"Works with:"),n.k0s(),n.j41(25,"div",61),n.DNE(26,qn,2,1,"span",62)(27,Qn,2,1,"span",63),n.k0s()()(),n.j41(28,"button",64),n.bIt("click",function(e){const r=n.eBV(t).$implicit,c=n.XpG(2);return n.Njj(c.onFollowInfluencer(r,e))}),n.j41(29,"span"),n.EFF(30),n.k0s(),n.nrm(31,"ion-icon",13),n.k0s()()}if(2&i){const t=a.$implicit,o=n.XpG(2);n.R7$(2),n.Y8G("src",t.avatar,n.B4B)("alt",t.fullName),n.R7$(),n.Y8G("ngIf",t.isVerified),n.R7$(3),n.JRh(t.fullName),n.R7$(2),n.SpI("@",t.username,""),n.R7$(2),n.JRh(t.category),n.R7$(4),n.JRh(o.formatFollowerCount(t.followerCount)),n.R7$(5),n.SpI("",t.engagementRate,"%"),n.R7$(7),n.Y8G("ngForOf",t.topBrands.slice(0,2)),n.R7$(),n.Y8G("ngIf",t.topBrands.length>2),n.R7$(),n.AVh("following",t.isFollowing),n.R7$(2),n.JRh(t.isFollowing?"Following":"Follow"),n.R7$(),n.Y8G("name",t.isFollowing?"checkmark":"add")}}function nt(i,a){if(1&i){const t=n.RV6();n.j41(0,"div",39)(1,"button",40),n.bIt("click",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.slidePrev())}),n.nrm(2,"ion-icon",41),n.k0s(),n.j41(3,"button",42),n.bIt("click",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.slideNext())}),n.nrm(4,"ion-icon",43),n.k0s(),n.j41(5,"div",44),n.bIt("mouseenter",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.pauseAutoSlide())})("mouseleave",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.resumeAutoSlide())}),n.j41(6,"div",45),n.DNE(7,Zn,32,14,"div",46),n.k0s()()()}if(2&i){const t=n.XpG();n.R7$(),n.Y8G("disabled",0===t.currentSlide),n.R7$(2),n.Y8G("disabled",t.currentSlide>=t.maxSlide),n.R7$(3),n.xc7("transform","translateX("+t.slideOffset+"px)"),n.R7$(),n.Y8G("ngForOf",t.topInfluencers)("ngForTrackBy",t.trackByInfluencerId)}}function tt(i,a){1&i&&(n.j41(0,"div",69),n.nrm(1,"ion-icon",70),n.j41(2,"h3",71),n.EFF(3,"No Influencers"),n.k0s(),n.j41(4,"p",72),n.EFF(5,"Check back later for top fashion influencers"),n.k0s()())}let S=(()=>{class i{constructor(t){this.router=t,this.topInfluencers=[],this.isLoading=!0,this.error=null,this.subscription=new f.yU,this.currentSlide=0,this.slideOffset=0,this.cardWidth=240,this.visibleCards=3,this.maxSlide=0,this.autoSlideDelay=6e3,this.isAutoSliding=!0,this.isPaused=!1,this.isSectionLiked=!1,this.isSectionBookmarked=!1,this.sectionLikes=512,this.sectionComments=234,this.isMobile=!1}ngOnInit(){this.loadTopInfluencers(),this.updateResponsiveSettings(),this.setupResizeListener(),this.checkMobileDevice()}ngOnDestroy(){this.subscription.unsubscribe(),this.stopAutoSlide()}loadTopInfluencers(){var t=this;return(0,s.A)(function*(){try{t.isLoading=!0,t.error=null,t.topInfluencers=[{id:"1",username:"fashionista_queen",fullName:"Priya Sharma",avatar:"https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=150&h=150&fit=crop&crop=face",followerCount:25e5,category:"High Fashion",isVerified:!0,isFollowing:!1,engagementRate:8.5,recentPosts:24,topBrands:["Gucci","Prada","Versace"]},{id:"2",username:"street_style_king",fullName:"Arjun Kapoor",avatar:"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",followerCount:18e5,category:"Streetwear",isVerified:!0,isFollowing:!1,engagementRate:12.3,recentPosts:18,topBrands:["Nike","Adidas","Supreme"]},{id:"3",username:"boho_goddess",fullName:"Ananya Singh",avatar:"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face",followerCount:12e5,category:"Boho Chic",isVerified:!0,isFollowing:!1,engagementRate:9.7,recentPosts:32,topBrands:["Free People","Anthropologie","Zara"]},{id:"4",username:"luxury_lifestyle",fullName:"Kavya Reddy",avatar:"https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face",followerCount:32e5,category:"Luxury",isVerified:!0,isFollowing:!1,engagementRate:6.8,recentPosts:15,topBrands:["Chanel","Dior","Louis Vuitton"]},{id:"5",username:"minimalist_maven",fullName:"Ravi Kumar",avatar:"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",followerCount:95e4,category:"Minimalist",isVerified:!0,isFollowing:!1,engagementRate:11.2,recentPosts:21,topBrands:["COS","Uniqlo","Everlane"]},{id:"6",username:"vintage_vibes",fullName:"Meera Patel",avatar:"https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face",followerCount:78e4,category:"Vintage",isVerified:!0,isFollowing:!1,engagementRate:13.5,recentPosts:28,topBrands:["Vintage Stores","Thrift Finds","Custom"]}],t.isLoading=!1,t.updateSliderOnInfluencersLoad()}catch(o){console.error("Error loading top influencers:",o),t.error="Failed to load top influencers",t.isLoading=!1}})()}onInfluencerClick(t){this.router.navigate(["/profile",t.username])}onFollowInfluencer(t,o){o.stopPropagation(),t.isFollowing=!t.isFollowing,t.isFollowing?t.followerCount++:t.followerCount--}formatFollowerCount(t){return t>=1e6?(t/1e6).toFixed(1)+"M":t>=1e3?(t/1e3).toFixed(1)+"K":t.toString()}onRetry(){this.loadTopInfluencers()}trackByInfluencerId(t,o){return o.id}startAutoSlide(){!this.isAutoSliding||this.isPaused||(this.stopAutoSlide(),this.autoSlideInterval=setInterval(()=>{!this.isPaused&&this.topInfluencers.length>this.visibleCards&&this.autoSlideNext()},this.autoSlideDelay))}stopAutoSlide(){this.autoSlideInterval&&(clearInterval(this.autoSlideInterval),this.autoSlideInterval=null)}autoSlideNext(){this.currentSlide>=this.maxSlide?this.currentSlide=0:this.currentSlide++,this.updateSlideOffset()}pauseAutoSlide(){this.isPaused=!0,this.stopAutoSlide()}resumeAutoSlide(){this.isPaused=!1,this.startAutoSlide()}updateResponsiveSettings(){const t=window.innerWidth;t<=480?(this.cardWidth=200,this.visibleCards=1):t<=768?(this.cardWidth=220,this.visibleCards=2):t<=1200?(this.cardWidth=240,this.visibleCards=2):(this.cardWidth=260,this.visibleCards=3),this.updateSliderLimits(),this.updateSlideOffset()}setupResizeListener(){window.addEventListener("resize",()=>{this.updateResponsiveSettings()})}updateSliderLimits(){this.maxSlide=Math.max(0,this.topInfluencers.length-this.visibleCards)}slidePrev(){this.currentSlide>0&&(this.currentSlide--,this.updateSlideOffset(),this.restartAutoSlideAfterInteraction())}slideNext(){this.currentSlide<this.maxSlide&&(this.currentSlide++,this.updateSlideOffset(),this.restartAutoSlideAfterInteraction())}updateSlideOffset(){this.slideOffset=-this.currentSlide*this.cardWidth}restartAutoSlideAfterInteraction(){this.stopAutoSlide(),setTimeout(()=>{this.startAutoSlide()},2e3)}updateSliderOnInfluencersLoad(){setTimeout(()=>{this.updateSliderLimits(),this.currentSlide=0,this.slideOffset=0,this.startAutoSlide()},100)}toggleSectionLike(){this.isSectionLiked=!this.isSectionLiked,this.isSectionLiked?this.sectionLikes++:this.sectionLikes--}toggleSectionBookmark(){this.isSectionBookmarked=!this.isSectionBookmarked}openComments(){console.log("Opening comments for top fashion influencers section")}shareSection(){navigator.share?navigator.share({title:"Top Fashion Influencers",text:"Follow the top fashion trendsetters!",url:window.location.href}):(navigator.clipboard.writeText(window.location.href),console.log("Link copied to clipboard"))}openMusicPlayer(){console.log("Opening music player for top fashion influencers")}formatCount(t){return t>=1e6?(t/1e6).toFixed(1)+"M":t>=1e3?(t/1e3).toFixed(1)+"K":t.toString()}checkMobileDevice(){this.isMobile=window.innerWidth<=768}static{this.\u0275fac=function(o){return new(o||i)(n.rXU(m.Ix))}}static{this.\u0275cmp=n.VBU({type:i,selectors:[["app-top-fashion-influencers"]],standalone:!0,features:[n.aNF],decls:13,vars:5,consts:[[1,"top-influencers-container"],["class","mobile-action-buttons",4,"ngIf"],[1,"section-header"],[1,"header-content"],[1,"section-title"],["name","star",1,"title-icon"],[1,"section-subtitle"],["class","loading-container",4,"ngIf"],["class","error-container",4,"ngIf"],["class","influencers-slider-container",4,"ngIf"],["class","empty-container",4,"ngIf"],[1,"mobile-action-buttons"],[1,"action-btn","like-btn",3,"click"],[3,"name"],[1,"action-count"],[1,"action-btn","comment-btn",3,"click"],["name","chatbubble-outline"],[1,"action-btn","share-btn",3,"click"],["name","arrow-redo-outline"],[1,"action-text"],[1,"action-btn","bookmark-btn",3,"click"],[1,"action-btn","music-btn",3,"click"],["name","musical-notes"],[1,"loading-container"],[1,"loading-grid"],["class","loading-influencer-card",4,"ngFor","ngForOf"],[1,"loading-influencer-card"],[1,"loading-avatar"],[1,"loading-content"],[1,"loading-line","short"],[1,"loading-line","medium"],[1,"loading-line","long"],[1,"loading-stats"],[1,"loading-stat"],[1,"error-container"],["name","alert-circle",1,"error-icon"],[1,"error-message"],[1,"retry-btn",3,"click"],["name","refresh"],[1,"influencers-slider-container"],[1,"slider-nav","prev-btn",3,"click","disabled"],["name","chevron-back"],[1,"slider-nav","next-btn",3,"click","disabled"],["name","chevron-forward"],[1,"influencers-slider-wrapper",3,"mouseenter","mouseleave"],[1,"influencers-slider"],["class","influencer-card",3,"click",4,"ngFor","ngForOf","ngForTrackBy"],[1,"influencer-card",3,"click"],[1,"influencer-avatar-container"],["loading","lazy",1,"influencer-avatar",3,"src","alt"],["class","verified-badge",4,"ngIf"],[1,"influencer-info"],[1,"influencer-name"],[1,"username"],[1,"category"],[1,"stats-container"],[1,"stat"],[1,"stat-value"],[1,"stat-label"],[1,"top-brands"],[1,"brands-label"],[1,"brands-list"],["class","brand-tag",4,"ngFor","ngForOf"],["class","more-brands",4,"ngIf"],[1,"follow-btn",3,"click"],[1,"verified-badge"],["name","checkmark"],[1,"brand-tag"],[1,"more-brands"],[1,"empty-container"],["name","star-outline",1,"empty-icon"],[1,"empty-title"],[1,"empty-message"]],template:function(o,e){1&o&&(n.j41(0,"div",0),n.DNE(1,Un,19,8,"div",1),n.j41(2,"div",2)(3,"div",3)(4,"h2",4),n.nrm(5,"ion-icon",5),n.EFF(6," Top Fashion Influencers "),n.k0s(),n.j41(7,"p",6),n.EFF(8,"Follow the trendsetters"),n.k0s()()(),n.DNE(9,Jn,3,2,"div",7)(10,Hn,7,1,"div",8)(11,nt,8,6,"div",9)(12,tt,6,0,"div",10),n.k0s()),2&o&&(n.R7$(),n.Y8G("ngIf",e.isMobile),n.R7$(8),n.Y8G("ngIf",e.isLoading),n.R7$(),n.Y8G("ngIf",e.error&&!e.isLoading),n.R7$(),n.Y8G("ngIf",!e.isLoading&&!e.error&&e.topInfluencers.length>0),n.R7$(),n.Y8G("ngIf",!e.isLoading&&!e.error&&0===e.topInfluencers.length))},dependencies:[d.MD,d.Sq,d.bT,g.bv,g.iq,_.Rl],styles:[".top-influencers-container[_ngcontent-%COMP%]{padding:20px;background:linear-gradient(135deg,#f8f9fa,#e9ecef);border-radius:16px;margin-bottom:24px;position:relative;max-width:675px}.mobile-action-buttons[_ngcontent-%COMP%]{position:absolute;right:15px;top:50%;transform:translateY(-50%);display:flex;flex-direction:column;gap:20px;z-index:10}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]{width:48px;height:48px;border-radius:50%;border:none;background:#ffd70033;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);color:gold;display:flex;flex-direction:column;align-items:center;justify-content:center;cursor:pointer;transition:all .3s ease;position:relative}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:24px;margin-bottom:2px}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   .action-count[_ngcontent-%COMP%], .mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]   .action-text[_ngcontent-%COMP%]{font-size:10px;font-weight:600;line-height:1}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]:hover{transform:scale(1.1);background:#ffd7004d}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active[_ngcontent-%COMP%]{background:#ffd700e6;color:#fff}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active.like-btn[_ngcontent-%COMP%]{background:#ff3040e6;color:#fff}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active.like-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_heartBeat .6s ease-in-out}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.active.bookmark-btn[_ngcontent-%COMP%]{background:#ffd700e6;color:#fff}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.like-btn.active[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:#fff}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.music-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,#ff3040,gold);color:#fff}.mobile-action-buttons[_ngcontent-%COMP%]   .action-btn.music-btn[_ngcontent-%COMP%]:hover{transform:scale(1.1) rotate(15deg)}@keyframes _ngcontent-%COMP%_heartBeat{0%{transform:scale(1)}25%{transform:scale(1.3)}50%{transform:scale(1.1)}75%{transform:scale(1.25)}to{transform:scale(1)}}@media (min-width: 769px){.mobile-action-buttons[_ngcontent-%COMP%]{display:none}}.section-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:24px}.section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]{flex:1}.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{font-size:24px;font-weight:700;color:#1a1a1a;margin:0 0 8px;display:flex;align-items:center;gap:12px}.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   .title-icon[_ngcontent-%COMP%]{font-size:28px;color:gold}.section-header[_ngcontent-%COMP%]   .section-subtitle[_ngcontent-%COMP%]{font-size:14px;color:#666;margin:0}.loading-container[_ngcontent-%COMP%]   .loading-grid[_ngcontent-%COMP%]{display:flex;gap:20px;overflow-x:auto;padding-bottom:8px}.loading-container[_ngcontent-%COMP%]   .loading-influencer-card[_ngcontent-%COMP%]{flex:0 0 220px;background:#ffffffb3;border-radius:16px;padding:20px}.loading-container[_ngcontent-%COMP%]   .loading-influencer-card[_ngcontent-%COMP%]   .loading-avatar[_ngcontent-%COMP%]{width:100px;height:100px;border-radius:50%;background:linear-gradient(90deg,#f0f0f0 25%,#e0e0e0,#f0f0f0 75%);background-size:200% 100%;animation:_ngcontent-%COMP%_loading 1.5s infinite;margin:0 auto 16px}.loading-container[_ngcontent-%COMP%]   .loading-influencer-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line[_ngcontent-%COMP%]{height:12px;border-radius:6px;background:linear-gradient(90deg,#f0f0f0 25%,#e0e0e0,#f0f0f0 75%);background-size:200% 100%;animation:_ngcontent-%COMP%_loading 1.5s infinite;margin-bottom:8px}.loading-container[_ngcontent-%COMP%]   .loading-influencer-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.short[_ngcontent-%COMP%]{width:60%}.loading-container[_ngcontent-%COMP%]   .loading-influencer-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.medium[_ngcontent-%COMP%]{width:80%}.loading-container[_ngcontent-%COMP%]   .loading-influencer-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.long[_ngcontent-%COMP%]{width:100%}.loading-container[_ngcontent-%COMP%]   .loading-influencer-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-stats[_ngcontent-%COMP%]{display:flex;gap:10px;margin-top:12px}.loading-container[_ngcontent-%COMP%]   .loading-influencer-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-stats[_ngcontent-%COMP%]   .loading-stat[_ngcontent-%COMP%]{flex:1;height:20px;border-radius:10px;background:linear-gradient(90deg,#f0f0f0 25%,#e0e0e0,#f0f0f0 75%);background-size:200% 100%;animation:_ngcontent-%COMP%_loading 1.5s infinite}@keyframes _ngcontent-%COMP%_loading{0%{background-position:200% 0}to{background-position:-200% 0}}.error-container[_ngcontent-%COMP%]{text-align:center;padding:40px 20px}.error-container[_ngcontent-%COMP%]   .error-icon[_ngcontent-%COMP%]{font-size:48px;color:#e74c3c;margin-bottom:16px}.error-container[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%]{color:#666;margin-bottom:20px;font-size:16px}.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,#e74c3c,#c0392b);color:#fff;border:none;padding:12px 24px;border-radius:25px;font-weight:600;display:flex;align-items:center;gap:8px;margin:0 auto;cursor:pointer;transition:all .3s ease}.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 8px 25px #e74c3c4d}.influencers-slider-container[_ngcontent-%COMP%]{position:relative;margin:0 -20px}.influencers-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]{position:absolute;top:50%;transform:translateY(-50%);z-index:10;background:#000000b3;color:#fff;border:none;width:40px;height:40px;border-radius:50%;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all .3s ease}.influencers-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]:hover:not(:disabled){background:#000000e6;transform:translateY(-50%) scale(1.1)}.influencers-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]:disabled{opacity:.3;cursor:not-allowed}.influencers-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px}.influencers-slider-container[_ngcontent-%COMP%]   .slider-nav.prev-btn[_ngcontent-%COMP%]{left:-20px}.influencers-slider-container[_ngcontent-%COMP%]   .slider-nav.next-btn[_ngcontent-%COMP%]{right:-20px}.influencers-slider-wrapper[_ngcontent-%COMP%]{overflow:hidden;padding:0 20px}.influencers-slider[_ngcontent-%COMP%]{display:flex;transition:transform .4s cubic-bezier(.25,.46,.45,.94);gap:20px}.influencers-slider[_ngcontent-%COMP%]   .influencer-card[_ngcontent-%COMP%]{flex:0 0 220px;width:220px}.influencer-card[_ngcontent-%COMP%]{background:#fff;border-radius:16px;padding:24px;text-align:center;box-shadow:0 4px 20px #00000014;transition:all .3s ease;cursor:pointer}.influencer-card[_ngcontent-%COMP%]:hover{transform:translateY(-8px);box-shadow:0 12px 40px #00000026}.influencer-avatar-container[_ngcontent-%COMP%]{position:relative;margin-bottom:20px}.influencer-avatar-container[_ngcontent-%COMP%]   .influencer-avatar[_ngcontent-%COMP%]{width:100px;height:100px;border-radius:50%;object-fit:cover;border:4px solid #ffd700;margin:0 auto;display:block}.influencer-avatar-container[_ngcontent-%COMP%]   .verified-badge[_ngcontent-%COMP%]{position:absolute;top:-5px;right:calc(50% - 55px);background:linear-gradient(135deg,#00b894,#00a085);color:#fff;width:28px;height:28px;border-radius:50%;display:flex;align-items:center;justify-content:center;font-size:14px;border:3px solid white}.influencer-avatar-container[_ngcontent-%COMP%]   .verified-badge[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:14px}.influencer-info[_ngcontent-%COMP%]{margin-bottom:20px}.influencer-info[_ngcontent-%COMP%]   .influencer-name[_ngcontent-%COMP%]{font-size:18px;font-weight:700;color:#1a1a1a;margin:0 0 4px}.influencer-info[_ngcontent-%COMP%]   .username[_ngcontent-%COMP%]{font-size:14px;color:#6c5ce7;margin:0 0 8px;font-weight:500}.influencer-info[_ngcontent-%COMP%]   .category[_ngcontent-%COMP%]{font-size:12px;color:gold;background:#ffd7001a;padding:4px 12px;border-radius:12px;display:inline-block;margin:0 0 16px;font-weight:600}.stats-container[_ngcontent-%COMP%]{display:flex;gap:16px;margin-bottom:16px}.stats-container[_ngcontent-%COMP%]   .stat[_ngcontent-%COMP%]{flex:1;text-align:center}.stats-container[_ngcontent-%COMP%]   .stat[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%]{display:block;font-size:16px;font-weight:700;color:#1a1a1a;margin-bottom:2px}.stats-container[_ngcontent-%COMP%]   .stat[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%]{font-size:11px;color:#666;text-transform:uppercase;letter-spacing:.5px}.top-brands[_ngcontent-%COMP%]   .brands-label[_ngcontent-%COMP%]{font-size:11px;color:#666;display:block;margin-bottom:6px}.top-brands[_ngcontent-%COMP%]   .brands-list[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:4px;justify-content:center}.top-brands[_ngcontent-%COMP%]   .brands-list[_ngcontent-%COMP%]   .brand-tag[_ngcontent-%COMP%]{font-size:10px;background:#6c5ce71a;color:#6c5ce7;padding:2px 6px;border-radius:8px;font-weight:500}.top-brands[_ngcontent-%COMP%]   .brands-list[_ngcontent-%COMP%]   .more-brands[_ngcontent-%COMP%]{font-size:10px;color:#999;font-weight:500}.follow-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,gold,#ffb347);color:#1a1a1a;border:none;padding:10px 20px;border-radius:25px;font-size:13px;font-weight:700;display:flex;align-items:center;gap:6px;margin:0 auto;cursor:pointer;transition:all .3s ease}.follow-btn[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 8px 25px #ffd70066}.follow-btn.following[_ngcontent-%COMP%]{background:linear-gradient(135deg,#00b894,#00a085);color:#fff}.follow-btn.following[_ngcontent-%COMP%]:hover{box-shadow:0 8px 25px #00b89466}.follow-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:16px}.empty-container[_ngcontent-%COMP%]{text-align:center;padding:60px 20px}.empty-container[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%]{font-size:64px;color:#ddd;margin-bottom:20px}.empty-container[_ngcontent-%COMP%]   .empty-title[_ngcontent-%COMP%]{font-size:20px;font-weight:600;color:#666;margin:0 0 8px}.empty-container[_ngcontent-%COMP%]   .empty-message[_ngcontent-%COMP%]{color:#999;margin:0}@media (max-width: 1200px){.influencers-slider[_ngcontent-%COMP%]   .influencer-card[_ngcontent-%COMP%]{flex:0 0 200px;width:200px;padding:20px}}@media (max-width: 768px){.influencers-slider-container[_ngcontent-%COMP%]{margin:0 -10px}.influencers-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]{width:35px;height:35px}.influencers-slider-container[_ngcontent-%COMP%]   .slider-nav.prev-btn[_ngcontent-%COMP%]{left:-15px}.influencers-slider-container[_ngcontent-%COMP%]   .slider-nav.next-btn[_ngcontent-%COMP%]{right:-15px}.influencers-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:16px}.influencers-slider-wrapper[_ngcontent-%COMP%]{padding:0 10px}.influencers-slider[_ngcontent-%COMP%]{gap:15px}.influencers-slider[_ngcontent-%COMP%]   .influencer-card[_ngcontent-%COMP%]{flex:0 0 180px;width:180px;padding:16px}.influencer-avatar-container[_ngcontent-%COMP%]   .influencer-avatar[_ngcontent-%COMP%]{width:80px;height:80px}}@media (max-width: 480px){.influencers-slider[_ngcontent-%COMP%]   .influencer-card[_ngcontent-%COMP%]{flex:0 0 170px;width:170px}}"]})}}return i})();const et=()=>[1,2,3,4];function ot(i,a){1&i&&(n.j41(0,"div",13),n.nrm(1,"div",14),n.j41(2,"div",15),n.nrm(3,"div",16)(4,"div",17)(5,"div",18),n.k0s()())}function it(i,a){1&i&&(n.j41(0,"div",10)(1,"div",11),n.DNE(2,ot,6,0,"div",12),n.k0s()()),2&i&&(n.R7$(2),n.Y8G("ngForOf",n.lJ4(1,et)))}function rt(i,a){if(1&i){const t=n.RV6();n.j41(0,"div",19),n.nrm(1,"ion-icon",20),n.j41(2,"p",21),n.EFF(3),n.k0s(),n.j41(4,"button",22),n.bIt("click",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.onRetry())}),n.nrm(5,"ion-icon",23),n.EFF(6," Try Again "),n.k0s()()}if(2&i){const t=n.XpG();n.R7$(3),n.JRh(t.error)}}function at(i,a){1&i&&(n.j41(0,"div",46),n.nrm(1,"ion-icon",47),n.j41(2,"span"),n.EFF(3,"Trending"),n.k0s()())}function ct(i,a){if(1&i&&(n.j41(0,"div",48),n.EFF(1),n.k0s()),2&i){const t=n.XpG().$implicit;n.R7$(),n.SpI(" ",t.discount,"% OFF ")}}function st(i,a){if(1&i&&(n.j41(0,"span",49),n.EFF(1),n.k0s()),2&i){const t=a.$implicit;n.R7$(),n.SpI(" ",t," ")}}function dt(i,a){if(1&i&&(n.j41(0,"span",50),n.EFF(1),n.k0s()),2&i){const t=n.XpG().$implicit;n.R7$(),n.SpI(" +",t.subcategories.length-3," ")}}function lt(i,a){if(1&i){const t=n.RV6();n.j41(0,"div",32),n.bIt("click",function(){const e=n.eBV(t).$implicit,r=n.XpG(2);return n.Njj(r.onCategoryClick(e))}),n.j41(1,"div",33),n.nrm(2,"img",34),n.DNE(3,at,4,0,"div",35)(4,ct,2,1,"div",36),n.j41(5,"div",37),n.nrm(6,"ion-icon",38),n.k0s()(),n.j41(7,"div",39)(8,"h3",40),n.EFF(9),n.k0s(),n.j41(10,"p",41),n.EFF(11),n.k0s(),n.j41(12,"p",42),n.EFF(13),n.k0s(),n.j41(14,"div",43),n.DNE(15,st,2,1,"span",44)(16,dt,2,1,"span",45),n.k0s()()()}if(2&i){const t=a.$implicit,o=n.XpG(2);n.AVh("trending",t.trending),n.R7$(2),n.Y8G("src",t.image,n.B4B)("alt",t.name),n.R7$(),n.Y8G("ngIf",t.trending),n.R7$(),n.Y8G("ngIf",t.discount),n.R7$(5),n.JRh(t.name),n.R7$(2),n.JRh(t.description),n.R7$(2),n.SpI("",o.formatProductCount(t.productCount)," Products"),n.R7$(2),n.Y8G("ngForOf",t.subcategories.slice(0,3)),n.R7$(),n.Y8G("ngIf",t.subcategories.length>3)}}function gt(i,a){if(1&i){const t=n.RV6();n.j41(0,"div",24)(1,"button",25),n.bIt("click",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.slidePrev())}),n.nrm(2,"ion-icon",26),n.k0s(),n.j41(3,"button",27),n.bIt("click",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.slideNext())}),n.nrm(4,"ion-icon",28),n.k0s(),n.j41(5,"div",29),n.bIt("mouseenter",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.pauseAutoSlide())})("mouseleave",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.resumeAutoSlide())}),n.j41(6,"div",30),n.DNE(7,lt,17,11,"div",31),n.k0s()()()}if(2&i){const t=n.XpG();n.R7$(),n.Y8G("disabled",0===t.currentSlide),n.R7$(2),n.Y8G("disabled",t.currentSlide>=t.maxSlide),n.R7$(3),n.xc7("transform","translateX("+t.slideOffset+"px)"),n.R7$(),n.Y8G("ngForOf",t.categories)("ngForTrackBy",t.trackByCategoryId)}}function pt(i,a){1&i&&(n.j41(0,"div",51),n.nrm(1,"ion-icon",52),n.j41(2,"h3",53),n.EFF(3,"No Categories"),n.k0s(),n.j41(4,"p",54),n.EFF(5,"Check back later for product categories"),n.k0s()())}let mt=(()=>{class i{constructor(t){this.router=t,this.categories=[],this.isLoading=!0,this.error=null,this.subscription=new f.yU,this.currentSlide=0,this.slideOffset=0,this.cardWidth=200,this.visibleCards=4,this.maxSlide=0,this.autoSlideDelay=4500,this.isAutoSliding=!0,this.isPaused=!1}ngOnInit(){this.loadCategories(),this.updateResponsiveSettings(),this.setupResizeListener()}ngOnDestroy(){this.subscription.unsubscribe(),this.stopAutoSlide()}loadCategories(){var t=this;return(0,s.A)(function*(){try{t.isLoading=!0,t.error=null,t.categories=[{id:"1",name:"Women's Fashion",image:"https://images.unsplash.com/photo-1483985988355-763728e1935b?w=300&h=200&fit=crop",productCount:15420,description:"Trendy outfits for every occasion",trending:!0,discount:30,subcategories:["Dresses","Tops","Bottoms","Accessories"]},{id:"2",name:"Men's Fashion",image:"https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=300&h=200&fit=crop",productCount:12850,description:"Stylish clothing for modern men",trending:!0,discount:25,subcategories:["Shirts","Pants","Jackets","Shoes"]},{id:"3",name:"Footwear",image:"https://images.unsplash.com/photo-1549298916-b41d501d3772?w=300&h=200&fit=crop",productCount:8960,description:"Step up your shoe game",trending:!1,discount:20,subcategories:["Sneakers","Formal","Casual","Sports"]},{id:"4",name:"Accessories",image:"https://images.unsplash.com/photo-1506629905607-d9c36e0a3f90?w=300&h=200&fit=crop",productCount:6750,description:"Complete your look",trending:!0,subcategories:["Bags","Jewelry","Watches","Belts"]},{id:"5",name:"Kids Fashion",image:"https://images.unsplash.com/photo-1503944583220-79d8926ad5e2?w=300&h=200&fit=crop",productCount:4320,description:"Adorable styles for little ones",trending:!1,discount:35,subcategories:["Boys","Girls","Baby","Toys"]},{id:"6",name:"Sports & Fitness",image:"https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=300&h=200&fit=crop",productCount:5680,description:"Gear up for your workout",trending:!0,subcategories:["Activewear","Equipment","Shoes","Supplements"]},{id:"7",name:"Beauty & Personal Care",image:"https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=300&h=200&fit=crop",productCount:7890,description:"Look and feel your best",trending:!1,discount:15,subcategories:["Skincare","Makeup","Haircare","Fragrance"]},{id:"8",name:"Home & Living",image:"https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=300&h=200&fit=crop",productCount:3450,description:"Style your space",trending:!1,subcategories:["Decor","Furniture","Kitchen","Bedding"]}],t.isLoading=!1,t.updateSliderOnCategoriesLoad()}catch(o){console.error("Error loading categories:",o),t.error="Failed to load categories",t.isLoading=!1}})()}onCategoryClick(t){this.router.navigate(["/category",t.id])}formatProductCount(t){return t>=1e3?(t/1e3).toFixed(1)+"K":t.toString()}onRetry(){this.loadCategories()}trackByCategoryId(t,o){return o.id}startAutoSlide(){!this.isAutoSliding||this.isPaused||(this.stopAutoSlide(),this.autoSlideInterval=setInterval(()=>{!this.isPaused&&this.categories.length>this.visibleCards&&this.autoSlideNext()},this.autoSlideDelay))}stopAutoSlide(){this.autoSlideInterval&&(clearInterval(this.autoSlideInterval),this.autoSlideInterval=null)}autoSlideNext(){this.currentSlide>=this.maxSlide?this.currentSlide=0:this.currentSlide++,this.updateSlideOffset()}pauseAutoSlide(){this.isPaused=!0,this.stopAutoSlide()}resumeAutoSlide(){this.isPaused=!1,this.startAutoSlide()}updateResponsiveSettings(){const t=window.innerWidth;t<=480?(this.cardWidth=160,this.visibleCards=1):t<=768?(this.cardWidth=180,this.visibleCards=2):t<=1200?(this.cardWidth=200,this.visibleCards=3):(this.cardWidth=220,this.visibleCards=4),this.updateSliderLimits(),this.updateSlideOffset()}setupResizeListener(){window.addEventListener("resize",()=>{this.updateResponsiveSettings()})}updateSliderLimits(){this.maxSlide=Math.max(0,this.categories.length-this.visibleCards)}slidePrev(){this.currentSlide>0&&(this.currentSlide--,this.updateSlideOffset(),this.restartAutoSlideAfterInteraction())}slideNext(){this.currentSlide<this.maxSlide&&(this.currentSlide++,this.updateSlideOffset(),this.restartAutoSlideAfterInteraction())}updateSlideOffset(){this.slideOffset=-this.currentSlide*this.cardWidth}restartAutoSlideAfterInteraction(){this.stopAutoSlide(),setTimeout(()=>{this.startAutoSlide()},2e3)}updateSliderOnCategoriesLoad(){setTimeout(()=>{this.updateSliderLimits(),this.currentSlide=0,this.slideOffset=0,this.startAutoSlide()},100)}static{this.\u0275fac=function(o){return new(o||i)(n.rXU(m.Ix))}}static{this.\u0275cmp=n.VBU({type:i,selectors:[["app-shop-by-category"]],standalone:!0,features:[n.aNF],decls:12,vars:4,consts:[[1,"shop-categories-container"],[1,"section-header"],[1,"header-content"],[1,"section-title"],["name","grid",1,"title-icon"],[1,"section-subtitle"],["class","loading-container",4,"ngIf"],["class","error-container",4,"ngIf"],["class","categories-slider-container",4,"ngIf"],["class","empty-container",4,"ngIf"],[1,"loading-container"],[1,"loading-grid"],["class","loading-category-card",4,"ngFor","ngForOf"],[1,"loading-category-card"],[1,"loading-image"],[1,"loading-content"],[1,"loading-line","short"],[1,"loading-line","medium"],[1,"loading-line","long"],[1,"error-container"],["name","alert-circle",1,"error-icon"],[1,"error-message"],[1,"retry-btn",3,"click"],["name","refresh"],[1,"categories-slider-container"],[1,"slider-nav","prev-btn",3,"click","disabled"],["name","chevron-back"],[1,"slider-nav","next-btn",3,"click","disabled"],["name","chevron-forward"],[1,"categories-slider-wrapper",3,"mouseenter","mouseleave"],[1,"categories-slider"],["class","category-card",3,"trending","click",4,"ngFor","ngForOf","ngForTrackBy"],[1,"category-card",3,"click"],[1,"category-image-container"],["loading","lazy",1,"category-image",3,"src","alt"],["class","trending-badge",4,"ngIf"],["class","discount-badge",4,"ngIf"],[1,"category-overlay"],["name","arrow-forward",1,"explore-icon"],[1,"category-info"],[1,"category-name"],[1,"category-description"],[1,"product-count"],[1,"subcategories"],["class","subcategory-tag",4,"ngFor","ngForOf"],["class","more-subcategories",4,"ngIf"],[1,"trending-badge"],["name","trending-up"],[1,"discount-badge"],[1,"subcategory-tag"],[1,"more-subcategories"],[1,"empty-container"],["name","grid-outline",1,"empty-icon"],[1,"empty-title"],[1,"empty-message"]],template:function(o,e){1&o&&(n.j41(0,"div",0)(1,"div",1)(2,"div",2)(3,"h2",3),n.nrm(4,"ion-icon",4),n.EFF(5," Shop by Category "),n.k0s(),n.j41(6,"p",5),n.EFF(7,"Explore our collections"),n.k0s()()(),n.DNE(8,it,3,2,"div",6)(9,rt,7,1,"div",7)(10,gt,8,6,"div",8)(11,pt,6,0,"div",9),n.k0s()),2&o&&(n.R7$(8),n.Y8G("ngIf",e.isLoading),n.R7$(),n.Y8G("ngIf",e.error&&!e.isLoading),n.R7$(),n.Y8G("ngIf",!e.isLoading&&!e.error&&e.categories.length>0),n.R7$(),n.Y8G("ngIf",!e.isLoading&&!e.error&&0===e.categories.length))},dependencies:[d.MD,d.Sq,d.bT,g.bv,g.iq,_.Rl],styles:[".shop-categories-container[_ngcontent-%COMP%]{padding:20px;background:linear-gradient(135deg,#f8f9fa,#e9ecef);border-radius:16px;margin-bottom:24px}.section-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:24px}.section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]{flex:1}.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{font-size:24px;font-weight:700;color:#1a1a1a;margin:0 0 8px;display:flex;align-items:center;gap:12px}.section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   .title-icon[_ngcontent-%COMP%]{font-size:28px;color:#6c5ce7}.section-header[_ngcontent-%COMP%]   .section-subtitle[_ngcontent-%COMP%]{font-size:14px;color:#666;margin:0}.loading-container[_ngcontent-%COMP%]   .loading-grid[_ngcontent-%COMP%]{display:flex;gap:20px;overflow-x:auto;padding-bottom:8px}.loading-container[_ngcontent-%COMP%]   .loading-category-card[_ngcontent-%COMP%]{flex:0 0 180px;background:#ffffffb3;border-radius:16px;overflow:hidden}.loading-container[_ngcontent-%COMP%]   .loading-category-card[_ngcontent-%COMP%]   .loading-image[_ngcontent-%COMP%]{width:100%;height:120px;background:linear-gradient(90deg,#f0f0f0 25%,#e0e0e0,#f0f0f0 75%);background-size:200% 100%;animation:_ngcontent-%COMP%_loading 1.5s infinite}.loading-container[_ngcontent-%COMP%]   .loading-category-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]{padding:16px}.loading-container[_ngcontent-%COMP%]   .loading-category-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line[_ngcontent-%COMP%]{height:12px;border-radius:6px;background:linear-gradient(90deg,#f0f0f0 25%,#e0e0e0,#f0f0f0 75%);background-size:200% 100%;animation:_ngcontent-%COMP%_loading 1.5s infinite;margin-bottom:8px}.loading-container[_ngcontent-%COMP%]   .loading-category-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.short[_ngcontent-%COMP%]{width:60%}.loading-container[_ngcontent-%COMP%]   .loading-category-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.medium[_ngcontent-%COMP%]{width:80%}.loading-container[_ngcontent-%COMP%]   .loading-category-card[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%]   .loading-line.long[_ngcontent-%COMP%]{width:100%}@keyframes _ngcontent-%COMP%_loading{0%{background-position:200% 0}to{background-position:-200% 0}}.error-container[_ngcontent-%COMP%]{text-align:center;padding:40px 20px}.error-container[_ngcontent-%COMP%]   .error-icon[_ngcontent-%COMP%]{font-size:48px;color:#e74c3c;margin-bottom:16px}.error-container[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%]{color:#666;margin-bottom:20px;font-size:16px}.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,#e74c3c,#c0392b);color:#fff;border:none;padding:12px 24px;border-radius:25px;font-weight:600;display:flex;align-items:center;gap:8px;margin:0 auto;cursor:pointer;transition:all .3s ease}.error-container[_ngcontent-%COMP%]   .retry-btn[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 8px 25px #e74c3c4d}.categories-slider-container[_ngcontent-%COMP%]{position:relative;margin:0 -20px}.categories-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]{position:absolute;top:50%;transform:translateY(-50%);z-index:10;background:#000000b3;color:#fff;border:none;width:40px;height:40px;border-radius:50%;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all .3s ease}.categories-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]:hover:not(:disabled){background:#000000e6;transform:translateY(-50%) scale(1.1)}.categories-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]:disabled{opacity:.3;cursor:not-allowed}.categories-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:18px}.categories-slider-container[_ngcontent-%COMP%]   .slider-nav.prev-btn[_ngcontent-%COMP%]{left:-20px}.categories-slider-container[_ngcontent-%COMP%]   .slider-nav.next-btn[_ngcontent-%COMP%]{right:-20px}.categories-slider-wrapper[_ngcontent-%COMP%]{overflow:hidden;padding:0 20px}.categories-slider[_ngcontent-%COMP%]{display:flex;transition:transform .4s cubic-bezier(.25,.46,.45,.94);gap:20px}.categories-slider[_ngcontent-%COMP%]   .category-card[_ngcontent-%COMP%]{flex:0 0 180px;width:180px}.category-card[_ngcontent-%COMP%]{background:#fff;border-radius:16px;overflow:hidden;box-shadow:0 4px 20px #00000014;transition:all .3s ease;cursor:pointer}.category-card[_ngcontent-%COMP%]:hover{transform:translateY(-8px);box-shadow:0 12px 40px #00000026}.category-card[_ngcontent-%COMP%]:hover   .category-overlay[_ngcontent-%COMP%]{opacity:1}.category-card[_ngcontent-%COMP%]:hover   .category-image[_ngcontent-%COMP%]{transform:scale(1.1)}.category-card.trending[_ngcontent-%COMP%]{border:2px solid #ff6b6b}.category-card.trending[_ngcontent-%COMP%]   .category-name[_ngcontent-%COMP%]{color:#ff6b6b}.category-image-container[_ngcontent-%COMP%]{position:relative;height:120px;overflow:hidden}.category-image-container[_ngcontent-%COMP%]   .category-image[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover;transition:transform .3s ease}.category-image-container[_ngcontent-%COMP%]   .trending-badge[_ngcontent-%COMP%]{position:absolute;top:8px;left:8px;background:linear-gradient(135deg,#ff6b6b,#ee5a52);color:#fff;padding:4px 8px;border-radius:12px;font-size:10px;font-weight:600;display:flex;align-items:center;gap:4px}.category-image-container[_ngcontent-%COMP%]   .trending-badge[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:12px}.category-image-container[_ngcontent-%COMP%]   .discount-badge[_ngcontent-%COMP%]{position:absolute;top:8px;right:8px;background:linear-gradient(135deg,#00b894,#00a085);color:#fff;padding:4px 8px;border-radius:12px;font-size:10px;font-weight:700}.category-image-container[_ngcontent-%COMP%]   .category-overlay[_ngcontent-%COMP%]{position:absolute;inset:0;background:#0006;display:flex;align-items:center;justify-content:center;opacity:0;transition:opacity .3s ease}.category-image-container[_ngcontent-%COMP%]   .category-overlay[_ngcontent-%COMP%]   .explore-icon[_ngcontent-%COMP%]{font-size:24px;color:#fff}.category-info[_ngcontent-%COMP%]{padding:16px}.category-info[_ngcontent-%COMP%]   .category-name[_ngcontent-%COMP%]{font-size:16px;font-weight:700;color:#1a1a1a;margin:0 0 6px;line-height:1.2}.category-info[_ngcontent-%COMP%]   .category-description[_ngcontent-%COMP%]{font-size:12px;color:#666;margin:0 0 8px;line-height:1.3}.category-info[_ngcontent-%COMP%]   .product-count[_ngcontent-%COMP%]{font-size:11px;color:#6c5ce7;font-weight:600;margin:0 0 12px}.subcategories[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:4px}.subcategories[_ngcontent-%COMP%]   .subcategory-tag[_ngcontent-%COMP%]{font-size:9px;background:#6c5ce71a;color:#6c5ce7;padding:2px 6px;border-radius:8px;font-weight:500}.subcategories[_ngcontent-%COMP%]   .more-subcategories[_ngcontent-%COMP%]{font-size:9px;color:#999;font-weight:500}.empty-container[_ngcontent-%COMP%]{text-align:center;padding:60px 20px}.empty-container[_ngcontent-%COMP%]   .empty-icon[_ngcontent-%COMP%]{font-size:64px;color:#ddd;margin-bottom:20px}.empty-container[_ngcontent-%COMP%]   .empty-title[_ngcontent-%COMP%]{font-size:20px;font-weight:600;color:#666;margin:0 0 8px}.empty-container[_ngcontent-%COMP%]   .empty-message[_ngcontent-%COMP%]{color:#999;margin:0}@media (max-width: 1200px){.categories-slider[_ngcontent-%COMP%]   .category-card[_ngcontent-%COMP%]{flex:0 0 170px;width:170px}}@media (max-width: 768px){.categories-slider-container[_ngcontent-%COMP%]{margin:0 -10px}.categories-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]{width:35px;height:35px}.categories-slider-container[_ngcontent-%COMP%]   .slider-nav.prev-btn[_ngcontent-%COMP%]{left:-15px}.categories-slider-container[_ngcontent-%COMP%]   .slider-nav.next-btn[_ngcontent-%COMP%]{right:-15px}.categories-slider-container[_ngcontent-%COMP%]   .slider-nav[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:16px}.categories-slider-wrapper[_ngcontent-%COMP%]{padding:0 10px}.categories-slider[_ngcontent-%COMP%]{gap:15px}.categories-slider[_ngcontent-%COMP%]   .category-card[_ngcontent-%COMP%]{flex:0 0 160px;width:160px}.category-image-container[_ngcontent-%COMP%]{height:100px}.category-info[_ngcontent-%COMP%]{padding:12px}}@media (max-width: 480px){.categories-slider[_ngcontent-%COMP%]   .category-card[_ngcontent-%COMP%]{flex:0 0 150px;width:150px}}"]})}}return i})();var ut=l(7359);let ft=(()=>{class i{constructor(t,o){this.productService=t,this.router=o,this.suggestedUsers=[],this.trendingProducts=[],this.topInfluencers=[],this.categories=[]}ngOnInit(){this.loadSuggestedUsers(),this.loadTrendingProducts(),this.loadTopInfluencers(),this.loadCategories()}loadSuggestedUsers(){this.suggestedUsers=[{id:"1",username:"fashionista_maya",fullName:"Maya Chen",avatar:"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150",followedBy:"Followed by 12 others",isFollowing:!1},{id:"2",username:"style_guru_alex",fullName:"Alex Rodriguez",avatar:"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150",followedBy:"Followed by 8 others",isFollowing:!1}]}loadTrendingProducts(){this.trendingProducts=[]}loadTopInfluencers(){this.topInfluencers=[{id:"1",username:"fashion_queen",fullName:"Priya Sharma",avatar:"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150",followersCount:25e3,postsCount:156,engagement:8.5,isFollowing:!1},{id:"2",username:"style_maven",fullName:"Kavya Reddy",avatar:"https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150",followersCount:18e3,postsCount:89,engagement:12.3,isFollowing:!0}]}loadCategories(){this.categories=[{id:"1",name:"Women",slug:"women",image:"https://images.unsplash.com/photo-1483985988355-763728e1935b?w=400"},{id:"2",name:"Men",slug:"men",image:"https://images.unsplash.com/photo-1516257984-b1b4d707412e?w=400"},{id:"3",name:"Accessories",slug:"accessories",image:"https://images.unsplash.com/photo-1506629905607-d405b7a30db9?w=400"},{id:"4",name:"Footwear",slug:"footwear",image:"https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400"}]}formatFollowerCount(t){return t>=1e3?(t/1e3).toFixed(1)+"k":t.toString()}followUser(t){const o=this.suggestedUsers.find(e=>e.id===t);o&&(o.isFollowing=!o.isFollowing)}followInfluencer(t){const o=this.topInfluencers.find(e=>e.id===t);o&&(o.isFollowing=!o.isFollowing)}quickBuy(t){console.log("Quick buy product:",t)}browseCategory(t){console.log("Browse category:",t),this.router.navigate(["/category",t])}static{this.\u0275fac=function(o){return new(o||i)(n.rXU(ut.b),n.rXU(m.Ix))}}static{this.\u0275cmp=n.VBU({type:i,selectors:[["app-sidebar"]],standalone:!0,features:[n.aNF],decls:7,vars:0,consts:[[1,"sidebar"]],template:function(o,e){1&o&&(n.j41(0,"aside",0),n.nrm(1,"app-trending-products")(2,"app-featured-brands")(3,"app-new-arrivals")(4,"app-suggested-for-you")(5,"app-top-fashion-influencers")(6,"app-shop-by-category"),n.k0s())},dependencies:[d.MD,m.iI,g.bv,v,k,w,y,S,mt],styles:['@charset "UTF-8";.sidebar[_ngcontent-%COMP%]{position:sticky;top:80px;height:-moz-fit-content;height:fit-content;display:flex;flex-direction:column;gap:20px;max-height:calc(100vh - 100px);overflow-y:auto;padding-right:8px}.sidebar[_ngcontent-%COMP%]::-webkit-scrollbar{width:6px}.sidebar[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:#f1f1f1;border-radius:3px}.sidebar[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#c1c1c1;border-radius:3px}.sidebar[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#a8a8a8}app-trending-products[_ngcontent-%COMP%], app-featured-brands[_ngcontent-%COMP%], app-new-arrivals[_ngcontent-%COMP%], app-suggested-for-you[_ngcontent-%COMP%], app-top-fashion-influencers[_ngcontent-%COMP%], app-shop-by-category[_ngcontent-%COMP%]{width:100%;display:block;margin-bottom:16px}app-trending-products[_ngcontent-%COMP%]     .component-container, app-featured-brands[_ngcontent-%COMP%]     .component-container, app-new-arrivals[_ngcontent-%COMP%]     .component-container, app-suggested-for-you[_ngcontent-%COMP%]     .component-container, app-top-fashion-influencers[_ngcontent-%COMP%]     .component-container, app-shop-by-category[_ngcontent-%COMP%]     .component-container{background:#fff;border:1px solid #dbdbdb;border-radius:8px;overflow:hidden}app-trending-products[_ngcontent-%COMP%]     .component-container .component-header, app-featured-brands[_ngcontent-%COMP%]     .component-container .component-header, app-new-arrivals[_ngcontent-%COMP%]     .component-container .component-header, app-suggested-for-you[_ngcontent-%COMP%]     .component-container .component-header, app-top-fashion-influencers[_ngcontent-%COMP%]     .component-container .component-header, app-shop-by-category[_ngcontent-%COMP%]     .component-container .component-header{padding:16px;border-bottom:1px solid #efefef}app-trending-products[_ngcontent-%COMP%]     .component-container .component-header h3, app-featured-brands[_ngcontent-%COMP%]     .component-container .component-header h3, app-new-arrivals[_ngcontent-%COMP%]     .component-container .component-header h3, app-suggested-for-you[_ngcontent-%COMP%]     .component-container .component-header h3, app-top-fashion-influencers[_ngcontent-%COMP%]     .component-container .component-header h3, app-shop-by-category[_ngcontent-%COMP%]     .component-container .component-header h3{margin:0;font-size:16px;font-weight:600;color:#262626}app-trending-products[_ngcontent-%COMP%]     .component-container .component-header .see-all, app-featured-brands[_ngcontent-%COMP%]     .component-container .component-header .see-all, app-new-arrivals[_ngcontent-%COMP%]     .component-container .component-header .see-all, app-suggested-for-you[_ngcontent-%COMP%]     .component-container .component-header .see-all, app-top-fashion-influencers[_ngcontent-%COMP%]     .component-container .component-header .see-all, app-shop-by-category[_ngcontent-%COMP%]     .component-container .component-header .see-all{color:#0095f6;font-size:14px;font-weight:600;text-decoration:none}app-trending-products[_ngcontent-%COMP%]     .component-container .component-header .see-all:hover, app-featured-brands[_ngcontent-%COMP%]     .component-container .component-header .see-all:hover, app-new-arrivals[_ngcontent-%COMP%]     .component-container .component-header .see-all:hover, app-suggested-for-you[_ngcontent-%COMP%]     .component-container .component-header .see-all:hover, app-top-fashion-influencers[_ngcontent-%COMP%]     .component-container .component-header .see-all:hover, app-shop-by-category[_ngcontent-%COMP%]     .component-container .component-header .see-all:hover{text-decoration:underline}app-trending-products[_ngcontent-%COMP%]     .component-container .component-content, app-featured-brands[_ngcontent-%COMP%]     .component-container .component-content, app-new-arrivals[_ngcontent-%COMP%]     .component-container .component-content, app-suggested-for-you[_ngcontent-%COMP%]     .component-container .component-content, app-top-fashion-influencers[_ngcontent-%COMP%]     .component-container .component-content, app-shop-by-category[_ngcontent-%COMP%]     .component-container .component-content{padding:16px}.suggestions[_ngcontent-%COMP%]{padding:20px;background:linear-gradient(135deg,#f8f9fa,#e9ecef);border-radius:16px;margin-bottom:24px}.suggestions[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:24px}.suggestions[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]{flex:1}.suggestions[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{font-size:24px;font-weight:700;color:#1a1a1a;margin:0 0 8px;display:flex;align-items:center;gap:12px}.suggestions[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   .title-icon[_ngcontent-%COMP%]{font-size:28px;color:#6c5ce7}.suggestions[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .section-subtitle[_ngcontent-%COMP%]{font-size:14px;color:#666;margin:0}.suggestions[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,#6c5ce7,#a29bfe);color:#fff;border:none;padding:12px 20px;border-radius:25px;font-weight:600;font-size:14px;display:flex;align-items:center;gap:8px;cursor:pointer;transition:all .3s ease;box-shadow:0 4px 15px #6c5ce74d}.suggestions[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 6px 20px #6c5ce766}.suggestions[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   .view-all-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:16px}.suggestions[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:24px;font-weight:700;color:#1a1a1a;margin:0 0 24px;display:flex;align-items:center;gap:12px}.suggestions[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]:before{content:"\\1f4a1";font-size:28px}.suggestions[_ngcontent-%COMP%]   .suggestion-list[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:16px}.suggestions[_ngcontent-%COMP%]   .suggestion-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]{background:#fff;border-radius:16px;overflow:hidden;box-shadow:0 4px 20px #00000014;transition:all .3s ease;cursor:pointer;padding:16px;display:flex;align-items:center;gap:16px}.suggestions[_ngcontent-%COMP%]   .suggestion-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]:hover{transform:translateY(-4px);box-shadow:0 8px 30px #0000001f}.suggestions[_ngcontent-%COMP%]   .suggestion-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:60px;height:60px;border-radius:50%;object-fit:cover;border:3px solid #f8f9fa}.suggestions[_ngcontent-%COMP%]   .suggestion-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   .suggestion-info[_ngcontent-%COMP%]{flex:1}.suggestions[_ngcontent-%COMP%]   .suggestion-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   .suggestion-info[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]{margin:0 0 4px;font-size:16px;font-weight:600;color:#1a1a1a}.suggestions[_ngcontent-%COMP%]   .suggestion-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   .suggestion-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:12px;color:#666}.suggestions[_ngcontent-%COMP%]   .suggestion-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   .follow-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,#6c5ce7,#a29bfe);color:#fff;border:none;padding:8px 16px;border-radius:8px;font-size:12px;font-weight:600;cursor:pointer;transition:all .3s ease}.suggestions[_ngcontent-%COMP%]   .suggestion-list[_ngcontent-%COMP%]   .suggestion-item[_ngcontent-%COMP%]   .follow-btn[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 15px #6c5ce74d}.influencers[_ngcontent-%COMP%]{padding:20px;background:linear-gradient(135deg,#f8f9fa,#e9ecef);border-radius:16px;margin-bottom:24px}.influencers[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:24px;font-weight:700;color:#1a1a1a;margin:0 0 24px;display:flex;align-items:center;gap:12px}.influencers[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]:before{content:"\\1f451";font-size:28px}.influencers[_ngcontent-%COMP%]   .influencer-list[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:20px}.influencers[_ngcontent-%COMP%]   .influencer-list[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]{background:#fff;border-radius:16px;overflow:hidden;box-shadow:0 4px 20px #00000014;transition:all .3s ease;cursor:pointer;padding:20px;display:flex;align-items:flex-start;gap:16px}.influencers[_ngcontent-%COMP%]   .influencer-list[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]:hover{transform:translateY(-4px);box-shadow:0 8px 30px #0000001f}.influencers[_ngcontent-%COMP%]   .influencer-list[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:70px;height:70px;border-radius:50%;object-fit:cover;border:3px solid #f8f9fa}.influencers[_ngcontent-%COMP%]   .influencer-list[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]   .influencer-info[_ngcontent-%COMP%]{flex:1}.influencers[_ngcontent-%COMP%]   .influencer-list[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]   .influencer-info[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]{margin:0 0 6px;font-size:16px;font-weight:600;color:#1a1a1a}.influencers[_ngcontent-%COMP%]   .influencer-list[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]   .influencer-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0 0 12px;font-size:14px;color:#666;font-weight:500}.influencers[_ngcontent-%COMP%]   .influencer-list[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]   .influencer-info[_ngcontent-%COMP%]   .influencer-stats[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:6px;margin-bottom:16px}.influencers[_ngcontent-%COMP%]   .influencer-list[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]   .influencer-info[_ngcontent-%COMP%]   .influencer-stats[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:12px;color:#666;background:#f8f9fa;padding:4px 8px;border-radius:12px;width:-moz-fit-content;width:fit-content;font-weight:500}.influencers[_ngcontent-%COMP%]   .influencer-list[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]   .influencer-info[_ngcontent-%COMP%]   .follow-btn[_ngcontent-%COMP%]{background:linear-gradient(135deg,#fd79a8,#e84393);color:#fff;border:none;padding:10px 20px;border-radius:8px;font-size:12px;font-weight:600;cursor:pointer;transition:all .3s ease}.influencers[_ngcontent-%COMP%]   .influencer-list[_ngcontent-%COMP%]   .influencer-item[_ngcontent-%COMP%]   .influencer-info[_ngcontent-%COMP%]   .follow-btn[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 15px #e843934d}.categories[_ngcontent-%COMP%]{padding:20px;background:linear-gradient(135deg,#f8f9fa,#e9ecef);border-radius:16px;margin-bottom:24px}.categories[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:24px;font-weight:700;color:#1a1a1a;margin:0 0 24px;display:flex;align-items:center;gap:12px}.categories[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]:before{content:"\\1f6cd\\fe0f";font-size:28px}.categories[_ngcontent-%COMP%]   .category-list[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(2,1fr);gap:16px}.categories[_ngcontent-%COMP%]   .category-list[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]{background:#fff;border-radius:16px;overflow:hidden;box-shadow:0 4px 20px #00000014;transition:all .3s ease;cursor:pointer;padding:20px 16px;display:flex;flex-direction:column;align-items:center;text-align:center;text-decoration:none;color:inherit}.categories[_ngcontent-%COMP%]   .category-list[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]:hover{transform:translateY(-4px);box-shadow:0 8px 30px #0000001f}.categories[_ngcontent-%COMP%]   .category-list[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:70px;height:70px;border-radius:50%;object-fit:cover;margin-bottom:12px;border:3px solid #f8f9fa;transition:transform .3s ease}.categories[_ngcontent-%COMP%]   .category-list[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]:hover   img[_ngcontent-%COMP%]{transform:scale(1.1)}.categories[_ngcontent-%COMP%]   .category-list[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:14px;font-weight:600;color:#1a1a1a;text-align:center}@media (max-width: 768px){.suggestions[_ngcontent-%COMP%], .influencers[_ngcontent-%COMP%], .categories[_ngcontent-%COMP%]{padding:16px}.suggestions[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .influencers[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .categories[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:20px}.suggestions[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]:before, .influencers[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]:before, .categories[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]:before{font-size:24px}.suggestion-item[_ngcontent-%COMP%], .influencer-item[_ngcontent-%COMP%]{padding:12px;gap:12px}.suggestion-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], .influencer-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:50px;height:50px}.suggestion-info[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%], .influencer-info[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]{font-size:14px}.category-list[_ngcontent-%COMP%]{grid-template-columns:1fr;gap:12px}.category-list[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]{flex-direction:row;text-align:left;padding:12px;gap:12px}.category-list[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:50px;height:50px;margin-bottom:0}.category-list[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:12px}}']})}}return i})();function _t(i,a){1&i&&(n.j41(0,"div",20),n.EFF(1,"3"),n.k0s())}function ht(i,a){if(1&i){const t=n.RV6();n.j41(0,"div",10)(1,"div",11)(2,"h1",12),n.EFF(3,"DFashion"),n.k0s(),n.nrm(4,"ion-icon",13),n.k0s(),n.j41(5,"div",14),n.nrm(6,"ion-icon",15),n.j41(7,"div",16),n.bIt("click",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.toggleTabMenu())}),n.nrm(8,"ion-icon",17),n.DNE(9,_t,2,0,"div",18),n.k0s(),n.j41(10,"div",16),n.bIt("click",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.toggleSidebar())}),n.nrm(11,"ion-icon",19),n.k0s()()()}if(2&i){const t=n.XpG();n.R7$(9),n.Y8G("ngIf",t.hasNotifications)}}function bt(i,a){1&i&&(n.j41(0,"div",21)(1,"div",22),n.nrm(2,"app-view-add-stories"),n.k0s(),n.j41(3,"div",23)(4,"div",24),n.nrm(5,"app-feed"),n.k0s(),n.j41(6,"div",25),n.nrm(7,"app-sidebar",26),n.k0s()()())}function xt(i,a){1&i&&(n.j41(0,"div",27)(1,"div",28)(2,"div",29),n.nrm(3,"app-view-add-stories")(4,"app-feed"),n.k0s(),n.nrm(5,"app-sidebar",26),n.k0s()())}function Ct(i,a){if(1&i){const t=n.RV6();n.j41(0,"div",30),n.bIt("click",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.closeTabMenu())}),n.k0s()}if(2&i){const t=n.XpG();n.AVh("active",t.isTabMenuOpen)}}function Pt(i,a){if(1&i){const t=n.RV6();n.j41(0,"div",31),n.bIt("click",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.closeSidebar())}),n.k0s()}if(2&i){const t=n.XpG();n.AVh("active",t.isSidebarOpen)}}function Ot(i,a){if(1&i){const t=n.RV6();n.j41(0,"div",32)(1,"div",33)(2,"h3"),n.EFF(3,"Discover"),n.k0s(),n.j41(4,"ion-icon",34),n.bIt("click",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.closeTabMenu())}),n.k0s()(),n.j41(5,"div",35)(6,"div",36),n.bIt("click",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.openSidebarTab("trending"))}),n.j41(7,"div",37),n.nrm(8,"ion-icon",38),n.k0s(),n.j41(9,"span",39),n.EFF(10,"Trending"),n.k0s(),n.j41(11,"div",40),n.EFF(12,"Hot products right now"),n.k0s()(),n.j41(13,"div",36),n.bIt("click",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.openSidebarTab("brands"))}),n.j41(14,"div",41),n.nrm(15,"ion-icon",42),n.k0s(),n.j41(16,"span",39),n.EFF(17,"Brands"),n.k0s(),n.j41(18,"div",40),n.EFF(19,"Top fashion brands"),n.k0s()(),n.j41(20,"div",36),n.bIt("click",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.openSidebarTab("arrivals"))}),n.j41(21,"div",43),n.nrm(22,"ion-icon",44),n.k0s(),n.j41(23,"span",39),n.EFF(24,"New"),n.k0s(),n.j41(25,"div",40),n.EFF(26,"Latest arrivals"),n.k0s()(),n.j41(27,"div",36),n.bIt("click",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.openSidebarTab("suggested"))}),n.j41(28,"div",45),n.nrm(29,"ion-icon",46),n.k0s(),n.j41(30,"span",39),n.EFF(31,"For You"),n.k0s(),n.j41(32,"div",40),n.EFF(33,"Personalized picks"),n.k0s()(),n.j41(34,"div",36),n.bIt("click",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.openSidebarTab("influencers"))}),n.j41(35,"div",47),n.nrm(36,"ion-icon",48),n.k0s(),n.j41(37,"span",39),n.EFF(38,"Influencers"),n.k0s(),n.j41(39,"div",40),n.EFF(40,"Top fashion creators"),n.k0s()(),n.j41(41,"div",36),n.bIt("click",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.openSidebarTab("categories"))}),n.j41(42,"div",49),n.nrm(43,"ion-icon",50),n.k0s(),n.j41(44,"span",39),n.EFF(45,"Categories"),n.k0s(),n.j41(46,"div",40),n.EFF(47,"Browse by category"),n.k0s()()()()}if(2&i){const t=n.XpG();n.AVh("active",t.isTabMenuOpen)}}function Mt(i,a){if(1&i){const t=n.RV6();n.j41(0,"div",51)(1,"div",52)(2,"div",53)(3,"div",54),n.nrm(4,"img",55),n.k0s(),n.j41(5,"div",56)(6,"h3"),n.EFF(7,"Your Profile"),n.k0s(),n.j41(8,"p"),n.EFF(9,"@username"),n.k0s()()(),n.j41(10,"ion-icon",34),n.bIt("click",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.closeSidebar())}),n.k0s()(),n.j41(11,"div",57),n.nrm(12,"app-sidebar"),n.k0s()()}if(2&i){const t=n.XpG();n.AVh("active",t.isSidebarOpen)}}function vt(i,a){1&i&&(n.j41(0,"div",25),n.nrm(1,"app-trending-products"),n.k0s())}function kt(i,a){1&i&&(n.j41(0,"div",25),n.nrm(1,"app-featured-brands"),n.k0s())}function wt(i,a){1&i&&(n.j41(0,"div",25),n.nrm(1,"app-new-arrivals"),n.k0s())}function yt(i,a){1&i&&(n.j41(0,"div",25),n.nrm(1,"app-suggested-for-you"),n.k0s())}function St(i,a){1&i&&(n.j41(0,"div",25),n.nrm(1,"app-top-fashion-influencers"),n.k0s())}function Ft(i,a){if(1&i&&(n.j41(0,"div",64)(1,"div",65),n.nrm(2,"ion-icon",66),n.k0s(),n.j41(3,"span"),n.EFF(4),n.k0s()()),2&i){const t=a.$implicit;n.R7$(2),n.Y8G("name",t.icon),n.R7$(2),n.JRh(t.name)}}function jt(i,a){if(1&i&&(n.j41(0,"div",25)(1,"div",62),n.DNE(2,Ft,5,2,"div",63),n.k0s()()),2&i){const t=n.XpG(2);n.R7$(2),n.Y8G("ngForOf",t.categories)}}function It(i,a){if(1&i){const t=n.RV6();n.j41(0,"div",58)(1,"div",59)(2,"h3"),n.EFF(3),n.k0s(),n.j41(4,"ion-icon",34),n.bIt("click",function(){n.eBV(t);const e=n.XpG();return n.Njj(e.closeSidebarContent())}),n.k0s()(),n.j41(5,"div",60),n.DNE(6,vt,2,0,"div",61)(7,kt,2,0,"div",61)(8,wt,2,0,"div",61)(9,yt,2,0,"div",61)(10,St,2,0,"div",61)(11,jt,3,1,"div",61),n.k0s()()}if(2&i){const t=n.XpG();n.AVh("active",t.isSidebarContentOpen),n.R7$(3),n.JRh(t.currentSidebarTitle),n.R7$(3),n.Y8G("ngIf","trending"===t.currentSidebarTab),n.R7$(),n.Y8G("ngIf","brands"===t.currentSidebarTab),n.R7$(),n.Y8G("ngIf","arrivals"===t.currentSidebarTab),n.R7$(),n.Y8G("ngIf","suggested"===t.currentSidebarTab),n.R7$(),n.Y8G("ngIf","influencers"===t.currentSidebarTab),n.R7$(),n.Y8G("ngIf","categories"===t.currentSidebarTab)}}function Tt(i,a){1&i&&(n.j41(0,"div",67)(1,"div",68),n.nrm(2,"ion-icon",69),n.k0s(),n.j41(3,"div",70),n.nrm(4,"ion-icon",71),n.k0s(),n.j41(5,"div",70),n.nrm(6,"ion-icon",72),n.k0s(),n.j41(7,"div",70),n.nrm(8,"ion-icon",73),n.k0s(),n.j41(9,"div",70)(10,"div",74),n.nrm(11,"img",55),n.k0s()()())}let Rt=(()=>{class i{constructor(){this.isMobile=!1,this.isSidebarOpen=!1,this.isTabMenuOpen=!1,this.isSidebarContentOpen=!1,this.currentSidebarTab="",this.currentSidebarTitle="",this.hasNotifications=!0,this.window=window,this.isLiked=!1,this.instagramStories=[{id:1,username:"zara",avatar:"/assets/images/default-avatar.svg",hasStory:!0,viewed:!1,touching:!1},{id:2,username:"nike",avatar:"/assets/images/default-avatar.svg",hasStory:!0,viewed:!1,touching:!1},{id:3,username:"adidas",avatar:"/assets/images/default-avatar.svg",hasStory:!0,viewed:!0,touching:!1},{id:4,username:"h&m",avatar:"/assets/images/default-avatar.svg",hasStory:!0,viewed:!1,touching:!1},{id:5,username:"uniqlo",avatar:"/assets/images/default-avatar.svg",hasStory:!0,viewed:!1,touching:!1},{id:6,username:"gucci",avatar:"/assets/images/default-avatar.svg",hasStory:!0,viewed:!0,touching:!1},{id:7,username:"prada",avatar:"/assets/images/default-avatar.svg",hasStory:!0,viewed:!1,touching:!1},{id:8,username:"versace",avatar:"/assets/images/default-avatar.svg",hasStory:!0,viewed:!1,touching:!1}],this.categories=[{name:"Women",icon:"woman"},{name:"Men",icon:"man"},{name:"Kids",icon:"happy"},{name:"Shoes",icon:"footsteps"},{name:"Bags",icon:"bag"},{name:"Accessories",icon:"watch"},{name:"Beauty",icon:"flower"},{name:"Sports",icon:"fitness"}],this.preventScroll=t=>{(this.isSidebarOpen||this.isTabMenuOpen||this.isSidebarContentOpen)&&t.preventDefault()}}ngOnInit(){this.checkScreenSize(),console.log("Home component initialized:",{isMobile:this.isMobile,instagramStories:this.instagramStories.length}),document.addEventListener("touchmove",this.preventScroll,{passive:!1})}ngOnDestroy(){document.removeEventListener("touchmove",this.preventScroll)}onResize(t){this.checkScreenSize(),!this.isMobile&&this.isSidebarOpen&&this.closeSidebar()}checkScreenSize(){const t=window.innerWidth,o=navigator.userAgent,e=/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(o);this.isMobile=t<=768||e,console.log("Screen size check:",{width:t,height:window.innerHeight,isMobile:this.isMobile,isMobileUserAgent:e,userAgent:o})}toggleSidebar(){this.isSidebarOpen=!this.isSidebarOpen,this.toggleBodyScroll()}closeSidebar(){this.isSidebarOpen=!1,this.toggleBodyScroll()}toggleBodyScroll(){document.body.style.overflow=this.isSidebarOpen?"hidden":""}toggleTabMenu(){this.isTabMenuOpen=!this.isTabMenuOpen,this.toggleBodyScroll()}closeTabMenu(){this.isTabMenuOpen=!1,this.toggleBodyScroll()}openSidebarTab(t){this.currentSidebarTab=t,this.isSidebarContentOpen=!0,this.isTabMenuOpen=!1,this.currentSidebarTitle={trending:"Trending Products",brands:"Featured Brands",arrivals:"New Arrivals",suggested:"Suggested for You",influencers:"Fashion Influencers",categories:"Categories"}[t]||"Discover",this.toggleBodyScroll()}closeSidebarContent(){this.isSidebarContentOpen=!1,this.currentSidebarTab="",this.toggleBodyScroll()}toggleLike(){this.isLiked=!this.isLiked,console.log("Like toggled:",this.isLiked)}openComments(){console.log("Opening comments...")}shareContent(){console.log("Sharing content..."),navigator.share&&navigator.share({title:"DFashion",text:"Check out this amazing fashion content!",url:window.location.href})}openMusic(){console.log("Opening music...")}createStory(){console.log("Create story clicked")}viewStory(t){console.log("View story:",t)}trackByStoryId(t,o){return o.id||t}onStoryTouchStart(t,o){o.touching=!0,"vibrate"in navigator&&navigator.vibrate(10)}onStoryTouchEnd(t,o){o.touching=!1}onLikeClick(){this.isLiked=!this.isLiked,console.log("Like clicked:",this.isLiked)}onCommentClick(){console.log("Comment clicked")}onShareClick(){console.log("Share clicked")}onBookmarkClick(){console.log("Bookmark clicked")}navigateToTrending(){console.log("Navigate to trending")}navigateToNewArrivals(){console.log("Navigate to new arrivals")}navigateToOffers(){console.log("Navigate to offers")}navigateToCategories(){console.log("Navigate to categories")}navigateToWishlist(){console.log("Navigate to wishlist")}navigateToCart(){console.log("Navigate to cart")}static{this.\u0275fac=function(o){return new(o||i)}}static{this.\u0275cmp=n.VBU({type:i,selectors:[["app-home"]],hostBindings:function(o,e){1&o&&n.bIt("resize",function(c){return e.onResize(c)},!1,n.tSv)},standalone:!0,features:[n.aNF],decls:10,vars:15,consts:[[1,"home-container"],["class","mobile-header instagram-style",4,"ngIf"],["class","web-layout",4,"ngIf"],["class","mobile-layout",4,"ngIf"],["class","tab-menu-overlay",3,"active","click",4,"ngIf"],["class","sidebar-overlay",3,"active","click",4,"ngIf"],["class","instagram-tab-menu",3,"active",4,"ngIf"],["class","mobile-sidebar",3,"active",4,"ngIf"],["class","sidebar-content-modal",3,"active",4,"ngIf"],["class","instagram-bottom-nav",4,"ngIf"],[1,"mobile-header","instagram-style"],[1,"header-left"],[1,"app-logo"],["name","chevron-down",1,"logo-dropdown"],[1,"header-right"],["name","heart-outline",1,"header-icon"],[1,"menu-icon-container",3,"click"],["name","grid-outline",1,"header-icon","menu-icon"],["class","notification-dot",4,"ngIf"],["name","menu-outline",1,"header-icon","menu-icon"],[1,"notification-dot"],[1,"web-layout"],[1,"stories-section-container"],[1,"two-column-layout"],[1,"post-section"],[1,"sidebar-section"],[1,"desktop-sidebar"],[1,"mobile-layout"],[1,"content-grid"],[1,"main-content"],[1,"tab-menu-overlay",3,"click"],[1,"sidebar-overlay",3,"click"],[1,"instagram-tab-menu"],[1,"tab-menu-header"],["name","close-outline",1,"close-icon",3,"click"],[1,"tab-menu-grid"],[1,"tab-item",3,"click"],[1,"tab-icon","trending"],["name","trending-up"],[1,"tab-label"],[1,"tab-tooltip"],[1,"tab-icon","brands"],["name","diamond"],[1,"tab-icon","arrivals"],["name","sparkles"],[1,"tab-icon","suggested"],["name","heart"],[1,"tab-icon","influencers"],["name","people"],[1,"tab-icon","categories"],["name","grid"],[1,"mobile-sidebar"],[1,"sidebar-header"],[1,"user-profile"],[1,"profile-avatar"],["src","assets/images/default-avatar.svg","alt","Profile"],[1,"profile-info"],[1,"sidebar-content"],[1,"sidebar-content-modal"],[1,"modal-header"],[1,"modal-content"],["class","sidebar-section",4,"ngIf"],[1,"categories-grid"],["class","category-item",4,"ngFor","ngForOf"],[1,"category-item"],[1,"category-icon"],[3,"name"],[1,"instagram-bottom-nav"],[1,"nav-item","active"],["name","home"],[1,"nav-item"],["name","search"],["name","add-circle-outline"],["name","play-circle-outline"],[1,"profile-avatar-nav"]],template:function(o,e){1&o&&(n.j41(0,"div",0),n.DNE(1,ht,12,1,"div",1)(2,bt,8,0,"div",2)(3,xt,6,0,"div",3)(4,Ct,1,2,"div",4)(5,Pt,1,2,"div",5)(6,Ot,48,2,"div",6)(7,Mt,13,2,"div",7)(8,It,12,9,"div",8)(9,Tt,12,0,"div",9),n.k0s()),2&o&&(n.AVh("mobile-instagram",e.isMobile)("sidebar-open",e.isSidebarOpen)("mobile",e.isMobile),n.R7$(),n.Y8G("ngIf",e.isMobile),n.R7$(),n.Y8G("ngIf",!e.isMobile),n.R7$(),n.Y8G("ngIf",e.isMobile),n.R7$(),n.Y8G("ngIf",e.isMobile),n.R7$(),n.Y8G("ngIf",e.isMobile),n.R7$(),n.Y8G("ngIf",e.isMobile),n.R7$(),n.Y8G("ngIf",e.isMobile),n.R7$(),n.Y8G("ngIf",e.isMobile),n.R7$(),n.Y8G("ngIf",e.isMobile))},dependencies:[d.MD,d.Sq,d.bT,g.bv,g.iq,F.V,K,ft,v,k,w,y,S],styles:[".home-container[_ngcontent-%COMP%]{padding:20px 0;min-height:calc(100vh - 60px);position:relative;background:#fff}.home-container.mobile-instagram[_ngcontent-%COMP%]{background:#fff!important;color:#262626!important;padding:0!important;min-height:100vh!important}.mobile-header[_ngcontent-%COMP%]{display:none;position:fixed;top:0;left:0;right:0;height:60px;background:#fff;border-bottom:1px solid #dbdbdb;z-index:1001;padding:0 16px;align-items:center;justify-content:space-between}.mobile-header.instagram-style[_ngcontent-%COMP%]{background:#fff;border-bottom:1px solid #dbdbdb}.mobile-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px}.mobile-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .app-logo[_ngcontent-%COMP%]{font-size:28px;font-weight:400;color:#262626;margin:0;font-family:Billabong,cursive,-apple-system,BlinkMacSystemFont,sans-serif;letter-spacing:.5px}.mobile-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .logo-dropdown[_ngcontent-%COMP%]{font-size:16px;color:#262626;margin-top:2px}.mobile-header[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%]{display:flex;align-items:center;gap:16px}.mobile-header[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%]   .header-icon[_ngcontent-%COMP%]{font-size:24px;color:#262626;cursor:pointer;transition:all .2s ease;padding:8px;border-radius:50%}.mobile-header[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%]   .header-icon[_ngcontent-%COMP%]:hover{background-color:#0000000d}.mobile-header[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%]   .menu-icon-container[_ngcontent-%COMP%]{position:relative;cursor:pointer}.mobile-header[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%]   .menu-icon-container[_ngcontent-%COMP%]   .notification-dot[_ngcontent-%COMP%]{position:absolute;top:-2px;right:-2px;width:8px;height:8px;background:#ff3040;border-radius:50%;border:2px solid #ffffff}.instagram-stories-section[_ngcontent-%COMP%]{display:none;position:fixed;top:60px;left:0;right:0;background:#fff;border-bottom:1px solid #dbdbdb;z-index:999;padding:12px 0;height:100px;box-shadow:0 2px 8px #00000014;backdrop-filter:blur(10px);-webkit-backdrop-filter:blur(10px)}@supports ((-webkit-backdrop-filter: blur(10px)) or (backdrop-filter: blur(10px))){.instagram-stories-section[_ngcontent-%COMP%]{background:#fffffff2}}.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]{display:flex;gap:12px;padding:0 16px;overflow-x:auto;overflow-y:hidden;scrollbar-width:none;-ms-overflow-style:none;height:100%;align-items:center;min-width:max-content;scroll-behavior:smooth;position:relative;z-index:998;-webkit-overflow-scrolling:touch;overscroll-behavior-x:contain;scroll-snap-type:x proximity;will-change:scroll-position;transform:translateZ(0)}.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]::-webkit-scrollbar{display:none}@media (min-width: 320px) and (max-width: 768px){.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]{padding:0 12px;gap:10px;scroll-snap-type:x mandatory;touch-action:pan-x;-webkit-overflow-scrolling:touch;overscroll-behavior-x:contain;contain:layout style paint}}@media (min-width: 320px) and (max-width: 480px){.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]{padding:0 8px;gap:8px;scroll-padding-left:8px;scroll-padding-right:8px}}@media (min-width: 320px) and (max-width: 400px){.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]{padding:0 6px;gap:6px;scroll-padding-left:6px;scroll-padding-right:6px}}.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:6px;min-width:70px;max-width:70px;cursor:pointer;flex-shrink:0;transition:transform .2s ease;scroll-snap-align:start;scroll-snap-stop:normal;position:relative}.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]:hover{transform:scale(1.05)}.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]:active{transform:scale(.95)}@media (min-width: 320px) and (max-width: 768px){.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]{min-width:65px;max-width:65px;gap:5px;padding:4px;margin:-4px}}@media (min-width: 320px) and (max-width: 480px){.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]{min-width:60px;max-width:60px;gap:4px}}@media (min-width: 320px) and (max-width: 400px){.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]{min-width:55px;max-width:55px;gap:3px}}@media (max-width: 320px){.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]{min-width:50px;max-width:50px;gap:2px}}.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item.your-story[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%]{position:relative}.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item.your-story[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%]   .add-story-btn[_ngcontent-%COMP%]{position:absolute;bottom:-2px;right:-2px;width:20px;height:20px;background:#0095f6;border:2px solid #ffffff;border-radius:50%;display:flex;align-items:center;justify-content:center}.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item.your-story[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%]   .add-story-btn[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:12px;color:#fff}.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%]{width:60px;height:60px;border-radius:50%;overflow:hidden;border:2px solid #dbdbdb;position:relative;flex-shrink:0;transition:all .2s ease}.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar.has-story[_ngcontent-%COMP%]{border:2px solid transparent;background:linear-gradient(45deg,#f09433,#e6683c,#dc2743,#cc2366,#bc1888);padding:2px}.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar.has-story[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{border-radius:50%;border:2px solid #ffffff}.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar.has-story[_ngcontent-%COMP%]:not(.viewed){animation:_ngcontent-%COMP%_storyPulse 2s infinite}.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar.viewed[_ngcontent-%COMP%]{border:2px solid #c7c7c7;background:#c7c7c7}.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar.touching[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_storyTouchFeedback .2s ease}.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%]   .story-ring[_ngcontent-%COMP%]{position:absolute;inset:-3px;border-radius:50%;background:linear-gradient(45deg,#f09433,#e6683c,#dc2743,#cc2366,#bc1888);z-index:-1;opacity:.8}.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%]   .story-gradient-ring[_ngcontent-%COMP%]{position:absolute;inset:-5px;border-radius:50%;background:linear-gradient(45deg,#f09433,#e6683c,#dc2743,#cc2366,#bc1888);z-index:-2;opacity:.3;animation:_ngcontent-%COMP%_storyRingGradient 3s infinite;filter:blur(2px)}.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover;border-radius:50%;display:block;transition:transform .2s ease;position:relative;z-index:1}@media (min-width: 320px) and (max-width: 768px){.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%]{width:55px;height:55px}}@media (min-width: 320px) and (max-width: 480px){.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%]{width:50px;height:50px}}@media (min-width: 320px) and (max-width: 400px){.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%]{width:45px;height:45px}}@media (max-width: 320px){.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-avatar[_ngcontent-%COMP%]{width:40px;height:40px}}.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-username[_ngcontent-%COMP%]{font-size:11px;color:#262626;text-align:center;max-width:70px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;line-height:1.2;font-weight:400;margin-top:4px;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility}@media (min-width: 320px) and (max-width: 768px){.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-username[_ngcontent-%COMP%]{font-size:10px;max-width:65px;font-weight:500}}@media (min-width: 320px) and (max-width: 480px){.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-username[_ngcontent-%COMP%]{font-size:9px;max-width:60px;line-height:1.1}}@media (min-width: 320px) and (max-width: 400px){.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-username[_ngcontent-%COMP%]{font-size:8px;max-width:55px;line-height:1.1}}@media (max-width: 320px){.instagram-stories-section[_ngcontent-%COMP%]   .stories-container[_ngcontent-%COMP%]   .story-item[_ngcontent-%COMP%]   .story-username[_ngcontent-%COMP%]{font-size:7px;max-width:50px;line-height:1;font-weight:600}}@keyframes _ngcontent-%COMP%_storyPulse{0%{transform:scale(1) translateZ(0);box-shadow:0 0 #f09433b3}70%{transform:scale(1.05) translateZ(0);box-shadow:0 0 0 10px #f0943300}to{transform:scale(1) translateZ(0);box-shadow:0 0 #f0943300}}@keyframes _ngcontent-%COMP%_storyRingGradient{0%{background:linear-gradient(45deg,#f09433,#e6683c,#dc2743,#cc2366,#bc1888)}25%{background:linear-gradient(90deg,#f09433,#e6683c,#dc2743,#cc2366,#bc1888)}50%{background:linear-gradient(135deg,#f09433,#e6683c,#dc2743,#cc2366,#bc1888)}75%{background:linear-gradient(180deg,#f09433,#e6683c,#dc2743,#cc2366,#bc1888)}to{background:linear-gradient(45deg,#f09433,#e6683c,#dc2743,#cc2366,#bc1888)}}@keyframes _ngcontent-%COMP%_storyTouchFeedback{0%{transform:scale(1) translateZ(0)}50%{transform:scale(.95) translateZ(0)}to{transform:scale(1) translateZ(0)}}.instagram-bottom-nav[_ngcontent-%COMP%]{display:none;position:fixed;bottom:0;left:0;right:0;background:#fff;border-top:1px solid #dbdbdb;justify-content:space-around;align-items:center;padding:8px 0;z-index:1000;height:60px;box-shadow:0 -1px 3px #0000001a;padding-bottom:max(8px,env(safe-area-inset-bottom))}.instagram-bottom-nav[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;cursor:pointer;padding:4px 8px;border-radius:8px;transition:all .2s ease;min-width:44px;min-height:44px;position:relative}.instagram-bottom-nav[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]:hover{background-color:#0000000d}.instagram-bottom-nav[_ngcontent-%COMP%]   .nav-item.active[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:#262626;transform:scale(1.1)}.instagram-bottom-nav[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:24px;color:#8e8e8e;transition:all .2s ease}.instagram-bottom-nav[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .profile-avatar-nav[_ngcontent-%COMP%]{width:24px;height:24px;border-radius:50%;overflow:hidden;border:1px solid #8e8e8e}.instagram-bottom-nav[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .profile-avatar-nav.active[_ngcontent-%COMP%]{border:2px solid #262626}.instagram-bottom-nav[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .profile-avatar-nav[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover}.instagram-bottom-nav[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .nav-badge[_ngcontent-%COMP%]{position:absolute;top:2px;right:2px;background:#ff3040;color:#fff;font-size:10px;font-weight:600;padding:2px 6px;border-radius:10px;min-width:16px;text-align:center;line-height:1.2}.tab-menu-overlay[_ngcontent-%COMP%]{position:fixed;inset:0;background:#000c;z-index:1500;opacity:0;visibility:hidden;transition:all .3s ease}.tab-menu-overlay.active[_ngcontent-%COMP%]{opacity:1;visibility:visible}.instagram-tab-menu[_ngcontent-%COMP%]{position:fixed;bottom:0;left:0;right:0;background:#000;border-top-left-radius:20px;border-top-right-radius:20px;z-index:1600;transform:translateY(100%);transition:transform .3s ease;max-height:70vh;overflow-y:auto}.instagram-tab-menu.active[_ngcontent-%COMP%]{transform:translateY(0)}.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:20px 24px 16px;border-bottom:1px solid #262626}.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:20px;font-weight:600;color:#fff;margin:0}.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-header[_ngcontent-%COMP%]   .close-icon[_ngcontent-%COMP%]{font-size:24px;color:#8e8e8e;cursor:pointer;padding:8px;border-radius:50%;transition:all .2s ease}.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-header[_ngcontent-%COMP%]   .close-icon[_ngcontent-%COMP%]:hover{background-color:#ffffff1a;color:#fff}.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(3,1fr);gap:24px;padding:24px}.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:12px;cursor:pointer;padding:16px;border-radius:16px;transition:all .2s ease;position:relative}.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]:hover{background-color:#ffffff0d;transform:scale(1.05)}.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-icon[_ngcontent-%COMP%]{width:56px;height:56px;border-radius:50%;display:flex;align-items:center;justify-content:center;position:relative}.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-icon[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:24px;color:#fff}.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-icon.trending[_ngcontent-%COMP%]{background:linear-gradient(135deg,#ff6b6b,#ff8e53)}.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-icon.brands[_ngcontent-%COMP%]{background:linear-gradient(135deg,#4ecdc4,#44a08d)}.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-icon.arrivals[_ngcontent-%COMP%]{background:linear-gradient(135deg,#a8edea,#fed6e3)}.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-icon.arrivals[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:#333}.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-icon.suggested[_ngcontent-%COMP%]{background:linear-gradient(135deg,#ff9a9e,#fecfef)}.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-icon.suggested[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{color:#333}.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-icon.influencers[_ngcontent-%COMP%]{background:linear-gradient(135deg,#667eea,#764ba2)}.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-icon.categories[_ngcontent-%COMP%]{background:linear-gradient(135deg,#f093fb,#f5576c)}.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-label[_ngcontent-%COMP%]{font-size:14px;font-weight:600;color:#fff;text-align:center}.instagram-tab-menu[_ngcontent-%COMP%]   .tab-menu-grid[_ngcontent-%COMP%]   .tab-item[_ngcontent-%COMP%]   .tab-tooltip[_ngcontent-%COMP%]{font-size:12px;color:#8e8e8e;text-align:center;line-height:1.3}.sidebar-content-modal[_ngcontent-%COMP%]{position:fixed;inset:0;background:#000;z-index:1700;transform:translate(100%);transition:transform .3s ease;overflow-y:auto}.sidebar-content-modal.active[_ngcontent-%COMP%]{transform:translate(0)}.sidebar-content-modal[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]{position:sticky;top:0;background:#000;display:flex;justify-content:space-between;align-items:center;padding:20px 24px;border-bottom:1px solid #262626;z-index:10}.sidebar-content-modal[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:18px;font-weight:600;color:#fff;margin:0}.sidebar-content-modal[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]   .close-icon[_ngcontent-%COMP%]{font-size:24px;color:#8e8e8e;cursor:pointer;padding:8px;border-radius:50%;transition:all .2s ease}.sidebar-content-modal[_ngcontent-%COMP%]   .modal-header[_ngcontent-%COMP%]   .close-icon[_ngcontent-%COMP%]:hover{background-color:#ffffff1a;color:#fff}.sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]{padding:0}.sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]{background:#000;color:#fff;min-height:calc(100vh - 80px)}.sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]     *{background-color:transparent!important;color:#fff!important}.sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]     .card, .sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]     .section, .sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]     .item{background:#1a1a1a!important;border:1px solid #262626!important}.sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]     .text-dark{color:#fff!important}.sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .sidebar-section[_ngcontent-%COMP%]     .bg-white{background:#1a1a1a!important}.sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(2,1fr);gap:16px;padding:24px}.sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:12px;padding:20px;background:#1a1a1a;border-radius:16px;border:1px solid #262626;cursor:pointer;transition:all .2s ease}.sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]:hover{background:#262626;transform:scale(1.02)}.sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   .category-icon[_ngcontent-%COMP%]{width:48px;height:48px;border-radius:50%;background:linear-gradient(135deg,#667eea,#764ba2);display:flex;align-items:center;justify-content:center}.sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   .category-icon[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:24px;color:#fff}.sidebar-content-modal[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]   .categories-grid[_ngcontent-%COMP%]   .category-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:14px;font-weight:600;color:#fff;text-align:center}.web-layout[_ngcontent-%COMP%]{max-width:1200px;margin:0 auto;padding:80px 20px 20px;min-height:calc(100vh - 100px)}@media (min-width: 769px){.web-layout[_ngcontent-%COMP%]{display:block}}@media (max-width: 768px){.web-layout[_ngcontent-%COMP%]{display:none}}.stories-section-container[_ngcontent-%COMP%]{width:100%;margin-bottom:20px}.two-column-layout[_ngcontent-%COMP%]{display:flex;gap:20px}.post-section[_ngcontent-%COMP%], .sidebar-section[_ngcontent-%COMP%]{flex:1}@media (min-width: 769px){.mobile-layout[_ngcontent-%COMP%]{display:none}}@media (max-width: 768px){.mobile-layout[_ngcontent-%COMP%]{display:block}}.content-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:1fr 400px;gap:40px;max-width:1000px;margin:0 auto;background:#fff;padding:0 20px}.main-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:24px}.desktop-sidebar[_ngcontent-%COMP%]{display:block}.sidebar-overlay[_ngcontent-%COMP%]{position:fixed;top:0;left:0;width:100%;height:100%;background:#000000a6;z-index:200;opacity:0;visibility:hidden;transition:all .3s cubic-bezier(.25,.46,.45,.94)}.sidebar-overlay.active[_ngcontent-%COMP%]{opacity:1;visibility:visible}.mobile-sidebar[_ngcontent-%COMP%]{position:fixed;top:0;right:-100%;width:85%;max-width:400px;height:100%;background:#fff;z-index:300;transition:right .3s cubic-bezier(.25,.46,.45,.94);box-shadow:-2px 0 10px #0000001a;display:flex;flex-direction:column}.mobile-sidebar.active[_ngcontent-%COMP%]{right:0}.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;padding:20px 16px;border-bottom:1px solid #dbdbdb;background:#fafafa}.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px}.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .profile-avatar[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;overflow:hidden;border:2px solid #dbdbdb}.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .profile-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover}.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .profile-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0;font-size:16px;font-weight:600;color:#262626}.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .profile-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:14px;color:#8e8e8e}.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%]   .close-icon[_ngcontent-%COMP%]{font-size:24px;color:#262626;cursor:pointer;padding:8px;margin:-8px;transition:color .2s ease}.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-header[_ngcontent-%COMP%]   .close-icon[_ngcontent-%COMP%]:hover{color:#8e8e8e}.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-content[_ngcontent-%COMP%]{flex:1;overflow-y:auto;padding:16px 0}.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-content[_ngcontent-%COMP%]::-webkit-scrollbar{width:4px}.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-content[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:#f1f1f1}.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#c1c1c1;border-radius:2px}.mobile-sidebar[_ngcontent-%COMP%]   .sidebar-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#a8a8a8}@media (max-width: 1024px){.content-grid[_ngcontent-%COMP%]{grid-template-columns:1fr;max-width:100%;padding:0 16px}}@media (max-width: 1024px) and (min-width: 768px){.content-grid[_ngcontent-%COMP%]{max-width:768px;margin:0 auto;padding:0 24px}}@media (max-width: 1024px){.desktop-sidebar[_ngcontent-%COMP%]{display:none}.mobile-header[_ngcontent-%COMP%]{display:flex}.home-container[_ngcontent-%COMP%]{padding-top:80px}}@media (max-width: 1024px) and (min-width: 768px){.home-container[_ngcontent-%COMP%]{padding:80px 0 0}}.home-container.mobile-instagram[_ngcontent-%COMP%]{background:#fff!important;min-height:100vh!important}.home-container.mobile-instagram[_ngcontent-%COMP%]   .mobile-header[_ngcontent-%COMP%]{display:flex!important;visibility:visible!important;opacity:1!important;width:100%!important;height:60px!important;background:#fff!important;border-bottom:1px solid #dbdbdb!important;box-shadow:0 1px 3px #0000001a!important}.home-container.mobile-instagram[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]{display:block!important;visibility:visible!important;opacity:1!important;width:100%!important;height:100px!important;padding:8px 0!important;background:#fff!important;border-bottom:1px solid #dbdbdb!important;transform:translateZ(0)!important;will-change:scroll-position!important;contain:layout style paint!important;backdrop-filter:blur(10px)!important;-webkit-backdrop-filter:blur(10px)!important}@supports ((-webkit-backdrop-filter: blur(10px)) or (backdrop-filter: blur(10px))){.home-container.mobile-instagram[_ngcontent-%COMP%]   .instagram-stories-section[_ngcontent-%COMP%]{background:#fffffff2!important}}.home-container.mobile-instagram[_ngcontent-%COMP%]   .content-grid[_ngcontent-%COMP%]{grid-template-columns:1fr!important;padding:160px 0 60px!important;background:#fff!important;gap:0!important;margin:0!important;max-width:100%!important;min-height:calc(100vh - 220px)!important;overflow-x:hidden!important;position:relative!important;z-index:1!important}@media (min-width: 768px){.home-container.mobile-instagram[_ngcontent-%COMP%]   .content-grid[_ngcontent-%COMP%]{padding:160px 16px 60px!important;max-width:768px!important;margin:0 auto!important;gap:16px!important}}@media (min-width: 1024px){.home-container.mobile-instagram[_ngcontent-%COMP%]   .content-grid[_ngcontent-%COMP%]{grid-template-columns:1fr 300px!important;padding:160px 24px 60px!important;max-width:1200px!important;gap:32px!important}}@media (min-width: 1200px){.home-container.mobile-instagram[_ngcontent-%COMP%]   .content-grid[_ngcontent-%COMP%]{grid-template-columns:1fr 400px!important;padding:160px 32px 60px!important;max-width:1400px!important;gap:40px!important}}@media (min-width: 1440px){.home-container.mobile-instagram[_ngcontent-%COMP%]   .content-grid[_ngcontent-%COMP%]{padding:160px 48px 60px!important;max-width:1600px!important;gap:48px!important}}.home-container.mobile-instagram[_ngcontent-%COMP%]   .mobile-bottom-nav[_ngcontent-%COMP%]{background:#fff!important;border-top:1px solid #dbdbdb!important;box-shadow:0 -1px 3px #0000001a!important}@media (min-width: 320px) and (max-width: 768px){.home-container[_ngcontent-%COMP%]{background:#fafafa!important;padding:0!important}.home-container.mobile-instagram[_ngcontent-%COMP%]{background:#fafafa!important}.home-container.mobile-instagram[_ngcontent-%COMP%]   .content-grid[_ngcontent-%COMP%]{grid-template-columns:1fr!important;padding:5px 0!important;background:#fff!important;gap:0!important;margin:0!important;max-width:100%!important;min-height:calc(100vh - 220px)!important;overflow-x:hidden!important;position:relative!important;z-index:1!important}.content-grid[_ngcontent-%COMP%]{padding:0;margin:0;background:#fafafa}.content-grid[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]{background:#fff;border-radius:0;box-shadow:none;margin:0;padding:0}.mobile-header[_ngcontent-%COMP%]{display:flex!important;visibility:visible!important;opacity:1!important;width:100%!important;height:60px!important}.instagram-stories-section[_ngcontent-%COMP%]{display:block!important;visibility:visible!important;opacity:1!important;width:100%!important;height:100px!important;padding:8px 0!important;background:#fff!important;border-bottom:1px solid #dbdbdb!important}app-view-add-stories[_ngcontent-%COMP%]{display:block!important;width:100%!important;padding:12px 0!important;background:#fff!important;border-bottom:1px solid #dbdbdb!important}.instagram-bottom-nav[_ngcontent-%COMP%]{display:flex!important}.desktop-sidebar[_ngcontent-%COMP%]{display:none!important}.home-container[_ngcontent-%COMP%]{background:#fff!important;min-height:100vh;padding:0!important}.content-grid[_ngcontent-%COMP%]{grid-template-columns:1fr!important;padding:160px 0 60px!important;background:#fff!important;gap:0!important;margin:0!important;max-width:100%!important;min-height:calc(100vh - 220px)!important;overflow-x:hidden!important}.main-content[_ngcontent-%COMP%]{background:#fff!important;color:#262626!important;gap:0;padding:0 8px 40px;width:100%!important;max-width:100%!important;box-sizing:border-box;overflow:visible!important}.mobile-sidebar[_ngcontent-%COMP%]{width:90%;background:#fff;color:#262626}.sidebar-overlay[_ngcontent-%COMP%]{background:#000c}}@media (max-width: 480px){.mobile-sidebar[_ngcontent-%COMP%]{width:95%}.mobile-header[_ngcontent-%COMP%]   .header-right[_ngcontent-%COMP%]{gap:12px}}@media (max-width: 480px) and (max-width: 480px){.content-grid[_ngcontent-%COMP%]{padding:160px 0 60px!important;min-height:calc(100vh - 220px)!important}.instagram-stories-section[_ngcontent-%COMP%]{height:100px!important;padding:8px 0!important}app-view-add-stories[_ngcontent-%COMP%]{display:block!important;width:100%!important;padding:8px 0!important}}@media (max-width: 480px) and (max-width: 400px){.content-grid[_ngcontent-%COMP%]{padding:160px 0 60px!important;min-height:calc(100vh - 220px)!important}.instagram-stories-section[_ngcontent-%COMP%]{height:100px!important;padding:8px 0!important}app-view-add-stories[_ngcontent-%COMP%]{display:block!important;width:100%!important;padding:8px 0!important}.main-content[_ngcontent-%COMP%]{width:100%!important;max-width:100%!important;overflow-x:hidden!important}}@media (max-width: 480px) and (max-width: 360px){.content-grid[_ngcontent-%COMP%]{padding:160px 0 80px!important;min-height:calc(100vh - 240px)!important}.main-content[_ngcontent-%COMP%]{padding:0 4px 50px!important;overflow:visible!important}.instagram-stories-section[_ngcontent-%COMP%]{height:100px!important;padding:8px 0!important}app-view-add-stories[_ngcontent-%COMP%]{display:block!important;width:100%!important;padding:8px 0!important}}@media (max-width: 480px) and (max-width: 425px) and (min-width: 320px){.content-grid[_ngcontent-%COMP%]{padding:160px 0 100px!important;min-height:calc(100vh - 260px)!important}.main-content[_ngcontent-%COMP%]{padding:0 6px 60px!important;overflow:visible!important;position:relative}app-feed[_ngcontent-%COMP%]{padding-bottom:40px!important;overflow:visible!important}}@media (max-width: 480px) and (max-width: 320px){.content-grid[_ngcontent-%COMP%]{padding:160px 0 120px!important;min-height:calc(100vh - 280px)!important}.main-content[_ngcontent-%COMP%]{padding:0 2px 70px!important;overflow:visible!important}app-feed[_ngcontent-%COMP%]{padding-bottom:50px!important;overflow:visible!important}}@media (min-width: 769px){.mobile-header[_ngcontent-%COMP%], .instagram-stories-section[_ngcontent-%COMP%], .instagram-bottom-nav[_ngcontent-%COMP%]{display:none!important;visibility:hidden!important;opacity:0!important}.desktop-sidebar[_ngcontent-%COMP%]{display:block}.mobile-sidebar[_ngcontent-%COMP%], .sidebar-overlay[_ngcontent-%COMP%]{display:none!important}.home-container[_ngcontent-%COMP%]{background:#fff;padding:20px 20px 0}.content-grid[_ngcontent-%COMP%]{background:#fff;color:#262626;padding:0;margin:0 auto;grid-template-columns:1fr 300px;gap:32px;max-width:1000px}}@media (min-width: 769px) and (min-width: 1024px){.content-grid[_ngcontent-%COMP%]{grid-template-columns:1fr 350px;gap:36px;max-width:1200px}}@media (min-width: 769px) and (min-width: 1200px){.content-grid[_ngcontent-%COMP%]{grid-template-columns:1fr 400px;gap:40px;max-width:1400px}}@media (min-width: 769px) and (min-width: 1440px){.content-grid[_ngcontent-%COMP%]{grid-template-columns:1fr 450px;gap:48px;max-width:1600px}}@media (min-width: 769px){.main-content[_ngcontent-%COMP%]{background:#fff;color:#262626}.instagram-tab-menu[_ngcontent-%COMP%], .tab-menu-overlay[_ngcontent-%COMP%], .sidebar-content-modal[_ngcontent-%COMP%]{display:none!important}}@media (min-width: 320px) and (max-width: 768px){.home-container.mobile-instagram[_ngcontent-%COMP%]   .content-grid[_ngcontent-%COMP%]{grid-template-columns:1fr!important;padding:5px 0!important;background:#fff!important;gap:0!important;margin:0!important;max-width:100%!important;min-height:calc(100vh - 220px)!important;overflow-x:hidden!important;position:relative!important;z-index:1!important}}"]})}}return i})()}}]);