{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../core/services/auth.service\";\nimport * as i2 from \"../../../core/services/cart.service\";\nimport * as i3 from \"../../../core/services/wishlist-new.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/forms\";\nfunction HeaderComponent_div_10_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵlistener(\"click\", function HeaderComponent_div_10_div_1_Template_div_click_0_listener() {\n      const suggestion_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.selectSuggestion(suggestion_r2));\n    });\n    i0.ɵɵelement(1, \"i\", 32);\n    i0.ɵɵelementStart(2, \"span\", 33);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 34);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const suggestion_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.getSuggestionIcon(suggestion_r2.type));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(suggestion_r2.text);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(suggestion_r2.type);\n  }\n}\nfunction HeaderComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵtemplate(1, HeaderComponent_div_10_div_1_Template, 6, 3, \"div\", 30);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.searchSuggestions);\n  }\n}\nfunction HeaderComponent_span_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r2.wishlistItemCount);\n  }\n}\nfunction HeaderComponent_span_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 36);\n    i0.ɵɵtext(1, \"0\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HeaderComponent_span_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 37);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r2.cartItemCount);\n  }\n}\nfunction HeaderComponent_span_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 38);\n    i0.ɵɵtext(1, \"0\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HeaderComponent_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"span\", 40);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.getFormattedCartTotal());\n  }\n}\nfunction HeaderComponent_div_37_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 44);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r2.getTotalItemCount());\n  }\n}\nfunction HeaderComponent_div_37_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 45);\n    i0.ɵɵtext(1, \"0\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HeaderComponent_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41);\n    i0.ɵɵelement(1, \"i\", 16);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"Total\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, HeaderComponent_div_37_span_4_Template, 2, 1, \"span\", 42)(5, HeaderComponent_div_37_span_5_Template, 2, 0, \"span\", 43);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getTotalItemCount() > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getTotalItemCount() === 0);\n  }\n}\nfunction HeaderComponent_div_38_a_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 61);\n    i0.ɵɵelement(1, \"i\", 62);\n    i0.ɵɵtext(2, \" Vendor Dashboard \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HeaderComponent_div_38_a_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 63);\n    i0.ɵɵelement(1, \"i\", 64);\n    i0.ɵɵtext(2, \" Admin Panel \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HeaderComponent_div_38_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 55);\n  }\n}\nfunction HeaderComponent_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 46);\n    i0.ɵɵlistener(\"click\", function HeaderComponent_div_38_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleUserMenu());\n    });\n    i0.ɵɵelementStart(1, \"img\", 47);\n    i0.ɵɵlistener(\"error\", function HeaderComponent_div_38_Template_img_error_1_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onAvatarError($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"span\", 48);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"i\", 49);\n    i0.ɵɵelementStart(5, \"div\", 50)(6, \"a\", 51);\n    i0.ɵɵelement(7, \"i\", 52);\n    i0.ɵɵtext(8, \" Profile \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"a\", 53);\n    i0.ɵɵelement(10, \"i\", 54);\n    i0.ɵɵtext(11, \" Settings \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(12, \"div\", 55);\n    i0.ɵɵtemplate(13, HeaderComponent_div_38_a_13_Template, 3, 0, \"a\", 56)(14, HeaderComponent_div_38_a_14_Template, 3, 0, \"a\", 57)(15, HeaderComponent_div_38_div_15_Template, 1, 0, \"div\", 58);\n    i0.ɵɵelementStart(16, \"button\", 59);\n    i0.ɵɵlistener(\"click\", function HeaderComponent_div_38_Template_button_click_16_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.logout());\n    });\n    i0.ɵɵelement(17, \"i\", 60);\n    i0.ɵɵtext(18, \" Logout \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", ctx_r2.currentUser.avatar || \"assets/images/default-avatar.svg\", i0.ɵɵsanitizeUrl)(\"alt\", ctx_r2.currentUser.fullName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.currentUser.username);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"show\", ctx_r2.showUserMenu);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.currentUser.role === \"vendor\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.currentUser.role === \"admin\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.currentUser.role !== \"customer\");\n  }\n}\nfunction HeaderComponent_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65)(1, \"a\", 66);\n    i0.ɵɵtext(2, \"Login\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"a\", 67);\n    i0.ɵɵtext(4, \"Sign Up\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class HeaderComponent {\n  constructor(authService, cartService, wishlistService, router) {\n    this.authService = authService;\n    this.cartService = cartService;\n    this.wishlistService = wishlistService;\n    this.router = router;\n    this.currentUser = null;\n    this.searchQuery = '';\n    this.showUserMenu = false;\n    this.cartItemCount = 0;\n    this.wishlistItemCount = 0;\n    this.totalItemCount = 0;\n    this.cartTotalAmount = 0;\n    this.showCartTotalPrice = false;\n    // Search functionality\n    this.showSuggestions = false;\n    this.searchSuggestions = [];\n  }\n  ngOnInit() {\n    // Subscribe to user changes and refresh counts on login\n    this.authService.currentUser$.subscribe(user => {\n      const wasLoggedOut = !this.currentUser;\n      this.currentUser = user;\n      // If user just logged in, refresh total count\n      if (user && wasLoggedOut) {\n        console.log('🔄 User logged in, refreshing total count...');\n        setTimeout(() => {\n          this.cartService.refreshTotalCount();\n        }, 100);\n      } else if (!user && !wasLoggedOut) {\n        // User logged out, reset total count\n        console.log('🔄 User logged out, resetting total count...');\n        this.totalItemCount = 0;\n      }\n    });\n    // Subscribe to individual cart count\n    this.cartService.cartItemCount$.subscribe(count => {\n      this.cartItemCount = count;\n      console.log('🛒 Header cart count updated:', count);\n    });\n    // Subscribe to individual wishlist count\n    this.wishlistService.wishlistItemCount$.subscribe(count => {\n      this.wishlistItemCount = count;\n      console.log('💝 Header wishlist count updated:', count);\n    });\n    // Subscribe to total count (cart + wishlist)\n    this.cartService.totalItemCount$.subscribe(count => {\n      this.totalItemCount = count;\n      console.log('🔢 Header total count updated:', count);\n    });\n    // Subscribe to cart total amount\n    this.cartService.cartTotalAmount$.subscribe(amount => {\n      this.cartTotalAmount = amount;\n      console.log('💰 Header cart total amount updated:', amount);\n    });\n    // Subscribe to cart price display flag\n    this.cartService.showCartTotalPrice$.subscribe(showPrice => {\n      this.showCartTotalPrice = showPrice;\n      console.log('💲 Header show cart total price updated:', showPrice);\n    });\n    // Refresh counts when user logs in\n    if (this.currentUser) {\n      this.cartService.refreshTotalCount();\n      this.wishlistService.refreshWishlistOnLogin();\n    }\n    // Load cart and wishlist on init\n    this.cartService.loadCart();\n    this.wishlistService.loadWishlist();\n    // Close dropdown when clicking outside\n    document.addEventListener('click', event => {\n      const target = event.target;\n      if (!target.closest('.user-menu')) {\n        this.showUserMenu = false;\n      }\n    });\n  }\n  toggleUserMenu() {\n    this.showUserMenu = !this.showUserMenu;\n  }\n  openSearch() {\n    this.router.navigate(['/search']);\n  }\n  // Get total count for display (cart + wishlist items for logged-in user)\n  getTotalItemCount() {\n    if (!this.currentUser) {\n      return 0; // Return 0 if user is not logged in\n    }\n    return this.totalItemCount || 0;\n  }\n  // Get formatted cart total amount\n  getFormattedCartTotal() {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(this.cartTotalAmount || 0);\n  }\n  // Check if cart total price should be displayed\n  shouldShowCartTotalPrice() {\n    return this.currentUser !== null && this.showCartTotalPrice;\n  }\n  onSearch() {\n    if (this.searchQuery.trim()) {\n      this.router.navigate(['/search'], {\n        queryParams: {\n          q: this.searchQuery\n        }\n      });\n      this.hideSuggestions();\n    } else {\n      this.router.navigate(['/search']);\n    }\n  }\n  onSearchInput() {\n    if (this.searchTimeout) {\n      clearTimeout(this.searchTimeout);\n    }\n    this.searchTimeout = setTimeout(() => {\n      if (this.searchQuery.trim().length > 2) {\n        this.loadSearchSuggestions();\n      } else {\n        this.hideSuggestions();\n      }\n    }, 300);\n  }\n  onSearchFocus() {\n    if (this.searchQuery.trim().length > 2) {\n      this.showSuggestions = true;\n    }\n  }\n  onSearchBlur() {\n    // Delay hiding to allow clicking on suggestions\n    setTimeout(() => {\n      this.hideSuggestions();\n    }, 200);\n  }\n  loadSearchSuggestions() {\n    // Simulate API call for search suggestions\n    const query = this.searchQuery.toLowerCase();\n    // Mock suggestions based on query\n    this.searchSuggestions = [{\n      text: `${this.searchQuery} in Products`,\n      type: 'product',\n      icon: 'fa-shopping-bag'\n    }, {\n      text: `${this.searchQuery} in Brands`,\n      type: 'brand',\n      icon: 'fa-tags'\n    }, {\n      text: `${this.searchQuery} in Categories`,\n      type: 'category',\n      icon: 'fa-list'\n    }];\n    this.showSuggestions = true;\n  }\n  selectSuggestion(suggestion) {\n    this.searchQuery = suggestion.text;\n    this.router.navigate(['/search'], {\n      queryParams: {\n        q: this.searchQuery,\n        type: suggestion.type\n      }\n    });\n    this.hideSuggestions();\n  }\n  getSuggestionIcon(type) {\n    switch (type) {\n      case 'product':\n        return 'fa-shopping-bag';\n      case 'brand':\n        return 'fa-tags';\n      case 'category':\n        return 'fa-list';\n      default:\n        return 'fa-search';\n    }\n  }\n  hideSuggestions() {\n    this.showSuggestions = false;\n    this.searchSuggestions = [];\n  }\n  logout() {\n    this.authService.logout();\n    this.showUserMenu = false;\n  }\n  static {\n    this.ɵfac = function HeaderComponent_Factory(t) {\n      return new (t || HeaderComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.CartService), i0.ɵɵdirectiveInject(i3.WishlistNewService), i0.ɵɵdirectiveInject(i4.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HeaderComponent,\n      selectors: [[\"app-header\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 40,\n      vars: 10,\n      consts: [[1, \"header\"], [1, \"container\"], [1, \"header-content\"], [1, \"logo\"], [\"routerLink\", \"/home\"], [1, \"gradient-text\"], [1, \"search-bar\"], [1, \"fas\", \"fa-search\"], [\"type\", \"text\", \"placeholder\", \"Search products, brands, categories...\", 3, \"ngModelChange\", \"keyup.enter\", \"input\", \"focus\", \"blur\", \"ngModel\"], [\"class\", \"search-suggestions\", 4, \"ngIf\"], [1, \"nav-menu\"], [\"routerLink\", \"/home\", \"routerLinkActive\", \"active\", 1, \"nav-item\"], [1, \"fas\", \"fa-home\"], [\"routerLink\", \"/explore\", \"routerLinkActive\", \"active\", 1, \"nav-item\"], [1, \"fas\", \"fa-compass\"], [\"routerLink\", \"/shop\", \"routerLinkActive\", \"active\", 1, \"nav-item\"], [1, \"fas\", \"fa-shopping-bag\"], [\"routerLink\", \"/wishlist\", \"routerLinkActive\", \"active\", 1, \"nav-item\", \"wishlist-item\"], [1, \"fas\", \"fa-heart\"], [\"class\", \"wishlist-badge\", 4, \"ngIf\"], [\"class\", \"wishlist-badge zero\", 4, \"ngIf\"], [\"routerLink\", \"/cart\", \"routerLinkActive\", \"active\", 1, \"nav-item\", \"cart-item\"], [1, \"fas\", \"fa-shopping-cart\"], [\"class\", \"cart-badge\", 4, \"ngIf\"], [\"class\", \"cart-badge zero\", 4, \"ngIf\"], [\"class\", \"cart-total-display\", 4, \"ngIf\"], [\"class\", \"total-count-item\", 4, \"ngIf\"], [\"class\", \"user-menu\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"auth-buttons\", 4, \"ngIf\"], [1, \"search-suggestions\"], [\"class\", \"suggestion-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"suggestion-item\", 3, \"click\"], [1, \"fas\", 3, \"ngClass\"], [1, \"suggestion-text\"], [1, \"suggestion-type\"], [1, \"wishlist-badge\"], [1, \"wishlist-badge\", \"zero\"], [1, \"cart-badge\"], [1, \"cart-badge\", \"zero\"], [1, \"cart-total-display\"], [1, \"cart-total-text\"], [1, \"total-count-item\"], [\"class\", \"total-count-badge\", 4, \"ngIf\"], [\"class\", \"total-count-badge zero\", 4, \"ngIf\"], [1, \"total-count-badge\"], [1, \"total-count-badge\", \"zero\"], [1, \"user-menu\", 3, \"click\"], [1, \"user-avatar\", 3, \"error\", \"src\", \"alt\"], [1, \"username\"], [1, \"fas\", \"fa-chevron-down\"], [1, \"dropdown-menu\"], [\"routerLink\", \"/profile\", 1, \"dropdown-item\"], [1, \"fas\", \"fa-user\"], [\"routerLink\", \"/settings\", 1, \"dropdown-item\"], [1, \"fas\", \"fa-cog\"], [1, \"dropdown-divider\"], [\"routerLink\", \"/vendor/dashboard\", \"class\", \"dropdown-item\", 4, \"ngIf\"], [\"routerLink\", \"/admin\", \"class\", \"dropdown-item\", 4, \"ngIf\"], [\"class\", \"dropdown-divider\", 4, \"ngIf\"], [1, \"dropdown-item\", \"logout\", 3, \"click\"], [1, \"fas\", \"fa-sign-out-alt\"], [\"routerLink\", \"/vendor/dashboard\", 1, \"dropdown-item\"], [1, \"fas\", \"fa-store\"], [\"routerLink\", \"/admin\", 1, \"dropdown-item\"], [1, \"fas\", \"fa-shield-alt\"], [1, \"auth-buttons\"], [\"routerLink\", \"/auth/login\", 1, \"btn\", \"btn-outline\"], [\"routerLink\", \"/auth/register\", 1, \"btn\", \"btn-primary\"]],\n      template: function HeaderComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"header\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"a\", 4)(5, \"h1\", 5);\n          i0.ɵɵtext(6, \"DFashion\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(7, \"div\", 6);\n          i0.ɵɵelement(8, \"i\", 7);\n          i0.ɵɵelementStart(9, \"input\", 8);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function HeaderComponent_Template_input_ngModelChange_9_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery, $event) || (ctx.searchQuery = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"keyup.enter\", function HeaderComponent_Template_input_keyup_enter_9_listener() {\n            return ctx.onSearch();\n          })(\"input\", function HeaderComponent_Template_input_input_9_listener() {\n            return ctx.onSearchInput();\n          })(\"focus\", function HeaderComponent_Template_input_focus_9_listener() {\n            return ctx.onSearchFocus();\n          })(\"blur\", function HeaderComponent_Template_input_blur_9_listener() {\n            return ctx.onSearchBlur();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(10, HeaderComponent_div_10_Template, 2, 1, \"div\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"nav\", 10)(12, \"a\", 11);\n          i0.ɵɵelement(13, \"i\", 12);\n          i0.ɵɵelementStart(14, \"span\");\n          i0.ɵɵtext(15, \"Home\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(16, \"a\", 13);\n          i0.ɵɵelement(17, \"i\", 14);\n          i0.ɵɵelementStart(18, \"span\");\n          i0.ɵɵtext(19, \"Explore\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"a\", 15);\n          i0.ɵɵelement(21, \"i\", 16);\n          i0.ɵɵelementStart(22, \"span\");\n          i0.ɵɵtext(23, \"Shop\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(24, \"a\", 17);\n          i0.ɵɵelement(25, \"i\", 18);\n          i0.ɵɵelementStart(26, \"span\");\n          i0.ɵɵtext(27, \"Wishlist\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(28, HeaderComponent_span_28_Template, 2, 1, \"span\", 19)(29, HeaderComponent_span_29_Template, 2, 0, \"span\", 20);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"a\", 21);\n          i0.ɵɵelement(31, \"i\", 22);\n          i0.ɵɵelementStart(32, \"span\");\n          i0.ɵɵtext(33, \"Cart\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(34, HeaderComponent_span_34_Template, 2, 1, \"span\", 23)(35, HeaderComponent_span_35_Template, 2, 0, \"span\", 24)(36, HeaderComponent_div_36_Template, 3, 1, \"div\", 25);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(37, HeaderComponent_div_37_Template, 6, 2, \"div\", 26)(38, HeaderComponent_div_38_Template, 19, 8, \"div\", 27)(39, HeaderComponent_div_39_Template, 5, 0, \"div\", 28);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(9);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showSuggestions && ctx.searchSuggestions.length > 0);\n          i0.ɵɵadvance(18);\n          i0.ɵɵproperty(\"ngIf\", ctx.wishlistItemCount > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentUser && ctx.wishlistItemCount === 0);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.cartItemCount > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentUser && ctx.cartItemCount === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentUser && ctx.cartTotalAmount > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentUser);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentUser);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.currentUser);\n        }\n      },\n      dependencies: [CommonModule, i5.NgClass, i5.NgForOf, i5.NgIf, RouterModule, i4.RouterLink, i4.RouterLinkActive, FormsModule, i6.DefaultValueAccessor, i6.NgControlStatus, i6.NgModel],\n      styles: [\".header[_ngcontent-%COMP%] {\\n  background: #fff;\\n  border-bottom: 1px solid #dbdbdb;\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  z-index: 1000;\\n  height: 60px;\\n}\\n\\n.header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  height: 60px;\\n}\\n\\n.logo[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  text-decoration: none;\\n}\\n\\n.logo[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 700;\\n  margin: 0;\\n}\\n\\n.search-bar[_ngcontent-%COMP%] {\\n  position: relative;\\n  flex: 1;\\n  max-width: 400px;\\n  margin: 0 40px;\\n}\\n\\n.search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 8px 16px 8px 40px;\\n  border: 1px solid #dbdbdb;\\n  border-radius: 8px;\\n  background: #fafafa;\\n  font-size: 14px;\\n  outline: none;\\n  transition: all 0.2s;\\n}\\n\\n.search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus {\\n  background: #fff;\\n  border-color: var(--primary-color);\\n}\\n\\n.search-bar[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  position: absolute;\\n  left: 12px;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  color: #8e8e8e;\\n}\\n\\n.search-suggestions[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 100%;\\n  left: 0;\\n  right: 0;\\n  background: white;\\n  border-radius: 8px;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\\n  z-index: 1000;\\n  margin-top: 5px;\\n  max-height: 300px;\\n  overflow-y: auto;\\n  border: 1px solid #e0e0e0;\\n}\\n\\n.suggestion-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 12px 16px;\\n  cursor: pointer;\\n  border-bottom: 1px solid #f0f0f0;\\n  transition: background-color 0.2s ease;\\n}\\n\\n.suggestion-item[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n}\\n\\n.suggestion-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n\\n.suggestion-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  margin-right: 12px;\\n  width: 16px;\\n  position: static;\\n  transform: none;\\n}\\n\\n.suggestion-text[_ngcontent-%COMP%] {\\n  flex: 1;\\n  font-size: 14px;\\n  color: #333;\\n}\\n\\n.suggestion-type[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #6c757d;\\n  text-transform: uppercase;\\n  font-weight: 500;\\n}\\n\\n.nav-menu[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 24px;\\n}\\n\\n.nav-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  text-decoration: none;\\n  color: #262626;\\n  font-size: 12px;\\n  transition: color 0.2s;\\n  padding: 8px;\\n  border-radius: 4px;\\n}\\n\\n.nav-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  margin-bottom: 4px;\\n}\\n\\n.nav-item.active[_ngcontent-%COMP%], .nav-item[_ngcontent-%COMP%]:hover {\\n  color: var(--primary-color);\\n}\\n\\n.cart-item[_ngcontent-%COMP%], .wishlist-item[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n\\n.cart-badge[_ngcontent-%COMP%], .wishlist-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -2px;\\n  right: -2px;\\n  background: #ef4444;\\n  color: white;\\n  font-size: 10px;\\n  font-weight: 600;\\n  padding: 2px 6px;\\n  border-radius: 10px;\\n  min-width: 16px;\\n  text-align: center;\\n  line-height: 1.2;\\n}\\n\\n.cart-badge.zero[_ngcontent-%COMP%], .wishlist-badge.zero[_ngcontent-%COMP%] {\\n  background: #6c757d;\\n}\\n\\n.total-count-item[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  padding: 8px 12px;\\n  background: linear-gradient(135deg, #4834d4, #686de0);\\n  color: white;\\n  border-radius: 8px;\\n  font-size: 14px;\\n  font-weight: 600;\\n  margin-left: 16px;\\n}\\n\\n.total-count-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n\\n.total-count-badge[_ngcontent-%COMP%] {\\n  background: #28a745;\\n  color: white;\\n  font-size: 12px;\\n  font-weight: 700;\\n  padding: 2px 8px;\\n  border-radius: 12px;\\n  min-width: 20px;\\n  text-align: center;\\n  margin-left: auto;\\n}\\n\\n.total-count-badge.zero[_ngcontent-%COMP%] {\\n  background: #6c757d;\\n}\\n\\n.cart-total-display[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 100%;\\n  right: 0;\\n  background: #28a745;\\n  color: white;\\n  font-size: 9px;\\n  font-weight: 600;\\n  padding: 2px 4px;\\n  border-radius: 4px;\\n  white-space: nowrap;\\n  margin-top: 2px;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n\\n.cart-total-text[_ngcontent-%COMP%] {\\n  font-size: 9px;\\n}\\n\\n.total-count-display[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n  padding: 4px 8px;\\n  background: linear-gradient(135deg, #4834d4, #686de0);\\n  color: white;\\n  border-radius: 12px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  margin-left: 8px;\\n}\\n\\n.total-count-display[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n}\\n\\n.total-count-text[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  min-width: 16px;\\n  text-align: center;\\n}\\n\\n.auth-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n\\n.btn[_ngcontent-%COMP%] {\\n  padding: 8px 16px;\\n  border-radius: 6px;\\n  text-decoration: none;\\n  font-size: 14px;\\n  font-weight: 500;\\n  transition: all 0.2s;\\n  border: 1px solid transparent;\\n}\\n\\n.btn-outline[_ngcontent-%COMP%] {\\n  color: var(--primary-color);\\n  border-color: var(--primary-color);\\n  background: transparent;\\n}\\n\\n.btn-outline[_ngcontent-%COMP%]:hover {\\n  background: var(--primary-color);\\n  color: white;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%] {\\n  background: var(--primary-color);\\n  color: white;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%]:hover {\\n  background: var(--primary-dark);\\n}\\n\\n.user-menu[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  cursor: pointer;\\n  padding: 8px 12px;\\n  border-radius: 8px;\\n  transition: background 0.2s;\\n}\\n\\n.user-menu[_ngcontent-%COMP%]:hover {\\n  background: #f1f5f9;\\n}\\n\\n.user-avatar[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n}\\n\\n.username[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  font-size: 14px;\\n}\\n\\n.user-menu[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #64748b;\\n  transition: transform 0.2s;\\n}\\n\\n.user-menu.active[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  transform: rotate(180deg);\\n}\\n\\n.dropdown-menu[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 100%;\\n  right: 0;\\n  background: #fff;\\n  border: 1px solid #e2e8f0;\\n  border-radius: 8px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\\n  min-width: 200px;\\n  opacity: 0;\\n  visibility: hidden;\\n  transform: translateY(-10px);\\n  transition: all 0.2s;\\n  z-index: 1000;\\n}\\n\\n.dropdown-menu.show[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  visibility: visible;\\n  transform: translateY(0);\\n}\\n\\n.dropdown-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  padding: 12px 16px;\\n  text-decoration: none;\\n  color: #262626;\\n  font-size: 14px;\\n  transition: background 0.2s;\\n  border: none;\\n  background: none;\\n  width: 100%;\\n  text-align: left;\\n  cursor: pointer;\\n}\\n\\n.dropdown-item[_ngcontent-%COMP%]:hover {\\n  background: #f8fafc;\\n}\\n\\n.dropdown-item.logout[_ngcontent-%COMP%] {\\n  color: #ef4444;\\n}\\n\\n.dropdown-item.logout[_ngcontent-%COMP%]:hover {\\n  background: #fef2f2;\\n}\\n\\n.dropdown-divider[_ngcontent-%COMP%] {\\n  height: 1px;\\n  background: #e2e8f0;\\n  margin: 8px 0;\\n}\\n\\n@media (max-width: 768px) {\\n  .search-bar[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  .nav-menu[_ngcontent-%COMP%] {\\n    gap: 16px;\\n  }\\n  .nav-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  .username[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  .cart-item[_ngcontent-%COMP%], .wishlist-item[_ngcontent-%COMP%] {\\n    position: relative;\\n  }\\n  .cart-badge[_ngcontent-%COMP%], .wishlist-badge[_ngcontent-%COMP%] {\\n    font-size: 8px;\\n    padding: 1px 4px;\\n    min-width: 12px;\\n  }\\n  .total-count-item[_ngcontent-%COMP%] {\\n    padding: 6px 8px;\\n    font-size: 12px;\\n    margin-left: 8px;\\n  }\\n  .total-count-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:not(.total-count-badge) {\\n    display: none;\\n  }\\n  .total-count-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n  .total-count-badge[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n    padding: 1px 6px;\\n    min-width: 16px;\\n  }\\n  .cart-total-display[_ngcontent-%COMP%] {\\n    font-size: 8px;\\n    padding: 1px 3px;\\n  }\\n  .cart-total-text[_ngcontent-%COMP%] {\\n    font-size: 8px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "FormsModule", "i0", "ɵɵelementStart", "ɵɵlistener", "HeaderComponent_div_10_div_1_Template_div_click_0_listener", "suggestion_r2", "ɵɵrestoreView", "_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "selectSuggestion", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "getSuggestionIcon", "type", "ɵɵtextInterpolate", "text", "ɵɵtemplate", "HeaderComponent_div_10_div_1_Template", "searchSuggestions", "wishlistItemCount", "cartItemCount", "getFormattedCartTotal", "getTotalItemCount", "HeaderComponent_div_37_span_4_Template", "HeaderComponent_div_37_span_5_Template", "HeaderComponent_div_38_Template_div_click_0_listener", "_r4", "toggleUserMenu", "HeaderComponent_div_38_Template_img_error_1_listener", "$event", "onAvatarError", "HeaderComponent_div_38_a_13_Template", "HeaderComponent_div_38_a_14_Template", "HeaderComponent_div_38_div_15_Template", "HeaderComponent_div_38_Template_button_click_16_listener", "logout", "currentUser", "avatar", "ɵɵsanitizeUrl", "fullName", "username", "ɵɵclassProp", "showUserMenu", "role", "HeaderComponent", "constructor", "authService", "cartService", "wishlistService", "router", "searchQuery", "totalItemCount", "cartTotalAmount", "showCartTotalPrice", "showSuggestions", "ngOnInit", "currentUser$", "subscribe", "user", "wasLoggedOut", "console", "log", "setTimeout", "refreshTotalCount", "cartItemCount$", "count", "wishlistItemCount$", "totalItemCount$", "cartTotalAmount$", "amount", "showCartTotalPrice$", "showPrice", "refreshWishlistOnLogin", "loadCart", "loadWishlist", "document", "addEventListener", "event", "target", "closest", "openSearch", "navigate", "Intl", "NumberFormat", "style", "currency", "format", "shouldShowCartTotalPrice", "onSearch", "trim", "queryParams", "q", "hideSuggestions", "onSearchInput", "searchTimeout", "clearTimeout", "length", "loadSearchSuggestions", "onSearchFocus", "onSearchBlur", "query", "toLowerCase", "icon", "suggestion", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "CartService", "i3", "WishlistNewService", "i4", "Router", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "HeaderComponent_Template", "rf", "ctx", "ɵɵtwoWayListener", "HeaderComponent_Template_input_ngModelChange_9_listener", "ɵɵtwoWayBindingSet", "HeaderComponent_Template_input_keyup_enter_9_listener", "HeaderComponent_Template_input_input_9_listener", "HeaderComponent_Template_input_focus_9_listener", "HeaderComponent_Template_input_blur_9_listener", "HeaderComponent_div_10_Template", "HeaderComponent_span_28_Template", "HeaderComponent_span_29_Template", "HeaderComponent_span_34_Template", "HeaderComponent_span_35_Template", "HeaderComponent_div_36_Template", "HeaderComponent_div_37_Template", "HeaderComponent_div_38_Template", "HeaderComponent_div_39_Template", "ɵɵtwoWayProperty", "i5", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "RouterLink", "RouterLinkActive", "i6", "DefaultValueAccessor", "NgControlStatus", "NgModel", "styles"], "sources": ["E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\shared\\components\\header\\header.component.ts", "E:\\Fashion\\DFashionFrontend\\frontend\\src\\app\\shared\\components\\header\\header.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule, Router } from '@angular/router';\nimport { FormsModule } from '@angular/forms';\n\nimport { AuthService } from '../../../core/services/auth.service';\nimport { CartService } from '../../../core/services/cart.service';\nimport { WishlistNewService } from '../../../core/services/wishlist-new.service';\nimport { User } from '../../../core/models/user.model';\n\n@Component({\n  selector: 'app-header',\n  standalone: true,\n  imports: [CommonModule, RouterModule, FormsModule],\n  templateUrl: './header.component.html',\n  styles: [`\n    .header {\n      background: #fff;\n      border-bottom: 1px solid #dbdbdb;\n      position: fixed;\n      top: 0;\n      left: 0;\n      right: 0;\n      z-index: 1000;\n      height: 60px;\n    }\n\n    .header-content {\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n      height: 60px;\n    }\n\n    .logo a {\n      text-decoration: none;\n    }\n\n    .logo h1 {\n      font-size: 24px;\n      font-weight: 700;\n      margin: 0;\n    }\n\n    .search-bar {\n      position: relative;\n      flex: 1;\n      max-width: 400px;\n      margin: 0 40px;\n    }\n\n    .search-bar input {\n      width: 100%;\n      padding: 8px 16px 8px 40px;\n      border: 1px solid #dbdbdb;\n      border-radius: 8px;\n      background: #fafafa;\n      font-size: 14px;\n      outline: none;\n      transition: all 0.2s;\n    }\n\n    .search-bar input:focus {\n      background: #fff;\n      border-color: var(--primary-color);\n    }\n\n    .search-bar i {\n      position: absolute;\n      left: 12px;\n      top: 50%;\n      transform: translateY(-50%);\n      color: #8e8e8e;\n    }\n\n    .search-suggestions {\n      position: absolute;\n      top: 100%;\n      left: 0;\n      right: 0;\n      background: white;\n      border-radius: 8px;\n      box-shadow: 0 4px 20px rgba(0,0,0,0.1);\n      z-index: 1000;\n      margin-top: 5px;\n      max-height: 300px;\n      overflow-y: auto;\n      border: 1px solid #e0e0e0;\n    }\n\n    .suggestion-item {\n      display: flex;\n      align-items: center;\n      padding: 12px 16px;\n      cursor: pointer;\n      border-bottom: 1px solid #f0f0f0;\n      transition: background-color 0.2s ease;\n    }\n\n    .suggestion-item:hover {\n      background-color: #f8f9fa;\n    }\n\n    .suggestion-item:last-child {\n      border-bottom: none;\n    }\n\n    .suggestion-item i {\n      color: #6c757d;\n      margin-right: 12px;\n      width: 16px;\n      position: static;\n      transform: none;\n    }\n\n    .suggestion-text {\n      flex: 1;\n      font-size: 14px;\n      color: #333;\n    }\n\n    .suggestion-type {\n      font-size: 12px;\n      color: #6c757d;\n      text-transform: uppercase;\n      font-weight: 500;\n    }\n\n    .nav-menu {\n      display: flex;\n      align-items: center;\n      gap: 24px;\n    }\n\n    .nav-item {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      text-decoration: none;\n      color: #262626;\n      font-size: 12px;\n      transition: color 0.2s;\n      padding: 8px;\n      border-radius: 4px;\n    }\n\n    .nav-item i {\n      font-size: 20px;\n      margin-bottom: 4px;\n    }\n\n    .nav-item.active,\n    .nav-item:hover {\n      color: var(--primary-color);\n    }\n\n    .cart-item,\n    .wishlist-item {\n      position: relative;\n    }\n\n    .cart-badge,\n    .wishlist-badge {\n      position: absolute;\n      top: -2px;\n      right: -2px;\n      background: #ef4444;\n      color: white;\n      font-size: 10px;\n      font-weight: 600;\n      padding: 2px 6px;\n      border-radius: 10px;\n      min-width: 16px;\n      text-align: center;\n      line-height: 1.2;\n    }\n\n    .cart-badge.zero,\n    .wishlist-badge.zero {\n      background: #6c757d;\n    }\n\n    .total-count-item {\n      position: relative;\n      display: flex;\n      align-items: center;\n      gap: 8px;\n      padding: 8px 12px;\n      background: linear-gradient(135deg, #4834d4, #686de0);\n      color: white;\n      border-radius: 8px;\n      font-size: 14px;\n      font-weight: 600;\n      margin-left: 16px;\n    }\n\n    .total-count-item i {\n      font-size: 16px;\n    }\n\n    .total-count-badge {\n      background: #28a745;\n      color: white;\n      font-size: 12px;\n      font-weight: 700;\n      padding: 2px 8px;\n      border-radius: 12px;\n      min-width: 20px;\n      text-align: center;\n      margin-left: auto;\n    }\n\n    .total-count-badge.zero {\n      background: #6c757d;\n    }\n\n    .cart-total-display {\n      position: absolute;\n      top: 100%;\n      right: 0;\n      background: #28a745;\n      color: white;\n      font-size: 9px;\n      font-weight: 600;\n      padding: 2px 4px;\n      border-radius: 4px;\n      white-space: nowrap;\n      margin-top: 2px;\n      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n    }\n\n    .cart-total-text {\n      font-size: 9px;\n    }\n\n    .total-count-display {\n      display: flex;\n      align-items: center;\n      gap: 4px;\n      padding: 4px 8px;\n      background: linear-gradient(135deg, #4834d4, #686de0);\n      color: white;\n      border-radius: 12px;\n      font-size: 12px;\n      font-weight: 600;\n      margin-left: 8px;\n    }\n\n    .total-count-display i {\n      font-size: 12px;\n    }\n\n    .total-count-text {\n      font-size: 12px;\n      min-width: 16px;\n      text-align: center;\n    }\n\n    .auth-buttons {\n      display: flex;\n      align-items: center;\n      gap: 12px;\n    }\n\n    .btn {\n      padding: 8px 16px;\n      border-radius: 6px;\n      text-decoration: none;\n      font-size: 14px;\n      font-weight: 500;\n      transition: all 0.2s;\n      border: 1px solid transparent;\n    }\n\n    .btn-outline {\n      color: var(--primary-color);\n      border-color: var(--primary-color);\n      background: transparent;\n    }\n\n    .btn-outline:hover {\n      background: var(--primary-color);\n      color: white;\n    }\n\n    .btn-primary {\n      background: var(--primary-color);\n      color: white;\n    }\n\n    .btn-primary:hover {\n      background: var(--primary-dark);\n    }\n\n    .user-menu {\n      position: relative;\n      display: flex;\n      align-items: center;\n      gap: 8px;\n      cursor: pointer;\n      padding: 8px 12px;\n      border-radius: 8px;\n      transition: background 0.2s;\n    }\n\n    .user-menu:hover {\n      background: #f1f5f9;\n    }\n\n    .user-avatar {\n      width: 32px;\n      height: 32px;\n      border-radius: 50%;\n      object-fit: cover;\n    }\n\n    .username {\n      font-weight: 500;\n      font-size: 14px;\n    }\n\n    .user-menu i {\n      font-size: 12px;\n      color: #64748b;\n      transition: transform 0.2s;\n    }\n\n    .user-menu.active i {\n      transform: rotate(180deg);\n    }\n\n    .dropdown-menu {\n      position: absolute;\n      top: 100%;\n      right: 0;\n      background: #fff;\n      border: 1px solid #e2e8f0;\n      border-radius: 8px;\n      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n      min-width: 200px;\n      opacity: 0;\n      visibility: hidden;\n      transform: translateY(-10px);\n      transition: all 0.2s;\n      z-index: 1000;\n    }\n\n    .dropdown-menu.show {\n      opacity: 1;\n      visibility: visible;\n      transform: translateY(0);\n    }\n\n    .dropdown-item {\n      display: flex;\n      align-items: center;\n      gap: 12px;\n      padding: 12px 16px;\n      text-decoration: none;\n      color: #262626;\n      font-size: 14px;\n      transition: background 0.2s;\n      border: none;\n      background: none;\n      width: 100%;\n      text-align: left;\n      cursor: pointer;\n    }\n\n    .dropdown-item:hover {\n      background: #f8fafc;\n    }\n\n    .dropdown-item.logout {\n      color: #ef4444;\n    }\n\n    .dropdown-item.logout:hover {\n      background: #fef2f2;\n    }\n\n    .dropdown-divider {\n      height: 1px;\n      background: #e2e8f0;\n      margin: 8px 0;\n    }\n\n    @media (max-width: 768px) {\n      .search-bar {\n        display: none;\n      }\n\n      .nav-menu {\n        gap: 16px;\n      }\n\n      .nav-item span {\n        display: none;\n      }\n\n      .username {\n        display: none;\n      }\n\n      .cart-item,\n      .wishlist-item {\n        position: relative;\n      }\n\n      .cart-badge,\n      .wishlist-badge {\n        font-size: 8px;\n        padding: 1px 4px;\n        min-width: 12px;\n      }\n\n      .total-count-item {\n        padding: 6px 8px;\n        font-size: 12px;\n        margin-left: 8px;\n      }\n\n      .total-count-item span:not(.total-count-badge) {\n        display: none;\n      }\n\n      .total-count-item i {\n        font-size: 14px;\n      }\n\n      .total-count-badge {\n        font-size: 10px;\n        padding: 1px 6px;\n        min-width: 16px;\n      }\n\n      .cart-total-display {\n        font-size: 8px;\n        padding: 1px 3px;\n      }\n\n      .cart-total-text {\n        font-size: 8px;\n      }\n    }\n  `]\n})\nexport class HeaderComponent implements OnInit {\n  currentUser: User | null = null;\n  searchQuery = '';\n  showUserMenu = false;\n  cartItemCount = 0;\n  wishlistItemCount = 0;\n  totalItemCount = 0;\n  cartTotalAmount = 0;\n  showCartTotalPrice = false;\n\n  // Search functionality\n  showSuggestions = false;\n  searchSuggestions: any[] = [];\n  searchTimeout: any;\n\n  constructor(\n    private authService: AuthService,\n    private cartService: CartService,\n    private wishlistService: WishlistNewService,\n    private router: Router\n  ) {}\n\n  ngOnInit() {\n    // Subscribe to user changes and refresh counts on login\n    this.authService.currentUser$.subscribe(user => {\n      const wasLoggedOut = !this.currentUser;\n      this.currentUser = user;\n\n      // If user just logged in, refresh total count\n      if (user && wasLoggedOut) {\n        console.log('🔄 User logged in, refreshing total count...');\n        setTimeout(() => {\n          this.cartService.refreshTotalCount();\n        }, 100);\n      } else if (!user && !wasLoggedOut) {\n        // User logged out, reset total count\n        console.log('🔄 User logged out, resetting total count...');\n        this.totalItemCount = 0;\n      }\n    });\n\n    // Subscribe to individual cart count\n    this.cartService.cartItemCount$.subscribe((count: number) => {\n      this.cartItemCount = count;\n      console.log('🛒 Header cart count updated:', count);\n    });\n\n    // Subscribe to individual wishlist count\n    this.wishlistService.wishlistItemCount$.subscribe((count: number) => {\n      this.wishlistItemCount = count;\n      console.log('💝 Header wishlist count updated:', count);\n    });\n\n    // Subscribe to total count (cart + wishlist)\n    this.cartService.totalItemCount$.subscribe((count: number) => {\n      this.totalItemCount = count;\n      console.log('🔢 Header total count updated:', count);\n    });\n\n    // Subscribe to cart total amount\n    this.cartService.cartTotalAmount$.subscribe((amount: number) => {\n      this.cartTotalAmount = amount;\n      console.log('💰 Header cart total amount updated:', amount);\n    });\n\n    // Subscribe to cart price display flag\n    this.cartService.showCartTotalPrice$.subscribe((showPrice: boolean) => {\n      this.showCartTotalPrice = showPrice;\n      console.log('💲 Header show cart total price updated:', showPrice);\n    });\n\n    // Refresh counts when user logs in\n    if (this.currentUser) {\n      this.cartService.refreshTotalCount();\n      this.wishlistService.refreshWishlistOnLogin();\n    }\n\n    // Load cart and wishlist on init\n    this.cartService.loadCart();\n    this.wishlistService.loadWishlist();\n\n    // Close dropdown when clicking outside\n    document.addEventListener('click', (event) => {\n      const target = event.target as HTMLElement;\n      if (!target.closest('.user-menu')) {\n        this.showUserMenu = false;\n      }\n    });\n  }\n\n  toggleUserMenu() {\n    this.showUserMenu = !this.showUserMenu;\n  }\n\n  openSearch() {\n    this.router.navigate(['/search']);\n  }\n\n  // Get total count for display (cart + wishlist items for logged-in user)\n  getTotalItemCount(): number {\n    if (!this.currentUser) {\n      return 0; // Return 0 if user is not logged in\n    }\n    return this.totalItemCount || 0;\n  }\n\n  // Get formatted cart total amount\n  getFormattedCartTotal(): string {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(this.cartTotalAmount || 0);\n  }\n\n  // Check if cart total price should be displayed\n  shouldShowCartTotalPrice(): boolean {\n    return this.currentUser !== null && this.showCartTotalPrice;\n  }\n\n  onSearch() {\n    if (this.searchQuery.trim()) {\n      this.router.navigate(['/search'], {\n        queryParams: { q: this.searchQuery }\n      });\n      this.hideSuggestions();\n    } else {\n      this.router.navigate(['/search']);\n    }\n  }\n\n  onSearchInput() {\n    if (this.searchTimeout) {\n      clearTimeout(this.searchTimeout);\n    }\n\n    this.searchTimeout = setTimeout(() => {\n      if (this.searchQuery.trim().length > 2) {\n        this.loadSearchSuggestions();\n      } else {\n        this.hideSuggestions();\n      }\n    }, 300);\n  }\n\n  onSearchFocus() {\n    if (this.searchQuery.trim().length > 2) {\n      this.showSuggestions = true;\n    }\n  }\n\n  onSearchBlur() {\n    // Delay hiding to allow clicking on suggestions\n    setTimeout(() => {\n      this.hideSuggestions();\n    }, 200);\n  }\n\n  loadSearchSuggestions() {\n    // Simulate API call for search suggestions\n    const query = this.searchQuery.toLowerCase();\n\n    // Mock suggestions based on query\n    this.searchSuggestions = [\n      { text: `${this.searchQuery} in Products`, type: 'product', icon: 'fa-shopping-bag' },\n      { text: `${this.searchQuery} in Brands`, type: 'brand', icon: 'fa-tags' },\n      { text: `${this.searchQuery} in Categories`, type: 'category', icon: 'fa-list' }\n    ];\n\n    this.showSuggestions = true;\n  }\n\n  selectSuggestion(suggestion: any) {\n    this.searchQuery = suggestion.text;\n    this.router.navigate(['/search'], {\n      queryParams: {\n        q: this.searchQuery,\n        type: suggestion.type\n      }\n    });\n    this.hideSuggestions();\n  }\n\n  getSuggestionIcon(type: string): string {\n    switch (type) {\n      case 'product': return 'fa-shopping-bag';\n      case 'brand': return 'fa-tags';\n      case 'category': return 'fa-list';\n      default: return 'fa-search';\n    }\n  }\n\n  hideSuggestions() {\n    this.showSuggestions = false;\n    this.searchSuggestions = [];\n  }\n\n  logout() {\n    this.authService.logout();\n    this.showUserMenu = false;\n  }\n}\n", "<header class=\"header\">\n  <div class=\"container\">\n    <div class=\"header-content\">\n      <!-- Logo -->\n      <div class=\"logo\">\n        <a routerLink=\"/home\">\n          <h1 class=\"gradient-text\">DFashion</h1>\n        </a>\n      </div>\n\n      <!-- Global Search Bar -->\n      <div class=\"search-bar\">\n        <i class=\"fas fa-search\"></i>\n        <input\n          type=\"text\"\n          placeholder=\"Search products, brands, categories...\"\n          [(ngModel)]=\"searchQuery\"\n          (keyup.enter)=\"onSearch()\"\n          (input)=\"onSearchInput()\"\n          (focus)=\"onSearchFocus()\"\n          (blur)=\"onSearchBlur()\"\n        >\n        <!-- Search Suggestions Dropdown -->\n        <div class=\"search-suggestions\" *ngIf=\"showSuggestions && searchSuggestions.length > 0\">\n          <div\n            *ngFor=\"let suggestion of searchSuggestions\"\n            class=\"suggestion-item\"\n            (click)=\"selectSuggestion(suggestion)\">\n            <i class=\"fas\" [ngClass]=\"getSuggestionIcon(suggestion.type)\"></i>\n            <span class=\"suggestion-text\">{{ suggestion.text }}</span>\n            <span class=\"suggestion-type\">{{ suggestion.type }}</span>\n          </div>\n        </div>\n      </div>\n\n      <!-- Navigation -->\n      <nav class=\"nav-menu\">\n        <a routerLink=\"/home\" routerLinkActive=\"active\" class=\"nav-item\">\n          <i class=\"fas fa-home\"></i>\n          <span>Home</span>\n        </a>\n        <a routerLink=\"/explore\" routerLinkActive=\"active\" class=\"nav-item\">\n          <i class=\"fas fa-compass\"></i>\n          <span>Explore</span>\n        </a>\n        <a routerLink=\"/shop\" routerLinkActive=\"active\" class=\"nav-item\">\n          <i class=\"fas fa-shopping-bag\"></i>\n          <span>Shop</span>\n        </a>\n        <!-- Wishlist Icon with Count -->\n        <a routerLink=\"/wishlist\" routerLinkActive=\"active\" class=\"nav-item wishlist-item\">\n          <i class=\"fas fa-heart\"></i>\n          <span>Wishlist</span>\n          <span class=\"wishlist-badge\" *ngIf=\"wishlistItemCount > 0\">{{ wishlistItemCount }}</span>\n          <span class=\"wishlist-badge zero\" *ngIf=\"currentUser && wishlistItemCount === 0\">0</span>\n        </a>\n\n        <!-- Cart Icon with Count and Total Amount -->\n        <a routerLink=\"/cart\" routerLinkActive=\"active\" class=\"nav-item cart-item\">\n          <i class=\"fas fa-shopping-cart\"></i>\n          <span>Cart</span>\n          <span class=\"cart-badge\" *ngIf=\"cartItemCount > 0\">{{ cartItemCount }}</span>\n          <span class=\"cart-badge zero\" *ngIf=\"currentUser && cartItemCount === 0\">0</span>\n\n          <!-- Cart total amount display -->\n          <div class=\"cart-total-display\" *ngIf=\"currentUser && cartTotalAmount > 0\">\n            <span class=\"cart-total-text\">{{ getFormattedCartTotal() }}</span>\n          </div>\n        </a>\n\n        <!-- Total Count Display (Cart + Wishlist Combined) -->\n        <div class=\"total-count-item\" *ngIf=\"currentUser\">\n          <i class=\"fas fa-shopping-bag\"></i>\n          <span>Total</span>\n          <span class=\"total-count-badge\" *ngIf=\"getTotalItemCount() > 0\">{{ getTotalItemCount() }}</span>\n          <span class=\"total-count-badge zero\" *ngIf=\"getTotalItemCount() === 0\">0</span>\n        </div>\n\n        <!-- User Menu for logged in users -->\n        <div *ngIf=\"currentUser\" class=\"user-menu\" (click)=\"toggleUserMenu()\">\n          <img [src]=\"currentUser.avatar || 'assets/images/default-avatar.svg'\" [alt]=\"currentUser.fullName\" class=\"user-avatar\" (error)=\"onAvatarError($event)\">\n          <span class=\"username\">{{ currentUser.username }}</span>\n          <i class=\"fas fa-chevron-down\"></i>\n          \n          <!-- Dropdown Menu -->\n          <div class=\"dropdown-menu\" [class.show]=\"showUserMenu\">\n            <a routerLink=\"/profile\" class=\"dropdown-item\">\n              <i class=\"fas fa-user\"></i>\n              Profile\n            </a>\n            <a routerLink=\"/settings\" class=\"dropdown-item\">\n              <i class=\"fas fa-cog\"></i>\n              Settings\n            </a>\n            <div class=\"dropdown-divider\"></div>\n            <a *ngIf=\"currentUser.role === 'vendor'\" routerLink=\"/vendor/dashboard\" class=\"dropdown-item\">\n              <i class=\"fas fa-store\"></i>\n              Vendor Dashboard\n            </a>\n            <a *ngIf=\"currentUser.role === 'admin'\" routerLink=\"/admin\" class=\"dropdown-item\">\n              <i class=\"fas fa-shield-alt\"></i>\n              Admin Panel\n            </a>\n            <div class=\"dropdown-divider\" *ngIf=\"currentUser.role !== 'customer'\"></div>\n            <button (click)=\"logout()\" class=\"dropdown-item logout\">\n              <i class=\"fas fa-sign-out-alt\"></i>\n              Logout\n            </button>\n          </div>\n        </div>\n\n        <!-- Login/Register for guest users -->\n        <div *ngIf=\"!currentUser\" class=\"auth-buttons\">\n          <a routerLink=\"/auth/login\" class=\"btn btn-outline\">Login</a>\n          <a routerLink=\"/auth/register\" class=\"btn btn-primary\">Sign Up</a>\n        </div>\n      </nav>\n    </div>\n  </div>\n</header>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAgB,iBAAiB;AACtD,SAASC,WAAW,QAAQ,gBAAgB;;;;;;;;;;;ICqBlCC,EAAA,CAAAC,cAAA,cAGyC;IAAvCD,EAAA,CAAAE,UAAA,mBAAAC,2DAAA;MAAA,MAAAC,aAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAG,gBAAA,CAAAP,aAAA,CAA4B;IAAA,EAAC;IACtCJ,EAAA,CAAAY,SAAA,YAAkE;IAClEZ,EAAA,CAAAC,cAAA,eAA8B;IAAAD,EAAA,CAAAa,MAAA,GAAqB;IAAAb,EAAA,CAAAc,YAAA,EAAO;IAC1Dd,EAAA,CAAAC,cAAA,eAA8B;IAAAD,EAAA,CAAAa,MAAA,GAAqB;IACrDb,EADqD,CAAAc,YAAA,EAAO,EACtD;;;;;IAHWd,EAAA,CAAAe,SAAA,EAA8C;IAA9Cf,EAAA,CAAAgB,UAAA,YAAAR,MAAA,CAAAS,iBAAA,CAAAb,aAAA,CAAAc,IAAA,EAA8C;IAC/BlB,EAAA,CAAAe,SAAA,GAAqB;IAArBf,EAAA,CAAAmB,iBAAA,CAAAf,aAAA,CAAAgB,IAAA,CAAqB;IACrBpB,EAAA,CAAAe,SAAA,GAAqB;IAArBf,EAAA,CAAAmB,iBAAA,CAAAf,aAAA,CAAAc,IAAA,CAAqB;;;;;IAPvDlB,EAAA,CAAAC,cAAA,cAAwF;IACtFD,EAAA,CAAAqB,UAAA,IAAAC,qCAAA,kBAGyC;IAK3CtB,EAAA,CAAAc,YAAA,EAAM;;;;IAPqBd,EAAA,CAAAe,SAAA,EAAoB;IAApBf,EAAA,CAAAgB,UAAA,YAAAR,MAAA,CAAAe,iBAAA,CAAoB;;;;;IA4B7CvB,EAAA,CAAAC,cAAA,eAA2D;IAAAD,EAAA,CAAAa,MAAA,GAAuB;IAAAb,EAAA,CAAAc,YAAA,EAAO;;;;IAA9Bd,EAAA,CAAAe,SAAA,EAAuB;IAAvBf,EAAA,CAAAmB,iBAAA,CAAAX,MAAA,CAAAgB,iBAAA,CAAuB;;;;;IAClFxB,EAAA,CAAAC,cAAA,eAAiF;IAAAD,EAAA,CAAAa,MAAA,QAAC;IAAAb,EAAA,CAAAc,YAAA,EAAO;;;;;IAOzFd,EAAA,CAAAC,cAAA,eAAmD;IAAAD,EAAA,CAAAa,MAAA,GAAmB;IAAAb,EAAA,CAAAc,YAAA,EAAO;;;;IAA1Bd,EAAA,CAAAe,SAAA,EAAmB;IAAnBf,EAAA,CAAAmB,iBAAA,CAAAX,MAAA,CAAAiB,aAAA,CAAmB;;;;;IACtEzB,EAAA,CAAAC,cAAA,eAAyE;IAAAD,EAAA,CAAAa,MAAA,QAAC;IAAAb,EAAA,CAAAc,YAAA,EAAO;;;;;IAI/Ed,EADF,CAAAC,cAAA,cAA2E,eAC3C;IAAAD,EAAA,CAAAa,MAAA,GAA6B;IAC7Db,EAD6D,CAAAc,YAAA,EAAO,EAC9D;;;;IAD0Bd,EAAA,CAAAe,SAAA,GAA6B;IAA7Bf,EAAA,CAAAmB,iBAAA,CAAAX,MAAA,CAAAkB,qBAAA,GAA6B;;;;;IAQ7D1B,EAAA,CAAAC,cAAA,eAAgE;IAAAD,EAAA,CAAAa,MAAA,GAAyB;IAAAb,EAAA,CAAAc,YAAA,EAAO;;;;IAAhCd,EAAA,CAAAe,SAAA,EAAyB;IAAzBf,EAAA,CAAAmB,iBAAA,CAAAX,MAAA,CAAAmB,iBAAA,GAAyB;;;;;IACzF3B,EAAA,CAAAC,cAAA,eAAuE;IAAAD,EAAA,CAAAa,MAAA,QAAC;IAAAb,EAAA,CAAAc,YAAA,EAAO;;;;;IAJjFd,EAAA,CAAAC,cAAA,cAAkD;IAChDD,EAAA,CAAAY,SAAA,YAAmC;IACnCZ,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAa,MAAA,YAAK;IAAAb,EAAA,CAAAc,YAAA,EAAO;IAElBd,EADA,CAAAqB,UAAA,IAAAO,sCAAA,mBAAgE,IAAAC,sCAAA,mBACO;IACzE7B,EAAA,CAAAc,YAAA,EAAM;;;;IAF6Bd,EAAA,CAAAe,SAAA,GAA6B;IAA7Bf,EAAA,CAAAgB,UAAA,SAAAR,MAAA,CAAAmB,iBAAA,OAA6B;IACxB3B,EAAA,CAAAe,SAAA,EAA+B;IAA/Bf,EAAA,CAAAgB,UAAA,SAAAR,MAAA,CAAAmB,iBAAA,SAA+B;;;;;IAoBnE3B,EAAA,CAAAC,cAAA,YAA8F;IAC5FD,EAAA,CAAAY,SAAA,YAA4B;IAC5BZ,EAAA,CAAAa,MAAA,yBACF;IAAAb,EAAA,CAAAc,YAAA,EAAI;;;;;IACJd,EAAA,CAAAC,cAAA,YAAkF;IAChFD,EAAA,CAAAY,SAAA,YAAiC;IACjCZ,EAAA,CAAAa,MAAA,oBACF;IAAAb,EAAA,CAAAc,YAAA,EAAI;;;;;IACJd,EAAA,CAAAY,SAAA,cAA4E;;;;;;IAxBhFZ,EAAA,CAAAC,cAAA,cAAsE;IAA3BD,EAAA,CAAAE,UAAA,mBAAA4B,qDAAA;MAAA9B,EAAA,CAAAK,aAAA,CAAA0B,GAAA;MAAA,MAAAvB,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAwB,cAAA,EAAgB;IAAA,EAAC;IACnEhC,EAAA,CAAAC,cAAA,cAAuJ;IAAhCD,EAAA,CAAAE,UAAA,mBAAA+B,qDAAAC,MAAA;MAAAlC,EAAA,CAAAK,aAAA,CAAA0B,GAAA;MAAA,MAAAvB,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAA2B,aAAA,CAAAD,MAAA,CAAqB;IAAA,EAAC;IAAtJlC,EAAA,CAAAc,YAAA,EAAuJ;IACvJd,EAAA,CAAAC,cAAA,eAAuB;IAAAD,EAAA,CAAAa,MAAA,GAA0B;IAAAb,EAAA,CAAAc,YAAA,EAAO;IACxDd,EAAA,CAAAY,SAAA,YAAmC;IAIjCZ,EADF,CAAAC,cAAA,cAAuD,YACN;IAC7CD,EAAA,CAAAY,SAAA,YAA2B;IAC3BZ,EAAA,CAAAa,MAAA,gBACF;IAAAb,EAAA,CAAAc,YAAA,EAAI;IACJd,EAAA,CAAAC,cAAA,YAAgD;IAC9CD,EAAA,CAAAY,SAAA,aAA0B;IAC1BZ,EAAA,CAAAa,MAAA,kBACF;IAAAb,EAAA,CAAAc,YAAA,EAAI;IACJd,EAAA,CAAAY,SAAA,eAAoC;IASpCZ,EARA,CAAAqB,UAAA,KAAAe,oCAAA,gBAA8F,KAAAC,oCAAA,gBAIZ,KAAAC,sCAAA,kBAIZ;IACtEtC,EAAA,CAAAC,cAAA,kBAAwD;IAAhDD,EAAA,CAAAE,UAAA,mBAAAqC,yDAAA;MAAAvC,EAAA,CAAAK,aAAA,CAAA0B,GAAA;MAAA,MAAAvB,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAgC,MAAA,EAAQ;IAAA,EAAC;IACxBxC,EAAA,CAAAY,SAAA,aAAmC;IACnCZ,EAAA,CAAAa,MAAA,gBACF;IAEJb,EAFI,CAAAc,YAAA,EAAS,EACL,EACF;;;;IA7BCd,EAAA,CAAAe,SAAA,EAAgE;IAACf,EAAjE,CAAAgB,UAAA,QAAAR,MAAA,CAAAiC,WAAA,CAAAC,MAAA,wCAAA1C,EAAA,CAAA2C,aAAA,CAAgE,QAAAnC,MAAA,CAAAiC,WAAA,CAAAG,QAAA,CAA6B;IAC3E5C,EAAA,CAAAe,SAAA,GAA0B;IAA1Bf,EAAA,CAAAmB,iBAAA,CAAAX,MAAA,CAAAiC,WAAA,CAAAI,QAAA,CAA0B;IAItB7C,EAAA,CAAAe,SAAA,GAA2B;IAA3Bf,EAAA,CAAA8C,WAAA,SAAAtC,MAAA,CAAAuC,YAAA,CAA2B;IAUhD/C,EAAA,CAAAe,SAAA,GAAmC;IAAnCf,EAAA,CAAAgB,UAAA,SAAAR,MAAA,CAAAiC,WAAA,CAAAO,IAAA,cAAmC;IAInChD,EAAA,CAAAe,SAAA,EAAkC;IAAlCf,EAAA,CAAAgB,UAAA,SAAAR,MAAA,CAAAiC,WAAA,CAAAO,IAAA,aAAkC;IAIPhD,EAAA,CAAAe,SAAA,EAAqC;IAArCf,EAAA,CAAAgB,UAAA,SAAAR,MAAA,CAAAiC,WAAA,CAAAO,IAAA,gBAAqC;;;;;IAUtEhD,EADF,CAAAC,cAAA,cAA+C,YACO;IAAAD,EAAA,CAAAa,MAAA,YAAK;IAAAb,EAAA,CAAAc,YAAA,EAAI;IAC7Dd,EAAA,CAAAC,cAAA,YAAuD;IAAAD,EAAA,CAAAa,MAAA,cAAO;IAChEb,EADgE,CAAAc,YAAA,EAAI,EAC9D;;;AD4Ud,OAAM,MAAOmC,eAAe;EAe1BC,YACUC,WAAwB,EACxBC,WAAwB,EACxBC,eAAmC,EACnCC,MAAc;IAHd,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,MAAM,GAANA,MAAM;IAlBhB,KAAAb,WAAW,GAAgB,IAAI;IAC/B,KAAAc,WAAW,GAAG,EAAE;IAChB,KAAAR,YAAY,GAAG,KAAK;IACpB,KAAAtB,aAAa,GAAG,CAAC;IACjB,KAAAD,iBAAiB,GAAG,CAAC;IACrB,KAAAgC,cAAc,GAAG,CAAC;IAClB,KAAAC,eAAe,GAAG,CAAC;IACnB,KAAAC,kBAAkB,GAAG,KAAK;IAE1B;IACA,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAApC,iBAAiB,GAAU,EAAE;EAQ1B;EAEHqC,QAAQA,CAAA;IACN;IACA,IAAI,CAACT,WAAW,CAACU,YAAY,CAACC,SAAS,CAACC,IAAI,IAAG;MAC7C,MAAMC,YAAY,GAAG,CAAC,IAAI,CAACvB,WAAW;MACtC,IAAI,CAACA,WAAW,GAAGsB,IAAI;MAEvB;MACA,IAAIA,IAAI,IAAIC,YAAY,EAAE;QACxBC,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;QAC3DC,UAAU,CAAC,MAAK;UACd,IAAI,CAACf,WAAW,CAACgB,iBAAiB,EAAE;QACtC,CAAC,EAAE,GAAG,CAAC;OACR,MAAM,IAAI,CAACL,IAAI,IAAI,CAACC,YAAY,EAAE;QACjC;QACAC,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;QAC3D,IAAI,CAACV,cAAc,GAAG,CAAC;;IAE3B,CAAC,CAAC;IAEF;IACA,IAAI,CAACJ,WAAW,CAACiB,cAAc,CAACP,SAAS,CAAEQ,KAAa,IAAI;MAC1D,IAAI,CAAC7C,aAAa,GAAG6C,KAAK;MAC1BL,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEI,KAAK,CAAC;IACrD,CAAC,CAAC;IAEF;IACA,IAAI,CAACjB,eAAe,CAACkB,kBAAkB,CAACT,SAAS,CAAEQ,KAAa,IAAI;MAClE,IAAI,CAAC9C,iBAAiB,GAAG8C,KAAK;MAC9BL,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEI,KAAK,CAAC;IACzD,CAAC,CAAC;IAEF;IACA,IAAI,CAAClB,WAAW,CAACoB,eAAe,CAACV,SAAS,CAAEQ,KAAa,IAAI;MAC3D,IAAI,CAACd,cAAc,GAAGc,KAAK;MAC3BL,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEI,KAAK,CAAC;IACtD,CAAC,CAAC;IAEF;IACA,IAAI,CAAClB,WAAW,CAACqB,gBAAgB,CAACX,SAAS,CAAEY,MAAc,IAAI;MAC7D,IAAI,CAACjB,eAAe,GAAGiB,MAAM;MAC7BT,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEQ,MAAM,CAAC;IAC7D,CAAC,CAAC;IAEF;IACA,IAAI,CAACtB,WAAW,CAACuB,mBAAmB,CAACb,SAAS,CAAEc,SAAkB,IAAI;MACpE,IAAI,CAAClB,kBAAkB,GAAGkB,SAAS;MACnCX,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEU,SAAS,CAAC;IACpE,CAAC,CAAC;IAEF;IACA,IAAI,IAAI,CAACnC,WAAW,EAAE;MACpB,IAAI,CAACW,WAAW,CAACgB,iBAAiB,EAAE;MACpC,IAAI,CAACf,eAAe,CAACwB,sBAAsB,EAAE;;IAG/C;IACA,IAAI,CAACzB,WAAW,CAAC0B,QAAQ,EAAE;IAC3B,IAAI,CAACzB,eAAe,CAAC0B,YAAY,EAAE;IAEnC;IACAC,QAAQ,CAACC,gBAAgB,CAAC,OAAO,EAAGC,KAAK,IAAI;MAC3C,MAAMC,MAAM,GAAGD,KAAK,CAACC,MAAqB;MAC1C,IAAI,CAACA,MAAM,CAACC,OAAO,CAAC,YAAY,CAAC,EAAE;QACjC,IAAI,CAACrC,YAAY,GAAG,KAAK;;IAE7B,CAAC,CAAC;EACJ;EAEAf,cAAcA,CAAA;IACZ,IAAI,CAACe,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;EACxC;EAEAsC,UAAUA,CAAA;IACR,IAAI,CAAC/B,MAAM,CAACgC,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;EACnC;EAEA;EACA3D,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAACc,WAAW,EAAE;MACrB,OAAO,CAAC,CAAC,CAAC;;IAEZ,OAAO,IAAI,CAACe,cAAc,IAAI,CAAC;EACjC;EAEA;EACA9B,qBAAqBA,CAAA;IACnB,OAAO,IAAI6D,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;KACX,CAAC,CAACC,MAAM,CAAC,IAAI,CAAClC,eAAe,IAAI,CAAC,CAAC;EACtC;EAEA;EACAmC,wBAAwBA,CAAA;IACtB,OAAO,IAAI,CAACnD,WAAW,KAAK,IAAI,IAAI,IAAI,CAACiB,kBAAkB;EAC7D;EAEAmC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACtC,WAAW,CAACuC,IAAI,EAAE,EAAE;MAC3B,IAAI,CAACxC,MAAM,CAACgC,QAAQ,CAAC,CAAC,SAAS,CAAC,EAAE;QAChCS,WAAW,EAAE;UAAEC,CAAC,EAAE,IAAI,CAACzC;QAAW;OACnC,CAAC;MACF,IAAI,CAAC0C,eAAe,EAAE;KACvB,MAAM;MACL,IAAI,CAAC3C,MAAM,CAACgC,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;;EAErC;EAEAY,aAAaA,CAAA;IACX,IAAI,IAAI,CAACC,aAAa,EAAE;MACtBC,YAAY,CAAC,IAAI,CAACD,aAAa,CAAC;;IAGlC,IAAI,CAACA,aAAa,GAAGhC,UAAU,CAAC,MAAK;MACnC,IAAI,IAAI,CAACZ,WAAW,CAACuC,IAAI,EAAE,CAACO,MAAM,GAAG,CAAC,EAAE;QACtC,IAAI,CAACC,qBAAqB,EAAE;OAC7B,MAAM;QACL,IAAI,CAACL,eAAe,EAAE;;IAE1B,CAAC,EAAE,GAAG,CAAC;EACT;EAEAM,aAAaA,CAAA;IACX,IAAI,IAAI,CAAChD,WAAW,CAACuC,IAAI,EAAE,CAACO,MAAM,GAAG,CAAC,EAAE;MACtC,IAAI,CAAC1C,eAAe,GAAG,IAAI;;EAE/B;EAEA6C,YAAYA,CAAA;IACV;IACArC,UAAU,CAAC,MAAK;MACd,IAAI,CAAC8B,eAAe,EAAE;IACxB,CAAC,EAAE,GAAG,CAAC;EACT;EAEAK,qBAAqBA,CAAA;IACnB;IACA,MAAMG,KAAK,GAAG,IAAI,CAAClD,WAAW,CAACmD,WAAW,EAAE;IAE5C;IACA,IAAI,CAACnF,iBAAiB,GAAG,CACvB;MAAEH,IAAI,EAAE,GAAG,IAAI,CAACmC,WAAW,cAAc;MAAErC,IAAI,EAAE,SAAS;MAAEyF,IAAI,EAAE;IAAiB,CAAE,EACrF;MAAEvF,IAAI,EAAE,GAAG,IAAI,CAACmC,WAAW,YAAY;MAAErC,IAAI,EAAE,OAAO;MAAEyF,IAAI,EAAE;IAAS,CAAE,EACzE;MAAEvF,IAAI,EAAE,GAAG,IAAI,CAACmC,WAAW,gBAAgB;MAAErC,IAAI,EAAE,UAAU;MAAEyF,IAAI,EAAE;IAAS,CAAE,CACjF;IAED,IAAI,CAAChD,eAAe,GAAG,IAAI;EAC7B;EAEAhD,gBAAgBA,CAACiG,UAAe;IAC9B,IAAI,CAACrD,WAAW,GAAGqD,UAAU,CAACxF,IAAI;IAClC,IAAI,CAACkC,MAAM,CAACgC,QAAQ,CAAC,CAAC,SAAS,CAAC,EAAE;MAChCS,WAAW,EAAE;QACXC,CAAC,EAAE,IAAI,CAACzC,WAAW;QACnBrC,IAAI,EAAE0F,UAAU,CAAC1F;;KAEpB,CAAC;IACF,IAAI,CAAC+E,eAAe,EAAE;EACxB;EAEAhF,iBAAiBA,CAACC,IAAY;IAC5B,QAAQA,IAAI;MACV,KAAK,SAAS;QAAE,OAAO,iBAAiB;MACxC,KAAK,OAAO;QAAE,OAAO,SAAS;MAC9B,KAAK,UAAU;QAAE,OAAO,SAAS;MACjC;QAAS,OAAO,WAAW;;EAE/B;EAEA+E,eAAeA,CAAA;IACb,IAAI,CAACtC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACpC,iBAAiB,GAAG,EAAE;EAC7B;EAEAiB,MAAMA,CAAA;IACJ,IAAI,CAACW,WAAW,CAACX,MAAM,EAAE;IACzB,IAAI,CAACO,YAAY,GAAG,KAAK;EAC3B;;;uBAvMWE,eAAe,EAAAjD,EAAA,CAAA6G,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA/G,EAAA,CAAA6G,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAjH,EAAA,CAAA6G,iBAAA,CAAAK,EAAA,CAAAC,kBAAA,GAAAnH,EAAA,CAAA6G,iBAAA,CAAAO,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAfpE,eAAe;MAAAqE,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAxH,EAAA,CAAAyH,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCzblB/H,EANV,CAAAC,cAAA,gBAAuB,aACE,aACO,aAER,WACM,YACM;UAAAD,EAAA,CAAAa,MAAA,eAAQ;UAEtCb,EAFsC,CAAAc,YAAA,EAAK,EACrC,EACA;UAGNd,EAAA,CAAAC,cAAA,aAAwB;UACtBD,EAAA,CAAAY,SAAA,WAA6B;UAC7BZ,EAAA,CAAAC,cAAA,eAQC;UALCD,EAAA,CAAAiI,gBAAA,2BAAAC,wDAAAhG,MAAA;YAAAlC,EAAA,CAAAmI,kBAAA,CAAAH,GAAA,CAAAzE,WAAA,EAAArB,MAAA,MAAA8F,GAAA,CAAAzE,WAAA,GAAArB,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAyB;UAIzBlC,EAHA,CAAAE,UAAA,yBAAAkI,sDAAA;YAAA,OAAeJ,GAAA,CAAAnC,QAAA,EAAU;UAAA,EAAC,mBAAAwC,gDAAA;YAAA,OACjBL,GAAA,CAAA9B,aAAA,EAAe;UAAA,EAAC,mBAAAoC,gDAAA;YAAA,OAChBN,GAAA,CAAAzB,aAAA,EAAe;UAAA,EAAC,kBAAAgC,+CAAA;YAAA,OACjBP,GAAA,CAAAxB,YAAA,EAAc;UAAA,EAAC;UAPzBxG,EAAA,CAAAc,YAAA,EAQC;UAEDd,EAAA,CAAAqB,UAAA,KAAAmH,+BAAA,iBAAwF;UAU1FxI,EAAA,CAAAc,YAAA,EAAM;UAIJd,EADF,CAAAC,cAAA,eAAsB,aAC6C;UAC/DD,EAAA,CAAAY,SAAA,aAA2B;UAC3BZ,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAa,MAAA,YAAI;UACZb,EADY,CAAAc,YAAA,EAAO,EACf;UACJd,EAAA,CAAAC,cAAA,aAAoE;UAClED,EAAA,CAAAY,SAAA,aAA8B;UAC9BZ,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAa,MAAA,eAAO;UACfb,EADe,CAAAc,YAAA,EAAO,EAClB;UACJd,EAAA,CAAAC,cAAA,aAAiE;UAC/DD,EAAA,CAAAY,SAAA,aAAmC;UACnCZ,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAa,MAAA,YAAI;UACZb,EADY,CAAAc,YAAA,EAAO,EACf;UAEJd,EAAA,CAAAC,cAAA,aAAmF;UACjFD,EAAA,CAAAY,SAAA,aAA4B;UAC5BZ,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAa,MAAA,gBAAQ;UAAAb,EAAA,CAAAc,YAAA,EAAO;UAErBd,EADA,CAAAqB,UAAA,KAAAoH,gCAAA,mBAA2D,KAAAC,gCAAA,mBACsB;UACnF1I,EAAA,CAAAc,YAAA,EAAI;UAGJd,EAAA,CAAAC,cAAA,aAA2E;UACzED,EAAA,CAAAY,SAAA,aAAoC;UACpCZ,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAa,MAAA,YAAI;UAAAb,EAAA,CAAAc,YAAA,EAAO;UAKjBd,EAJA,CAAAqB,UAAA,KAAAsH,gCAAA,mBAAmD,KAAAC,gCAAA,mBACsB,KAAAC,+BAAA,kBAGE;UAG7E7I,EAAA,CAAAc,YAAA,EAAI;UA4CJd,EAzCA,CAAAqB,UAAA,KAAAyH,+BAAA,kBAAkD,KAAAC,+BAAA,mBAQoB,KAAAC,+BAAA,kBAiCvB;UAOvDhJ,EAHM,CAAAc,YAAA,EAAM,EACF,EACF,EACC;;;UAvGCd,EAAA,CAAAe,SAAA,GAAyB;UAAzBf,EAAA,CAAAiJ,gBAAA,YAAAjB,GAAA,CAAAzE,WAAA,CAAyB;UAOMvD,EAAA,CAAAe,SAAA,EAAqD;UAArDf,EAAA,CAAAgB,UAAA,SAAAgH,GAAA,CAAArE,eAAA,IAAAqE,GAAA,CAAAzG,iBAAA,CAAA8E,MAAA,KAAqD;UA8BtDrG,EAAA,CAAAe,SAAA,IAA2B;UAA3Bf,EAAA,CAAAgB,UAAA,SAAAgH,GAAA,CAAAxG,iBAAA,KAA2B;UACtBxB,EAAA,CAAAe,SAAA,EAA4C;UAA5Cf,EAAA,CAAAgB,UAAA,SAAAgH,GAAA,CAAAvF,WAAA,IAAAuF,GAAA,CAAAxG,iBAAA,OAA4C;UAOrDxB,EAAA,CAAAe,SAAA,GAAuB;UAAvBf,EAAA,CAAAgB,UAAA,SAAAgH,GAAA,CAAAvG,aAAA,KAAuB;UAClBzB,EAAA,CAAAe,SAAA,EAAwC;UAAxCf,EAAA,CAAAgB,UAAA,SAAAgH,GAAA,CAAAvF,WAAA,IAAAuF,GAAA,CAAAvG,aAAA,OAAwC;UAGtCzB,EAAA,CAAAe,SAAA,EAAwC;UAAxCf,EAAA,CAAAgB,UAAA,SAAAgH,GAAA,CAAAvF,WAAA,IAAAuF,GAAA,CAAAvE,eAAA,KAAwC;UAM5CzD,EAAA,CAAAe,SAAA,EAAiB;UAAjBf,EAAA,CAAAgB,UAAA,SAAAgH,GAAA,CAAAvF,WAAA,CAAiB;UAQ1CzC,EAAA,CAAAe,SAAA,EAAiB;UAAjBf,EAAA,CAAAgB,UAAA,SAAAgH,GAAA,CAAAvF,WAAA,CAAiB;UAiCjBzC,EAAA,CAAAe,SAAA,EAAkB;UAAlBf,EAAA,CAAAgB,UAAA,UAAAgH,GAAA,CAAAvF,WAAA,CAAkB;;;qBDnGpB5C,YAAY,EAAAqJ,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EAAEvJ,YAAY,EAAAsH,EAAA,CAAAkC,UAAA,EAAAlC,EAAA,CAAAmC,gBAAA,EAAExJ,WAAW,EAAAyJ,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,eAAA,EAAAF,EAAA,CAAAG,OAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}